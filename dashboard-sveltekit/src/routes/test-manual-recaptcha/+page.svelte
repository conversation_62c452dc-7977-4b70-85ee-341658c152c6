<script lang="ts">
	import { onMount } from 'svelte';
	import { getSiteKey } from '$lib/config/recaptcha';
	
	let container: HTMLDivElement;
	let widgetId: number | null = null;
	let token = $state<string>('');
	
	const siteKey = getSiteKey();
	
	console.log('[Manual Test] Using siteKey:', siteKey.substring(0, 15) + '...');
	
	onMount(() => {
		// โหลด reCAPTCHA script
		const script = document.createElement('script');
		script.src = 'https://www.google.com/recaptcha/api.js';
		script.async = true;
		script.defer = true;
		
		script.onload = () => {
			console.log('[Manual Test] reCAPTCHA script loaded');
			
			// รอให้ grecaptcha พร้อม
			const checkGrecaptcha = () => {
				if ((window as any).grecaptcha && (window as any).grecaptcha.render) {
					console.log('[Manual Test] grecaptcha ready');
					createRecaptcha();
				} else {
					setTimeout(checkGrecaptcha, 100);
				}
			};
			checkGrecaptcha();
		};
		
		script.onerror = (error) => {
			console.error('[Manual Test] Failed to load reCAPTCHA script:', error);
		};
		
		document.head.appendChild(script);
	});
	
	function createRecaptcha() {
		if (!container) {
			console.error('[Manual Test] Container not found');
			return;
		}
		
		console.log('[Manual Test] Creating reCAPTCHA with siteKey:', siteKey);
		
		try {
			widgetId = (window as any).grecaptcha.render(container, {
				sitekey: siteKey,
				theme: 'light',
				size: 'normal',
				callback: (receivedToken: string) => {
					console.log('[Manual Test] Token received:', receivedToken.substring(0, 50) + '...');
					token = receivedToken;
				},
				'expired-callback': () => {
					console.log('[Manual Test] Token expired');
					token = '';
				},
				'error-callback': () => {
					console.error('[Manual Test] reCAPTCHA error');
					token = '';
				}
			});
			
			console.log('[Manual Test] Widget created with ID:', widgetId);
		} catch (error) {
			console.error('[Manual Test] Error creating reCAPTCHA:', error);
		}
	}
	
	function resetRecaptcha() {
		if (widgetId !== null && (window as any).grecaptcha) {
			(window as any).grecaptcha.reset(widgetId);
			token = '';
		}
	}
</script>

<svelte:head>
	<title>Manual reCAPTCHA Test</title>
</svelte:head>

<div class="container mx-auto p-8 max-w-md">
	<h1 class="text-2xl font-bold mb-6">Manual reCAPTCHA Test</h1>
	
	<div class="space-y-6">
		<div class="card bg-base-100 shadow-xl">
			<div class="card-body">
				<h2 class="card-title">Configuration</h2>
				<div class="text-sm space-y-2">
					<p><strong>Site Key:</strong> {siteKey.substring(0, 15)}...</p>
					<p><strong>Environment:</strong> {import.meta.env.MODE}</p>
					<p><strong>Dev Mode:</strong> {import.meta.env.DEV ? 'Yes' : 'No'}</p>
				</div>
			</div>
		</div>

		<div class="card bg-base-100 shadow-xl">
			<div class="card-body">
				<h2 class="card-title">reCAPTCHA Widget</h2>
				<div bind:this={container} class="my-4"></div>
				
				{#if token}
					<div class="alert alert-success">
						<span>✅ Token received!</span>
						<div class="text-xs mt-2">
							{token.substring(0, 50)}...
						</div>
					</div>
				{:else}
					<div class="alert alert-info">
						<span>Please complete the reCAPTCHA</span>
					</div>
				{/if}
				
				<div class="card-actions justify-end mt-4">
					<button class="btn btn-secondary btn-sm" onclick={resetRecaptcha}>
						Reset
					</button>
				</div>
			</div>
		</div>
		
		<div class="card bg-base-100 shadow-xl">
			<div class="card-body">
				<h2 class="card-title">Debug Info</h2>
				<div class="text-xs space-y-1">
					<p><strong>Widget ID:</strong> {widgetId ?? 'Not created'}</p>
					<p><strong>Token Length:</strong> {token.length}</p>
					<p><strong>Container:</strong> {container ? 'Found' : 'Not found'}</p>
				</div>
			</div>
		</div>
	</div>
</div>
