<script lang="ts">
	import Icon from '@iconify/svelte';
	import { onMount } from 'svelte';
	import { t } from 'svelte-i18n';
	import { enhance } from '$app/forms';
	import { goto } from '$app/navigation';
	import SEO from '$lib/components/layout/SEO.svelte';
	import {Button, Card, Input, Recaptcha} from '$lib/components/ui'; 
	import { type SigninData } from '$lib/schemas/auth.schema';
	import { authStore, type User } from '$lib/stores/auth.svelte';
	import { ErrorHandler } from '$lib/utils/error-handler';
	import { LogCategory, logger } from '$lib/utils/logger';

	import {
		showLoading,
		showLoginError,
		showToast,
		showValidationError,
	} from '$lib/utils/sweetalert'; 

	const { form } = $props<{
		form?: any;
	}>();

	// สร้าง form data
	const initialData: SigninData = {
		email: '',
		password: '',
		rememberMe: false,
	};

	// Form state variables
	let emailValue = $state(form?.email || initialData.email);
	let passwordValue = $state(initialData.password); // ไม่เก็บรหัสผ่านจาก server
	let rememberMeValue = $state(form?.rememberMe || initialData.rememberMe);

	let isSubmitting = $state(false);
	let recaptchaToken = $state<string>('');
	let recaptchaComponent: Recaptcha;

	// Form action result
	const formResult = $derived(form);

	onMount(() => {
		// ถ้า login แล้วให้ redirect ไป dashboard
		if (authStore.isAuthenticated) {
			goto('/dashboard');
		}

		// Clear sensitive data from localStorage if exists
		if (typeof window !== 'undefined') {
			const oldToken = localStorage.getItem('auth_token');
			if (oldToken) {
				localStorage.removeItem('auth_token');
				logger.info(LogCategory.AUTH, 'old_token_cleared', 'Cleared old auth token on signin page');
			}
		}

		// Cleanup เมื่อ component ถูก destroy
		return () => {
			// No cleanup needed for server-side validation
		};
	});

	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Enter' && !isSubmitting) {
			// Remove form.requestSubmit() to prevent double submission
			// The form will submit naturally via use:enhance
		}
	}
</script>

<SEO title={$t('auth.signin')} />

<Card centered variant="default" shadow="lg" size="sm" class="space-y-6">
	<div class="text-center space-y-2">
		<h2 class="text-xl sm:text-2xl font-bold tracking-tight">
			{$t('auth.signin')}
		</h2>
		<p class="max-sm:text-sm">
			{$t('auth.signinDescription')}
		</p>
	</div>

	<!-- Form Action Messages -->
	{#if formResult?.success}
		<div class="alert alert-success">
			<Icon icon="mdi:check-circle" class="w-5 h-5" />
			<span>{formResult.message}</span>
		</div>
	{:else if formResult?.message}
		<div class="alert alert-error">
			<Icon icon="mdi:alert-circle" class="w-5 h-5" />
			<span>{formResult.message}</span>
		</div>
	{/if}

	<!-- Debug info (แสดงสถานะ validation) -->
	{#if form?.errors}
		<div class="text-xs text-gray-500 space-y-1 bg-base-200 p-3 rounded">
			<h3 class="font-semibold">Server Validation Errors:</h3>
			<div>Form Errors: {JSON.stringify(form.errors)}</div>
			<div>Email: {emailValue}</div>
			<div>Remember Me: {rememberMeValue}</div>
		</div>
	{/if}

	<!-- Login Form -->
	<form
		method="POST"
		action="?/signin"
		class="space-y-6"
		use:enhance={() => {
			return async ({ result }: any) => {
				console.log('🚀 Form submission started', {
					timestamp: new Date().toISOString(),
				});
				console.log('result', result);

				// Prevent multiple submissions
				if (isSubmitting) {
					console.log('⚠️ Form already submitting, ignoring duplicate submission');
					return;
				}

				isSubmitting = true;

				if (result.type === 'success' && result.data?.success) {
					// อัปเดต authStore ด้วยข้อมูลที่ได้จาก server
					if (result.data?.user) {
						authStore.updateUser(result.data.user as User);
						// Token จะถูกจัดการโดย cookie แล้ว ไม่ต้องใช้ localStorage
					}

					logger.info(LogCategory.AUTH, 'client_signin_success', 'Client-side signin success');

					// Redirect ไป dashboard หลังจาก login สำเร็จ
					goto('/dashboard');

					showToast('success', 'เข้าสู่ระบบสำเร็จ!', {
						timer: 1500,
						timerProgressBar: true,
						showCloseButton: false,
						allowEscapeKey: true,
					});
				} else if (result.type === 'failure' || result.data?.success === false) {
					const errorMessage = ErrorHandler.toUserMessage(
						result.data?.error || result.data?.message || 'เข้าสู่ระบบล้มเหลว'
					);

					logger.warn(LogCategory.AUTH, 'client_signin_failed', 'Client-side signin failed', {
						error: errorMessage,
					});

					// แสดง error message ทันที
					showLoginError(errorMessage, {
						timer: 3000,
						timerProgressBar: true,
						showCloseButton: false,
						allowEscapeKey: true,
					});
				}
				isSubmitting = false;
				console.log('✅ Form submission completed', {
					timestamp: new Date().toISOString(),
				});
			};
		}}
	>
		<Input
			id="email"
			name="email"
			type="email"
			bind:value={emailValue}
			label={$t('auth.email')}
			placeholder="<EMAIL>"
			icon="mdi:email"
			autocomplete="email"
			disabled={isSubmitting}
			onkeydown={handleKeydown}
			validate={form?.errors?.email}
			showRequired={true}
		/>

		<Input
			id="password"
			name="password"
			type="password"
			bind:value={passwordValue}
			label={$t('auth.password')}
			placeholder="••••••••"
			icon="mdi:lock"
			showPasswordToggle
			autocomplete="current-password"
			disabled={isSubmitting}
			onkeydown={handleKeydown}
			validate={form?.errors?.password}
			showRequired={true}
		/>

		<div class="flex items-center justify-between">
			<label for="rememberMe" class="label cursor-pointer">
				<input
					id="rememberMe"
					name="rememberMe"
					type="checkbox"
					bind:checked={rememberMeValue}
					class="checkbox checkbox-primary checkbox-sm"
					disabled={isSubmitting}
				/>
				<span class="label-text ml-2">{$t('auth.rememberMe')}</span>
			</label>

			<a
				href="/forgot-password"
				class="link link-primary text-sm"
				class:pointer-events-none={isSubmitting}
			>
				{$t('auth.forgotPassword')}
			</a>
		</div>

		{#if authStore.error}
			<div class="alert alert-error">
				<Icon icon="mdi:alert-circle" class="w-5 h-5" />
				<span>{authStore.error}</span>
			</div>
		{/if}

		<!-- <Recaptcha
			version="v2"
			bind:this={recaptchaComponent}
			callback={token => {
				console.log('reCAPTCHA token received:', token.substring(0, 50) + '...');
				recaptchaToken = token;
			}}
			expiredCallback={() => {
				console.log('reCAPTCHA expired');
				recaptchaToken = '';
			}}
			errorCallback={() => {
				console.log('reCAPTCHA error');
				recaptchaToken = '';
			}}
		/> -->

		<!-- Hidden field สำหรับส่ง reCAPTCHA token -->
		<!-- <input type="hidden" name="recaptchaToken" value={recaptchaToken} /> -->

		<Button
			type="submit"
			color="primary"
			size="lg"
			block
			loading={isSubmitting || authStore.isLoading}
			disabled={isSubmitting || authStore.isLoading}
		>
			{isSubmitting ? $t('auth.signingIn') : $t('auth.signin')}
		</Button>

		<!-- {#if !recaptchaToken}
			<div class="text-center text-sm text-warning">
				<Icon icon="mdi:shield-alert" class="w-4 h-4 inline mr-1" />
				กรุณายืนยัน reCAPTCHA ก่อนเข้าสู่ระบบ
			</div>
		{/if} -->
	</form>

	<!-- Register Link -->
	<div class="text-center mt-6">
		<span class="text-base-content/60">{$t('auth.noAccount')} </span>
		<a href="/signup" class="link link-primary" class:pointer-events-none={isSubmitting}>
			{$t('auth.signup')}
		</a>
	</div>
</Card>
