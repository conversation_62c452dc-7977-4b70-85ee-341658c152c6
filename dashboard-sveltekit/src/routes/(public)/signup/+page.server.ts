import { fail, redirect } from '@sveltejs/kit';
import { validateSignupData } from '$lib/schemas/auth.schema';
import { authService } from '$lib/services/auth';
import { <PERSON>rror<PERSON>andler } from '$lib/utils/error-handler';
import { LogCategory, logger } from '$lib/utils/logger';
import type { Actions, PageServerLoad } from './$types';

export const prerender = false;

export const load: PageServerLoad = async ({ locals }) => {
	// ถ้ามี user อยู่แล้ว ให้ redirect ไป dashboard
	if (locals.user) {
		throw redirect(302, '/dashboard');
	}

	return {
		user: null,
	};
};

export const actions: Actions = {
	/**
	 * ✅ User Signup - Hybrid Approach
	 * Route API + Service Pattern
	 */
	signup: async ({ request, cookies, getClientAddress }) => {
		const clientIP = getClientAddress();
		const userAgent = request.headers.get('user-agent') || 'unknown';

		try {
			const formData = await request.formData();

			// Extract and validate form data at route level
			const signupData = {
				email: formData.get('email') as string,
				password: formData.get('password') as string,
				confirmPassword: formData.get('confirmPassword') as string,
			};
			const recaptchaToken = formData.get('recaptchaToken') as string;
			const recaptchaVersion = formData.get('recaptchaVersion') as string || 'v2';

			// ✅ ตรวจสอบ reCAPTCHA token ก่อน
			if (!recaptchaToken) {
				logger.warn(LogCategory.AUTH, 'signup_no_recaptcha', 'No reCAPTCHA token provided', {
					email: signupData.email?.substring(0, 3) + '***',
					clientIP,
					userAgent,
				});

				return fail(400, {
					message: 'กรุณายืนยัน reCAPTCHA',
					type: 'recaptcha_required',
				});
			}

			// ✅ ตรวจสอบ reCAPTCHA token กับ Google (Smart Verification - รองรับทั้ง v2 และ v3)
			try {
				const { verifyRecaptchaSmart } = await import('$lib/utils/recaptcha-verify');
				const recaptchaResult = await verifyRecaptchaSmart(
					recaptchaToken,
					clientIP,
					'signup', // action
					{
						v3MinScore: 0.5, // minimum score for v3
						strictActionCheck: true, // ตรวจสอบ action อย่างเข้มงวดสำหรับ v3
						expectedVersion: recaptchaVersion as 'v2' | 'v3' | undefined, // ใช้ version ที่ส่งมาจาก frontend
					}
				);

				if (!recaptchaResult.success) {
					let errorMessage = 'การตรวจสอบ reCAPTCHA ไม่สำเร็จ กรุณาลองใหม่อีกครั้ง';

					// Provide specific error messages based on failure reason
					switch (recaptchaResult.reason) {
						case 'low_score':
							errorMessage = `คะแนนความปลอดภัยต่ำเกินไป (${recaptchaResult.score?.toFixed(2)}) กรุณาลองใหม่อีกครั้ง`;
							break;
						case 'action_mismatch':
							errorMessage = 'การดำเนินการไม่ถูกต้อง กรุณาลองใหม่อีกครั้ง';
							break;
						case 'verification_failed':
							errorMessage = 'การตรวจสอบ reCAPTCHA ล้มเหลว กรุณายืนยัน reCAPTCHA อีกครั้ง';
							break;
						case 'no_token':
							errorMessage = 'กรุณายืนยัน reCAPTCHA ก่อนลงทะเบียน';
							break;
						case 'config_error':
							errorMessage = 'ระบบ reCAPTCHA ไม่พร้อมใช้งาน กรุณาลองใหม่ภายหลัง';
							break;
						default:
							errorMessage = 'การตรวจสอบ reCAPTCHA ไม่สำเร็จ กรุณาลองใหม่อีกครั้ง';
					}

					logger.warn(
						LogCategory.AUTH,
						'signup_recaptcha_failed',
						'reCAPTCHA verification failed',
						{
							email: signupData.email?.substring(0, 3) + '***',
							reason: recaptchaResult.reason,
							score: recaptchaResult.score,
							action: recaptchaResult.action,
							clientIP,
							userAgent,
						}
					);

					return fail(400, {
						message: errorMessage,
						type: 'recaptcha_failed',
					});
				}

				logger.info(
					LogCategory.AUTH,
					'signup_recaptcha_success',
					'reCAPTCHA verification successful',
					{
						email: signupData.email?.substring(0, 3) + '***',
						detectedVersion: recaptchaResult.detectedVersion,
						score: recaptchaResult.score,
						action: recaptchaResult.action,
						clientIP,
						userAgent,
					}
				);
			} catch (error) {
				logger.error(LogCategory.AUTH, 'signup_recaptcha_error', 'reCAPTCHA verification error', {
					error: error instanceof Error ? error.message : 'Unknown error',
					email: signupData.email?.substring(0, 3) + '***',
					clientIP,
					userAgent,
				});

				return fail(500, {
					message: 'เกิดข้อผิดพลาดในการตรวจสอบ reCAPTCHA',
					type: 'recaptcha_error',
				});
			}

			// Validate input using Zod
			const validation = validateSignupData(signupData);
			if (!validation.success) {
				return fail(400, {
					message: validation.error || 'ข้อมูลไม่ถูกต้อง',
					type: 'signup',
				});
			}

			// Call auth service for business logic + backend API
			const result = await authService.signup({
				email: signupData.email.trim(),
				password: signupData.password.trim(),
				confirmPassword: signupData.confirmPassword.trim(),
				recaptchaToken,
				agreeToTerms: true,
			});

			console.log('Signup result:', result);

			if (!result.success) {
				return fail(400, {
					message: result.error || result.message || 'ลงทะเบียนล้มเหลว',
					type: 'signup',
				});
			}

			// Set cookies for successful signup (if needed)
			if (result.data?.token) {
				const cookieOptions = {
					path: '/',
					httpOnly: true,
					secure: import.meta.env.NODE_ENV === 'production',
					sameSite: 'strict' as const,
					maxAge: 60 * 60 * 24 * 7, // 7 วัน
				};

				cookies.set('auth_token', result.data.token, cookieOptions);
			}

			return {
				success: true,
				user: result.data?.user,
				message: 'ลงทะเบียนสำเร็จ กรุณาตรวจสอบอีเมลเพื่อยืนยันบัญชี',
				type: 'signup',
			};
		} catch (error) {
			console.error('Signup action error:', error);
			return fail(500, {
				message: 'เกิดข้อผิดพลาดในการลงทะเบียน กรุณาลองใหม่อีกครั้ง',
				type: 'signup',
			});
		}
	},
};
