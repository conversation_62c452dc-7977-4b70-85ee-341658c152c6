<script lang="ts">
	// ทดสอบ environment variables และ reCAPTCHA config
	import { getSiteKey, debugRecaptchaConfig } from '$lib/config/recaptcha';
	
	// เรียก debug function
	debugRecaptchaConfig();
	
	const envInfo = {
		VITE_RECAPTCHA_SITE_KEY: import.meta.env.VITE_RECAPTCHA_SITE_KEY,
		VITE_RECAPTCHA_SECRET_KEY: import.meta.env.VITE_RECAPTCHA_SECRET_KEY,
		DEV: import.meta.env.DEV,
		PROD: import.meta.env.PROD,
		MODE: import.meta.env.MODE,
		finalSiteKey: getSiteKey()
	};
	
	console.log('Environment Info:', envInfo);
</script>

<div class="container mx-auto p-8">
	<h1 class="text-2xl font-bold mb-6">Environment Debug</h1>
	
	<div class="grid gap-4">
		{#each Object.entries(envInfo) as [key, value]}
			<div class="card bg-base-100 shadow">
				<div class="card-body p-4">
					<h3 class="font-semibold">{key}</h3>
					<p class="text-sm">
						{#if value}
							{#if key.includes('KEY')}
								<span class="text-success">✓ {value.substring(0, 15)}...</span>
							{:else}
								<span class="text-success">✓ {value}</span>
							{/if}
						{:else}
							<span class="text-error">✗ NOT SET</span>
						{/if}
					</p>
				</div>
			</div>
		{/each}
	</div>
	
	<div class="mt-8">
		<h2 class="text-xl font-bold mb-4">Debug Info</h2>
		<div class="card bg-base-100 shadow">
			<div class="card-body">
				<p>Check browser console for debug logs</p>
				<p class="text-sm text-gray-600 mt-2">
					Final Site Key: {getSiteKey().substring(0, 15)}...
				</p>
			</div>
		</div>
	</div>
</div>
