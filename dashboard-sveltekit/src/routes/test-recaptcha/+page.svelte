<script lang="ts">
	import Recaptcha from '$lib/components/ui/Recaptcha.svelte';
	
	let recaptchaToken = $state<string>('');
	let recaptchaComponent: Recaptcha;
	
	// Test siteKey - ใช้ test key ของ Google
	const testSiteKey = '6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI';
	const realSiteKey = '6LcRobIqAAAAAAnGAodZL6KoxsUzgyV-YDf2gbVy';
	
	console.log('Environment check:', {
		VITE_RECAPTCHA_SITE_KEY: import.meta.env.VITE_RECAPTCHA_SITE_KEY,
		testSiteKey,
		realSiteKey
	});
</script>

<div class="container mx-auto p-8 max-w-md">
	<h1 class="text-2xl font-bold mb-6">reCAPTCHA Test</h1>
	
	<div class="space-y-6">
		<div class="card bg-base-100 shadow-xl">
			<div class="card-body">
				<h2 class="card-title">Environment Variables</h2>
				<div class="text-sm space-y-2">
					<p>
						<strong>VITE_RECAPTCHA_SITE_KEY:</strong>
						{#if import.meta.env.VITE_RECAPTCHA_SITE_KEY}
							<span class="text-success">✓ SET</span>
							<span class="text-gray-500">({import.meta.env.VITE_RECAPTCHA_SITE_KEY.substring(0, 10)}...)</span>
						{:else}
							<span class="text-error">✗ NOT SET</span>
						{/if}
					</p>
				</div>
			</div>
		</div>

		<div class="card bg-base-100 shadow-xl">
			<div class="card-body">
				<h2 class="card-title">reCAPTCHA v2 Test (Real Key)</h2>
				<Recaptcha
					bind:this={recaptchaComponent}
					siteKey={realSiteKey}
					version="v2"
					action="test"
					callback={token => {
						console.log('reCAPTCHA token received:', token.substring(0, 50) + '...');
						recaptchaToken = token;
					}}
					expiredCallback={() => {
						console.log('reCAPTCHA expired');
						recaptchaToken = '';
					}}
					errorCallback={() => {
						console.log('reCAPTCHA error');
						recaptchaToken = '';
					}}
				/>
				
				{#if recaptchaToken}
					<div class="alert alert-success mt-4">
						<span>✓ reCAPTCHA verified successfully!</span>
						<div class="text-xs mt-2">
							Token: {recaptchaToken.substring(0, 50)}...
						</div>
					</div>
				{:else}
					<div class="alert alert-info mt-4">
						<span>Please complete the reCAPTCHA above</span>
					</div>
				{/if}
			</div>
		</div>

		<div class="card bg-base-100 shadow-xl">
			<div class="card-body">
				<h2 class="card-title">reCAPTCHA v2 Test (Test Key)</h2>
				<p class="text-sm text-gray-600 mb-4">Using Google's test key - should always pass</p>
				<Recaptcha
					siteKey={testSiteKey}
					version="v2"
					action="test"
					callback={token => {
						console.log('Test reCAPTCHA token received:', token.substring(0, 50) + '...');
					}}
					expiredCallback={() => {
						console.log('Test reCAPTCHA expired');
					}}
					errorCallback={() => {
						console.log('Test reCAPTCHA error');
					}}
				/>
			</div>
		</div>
	</div>
</div>
