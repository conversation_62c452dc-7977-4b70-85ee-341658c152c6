import type { LayoutServerLoad } from './$types';

export const load: LayoutServerLoad = async ({ locals, cookies, request }) => {
	console.log('Root Layout Server: locals.user:', locals.user);

	// รับภาษาจาก cookie หรือ header
	const savedLocale =
		cookies.get('locale') ||
		request.headers.get('accept-language')?.split(',')[0]?.split('-')[0] ||
		'th';

	// ส่งข้อมูลพื้นฐานจาก hooks.server.ts ไปยัง client
	return {
		user: locals.user || null,
		token: locals.token || null,
		initialLocale: savedLocale,
	};
};
