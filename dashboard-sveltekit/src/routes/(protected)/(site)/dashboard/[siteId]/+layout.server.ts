import { redirect } from '@sveltejs/kit';
import { siteService } from '$lib/services/site';
import type { LayoutServerLoad } from './$types';

export const load: LayoutServerLoad = async ({ locals, params }) => {
	const { siteId } = params;

	try {
		console.log('Layout Server: Fetching site data for siteId:', siteId);

		// ✅ ตรวจสอบ authentication - ถ้าไม่มี redirect ไปหน้า signin
		if (!locals.user || !locals.token) {
			console.log('Layout Server: No authentication found, redirecting to signin');
			throw redirect(302, '/signin');
		}

		// ✅ ใช้ site service เพื่อดึงข้อมูล site
		const result = await siteService.getSite(siteId, locals.token);
		console.log('Layout Server: Site service result:', result);

		if (!result.success) {
			console.error('Layout Server: Site service returned error:', result.error);

			// ✅ ถ้าเป็น error เรื่อง authentication ให้ redirect ไปหน้า signin
			if (
				result.error?.includes('token') ||
				result.error?.includes('unauthorized') ||
				result.error?.includes('authentication')
			) {
				console.log('Layout Server: Authentication error, redirecting to signin');
				throw redirect(302, '/signin');
			}

			// ✅ ถ้าเป็น error เรื่องไม่มีสิทธิ์เข้าถึง site หรือ site ไม่พบ ให้ redirect ไปหน้า dashboard หลัก
			if (
				result.error?.includes('ไม่พบ') ||
				result.error?.includes('not found') ||
				result.error?.includes('access denied')
			) {
				console.log('Layout Server: Site not found or access denied, redirecting to dashboard');
				throw redirect(302, '/dashboard');
			}

			// ✅ Error อื่นๆ ให้ redirect ไปหน้า dashboard หลัก
			console.log('Layout Server: Other error, redirecting to dashboard');
			throw redirect(302, '/dashboard');
		}

		// ✅ ตรวจสอบว่า user มีสิทธิ์เข้าถึง site นี้หรือไม่
		const site = result.data;
		if (!site) {
			console.log('Layout Server: No site data, redirecting to dashboard');
			throw redirect(302, '/dashboard');
		}

		// ✅ ตรวจสอบ ownership หรือ membership (ถ้าจำเป็น)
		// สามารถเพิ่มการตรวจสอบเพิ่มเติมได้ที่นี่

		console.log('Layout Server: Site data loaded successfully:', site.name);
		return {
			site,
			siteId,
			user: locals.user, // ✅ ส่งข้อมูล user ไปให้ client
			error: null,
		};
	} catch (error) {
		console.error('Layout Server: Load function error:', error);

		// ✅ ถ้าเป็น redirect ให้ throw ต่อไป
		if (error instanceof Response) {
			throw error;
		}

		// ✅ Error อื่นๆ ให้ redirect ไปหน้า dashboard หลัก
		console.log('Layout Server: Unexpected error, redirecting to dashboard');
		throw redirect(302, '/dashboard');
	}
};
