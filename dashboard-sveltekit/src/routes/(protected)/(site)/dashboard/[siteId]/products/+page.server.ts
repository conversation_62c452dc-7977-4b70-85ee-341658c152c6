import { error, fail, redirect } from '@sveltejs/kit';
import { categoryService } from '$lib/services/category';
import { productService } from '$lib/services/product';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ params, locals, url, setHeaders }) => {
	try {
		const siteId = params.siteId;
		if (!siteId) {
			throw error(400, 'Site ID is required');
		}

		// Get query parameters
		const page = parseInt(url.searchParams.get('page') || '1');
		const limit = parseInt(url.searchParams.get('limit') || '10');
		const search = url.searchParams.get('search') || '';
		const categoryId = url.searchParams.get('categoryId') || '';
		const status = url.searchParams.get('status') as 'active' | 'inactive' | undefined;
		const sortBy = url.searchParams.get('sortBy') || 'createdAt';
		const sortOrder = (url.searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc';

		// Fetch products and categories in parallel with error handling
		let productsResponse, categoriesResponse;

		try {
			productsResponse = await productService.getProducts(siteId, locals.token!, {
				page,
				limit,
				search,
				categoryId,
				status,
				sortBy,
				sortOrder,
			});
		} catch (err) {
			console.error('Error fetching products:', err);
			productsResponse = {
				success: false,
				error: 'Failed to fetch products',
				data: { products: [], total: 0, page: 1, limit: 10, totalPages: 0 },
			};
		}

		try {
			categoriesResponse = await categoryService.getCategories(siteId, locals.token!);
		} catch (err) {
			console.error('Error fetching categories:', err);
			categoriesResponse = {
				success: false,
				error: 'Failed to fetch categories',
				data: [],
			};
		}

		// Set cache headers
		setHeaders({
			'Cache-Control': 'public, max-age=300', // 5 minutes
		});

		return {
			products: productsResponse.success
				? productsResponse.data
				: {
						products: [],
						total: 0,
						page: 1,
						limit: 10,
						totalPages: 0,
					},
			categories: categoriesResponse.success ? categoriesResponse.data : [],
			pagination: {
				page: productsResponse.success ? productsResponse.data?.page || 1 : 1,
				limit: productsResponse.success ? productsResponse.data?.limit || 10 : 10,
				total: productsResponse.success ? productsResponse.data?.total || 0 : 0,
				totalPages: productsResponse.success ? productsResponse.data?.totalPages || 0 : 0,
			},
			filters: {
				search,
				categoryId,
				status,
				sortBy,
				sortOrder,
			},
			errors: {
				products: !productsResponse.success ? productsResponse.error : null,
				categories: !categoriesResponse.success ? categoriesResponse.error : null,
			},
		};
	} catch (err) {
		console.error('Error loading products page:', err);
		throw error(500, 'Failed to load products');
	}
};

export const actions: Actions = {
	/**
	 * ✅ Delete Product - Hybrid Approach
	 * Route API + Service Pattern
	 */
	deleteProduct: async ({ request, params, locals }) => {
		try {
			// Auth check already done in layout
			const { siteId } = params;
			const formData = await request.formData();
			const productId = formData.get('productId') as string;

			// Basic validation at route level
			if (!productId?.trim()) {
				return fail(400, {
					message: 'ไม่พบ ID สินค้า',
					type: 'delete',
				});
			}

			// Call service for business logic + backend API
			const response = await productService.deleteProduct(productId, siteId, locals.token!);

			if (!response.success) {
				return fail(400, {
					message: response.error || 'เกิดข้อผิดพลาดในการลบสินค้า',
					type: 'delete',
				});
			}

			return {
				success: true,
				message: 'ลบสินค้าสำเร็จ',
				type: 'delete',
			};
		} catch (error) {
			console.error('Delete product error:', error);
			return fail(500, {
				message: 'เกิดข้อผิดพลาดในการลบสินค้า',
				type: 'delete',
			});
		}
	},

	/**
	 * ✅ Update Product Stock - Hybrid Approach
	 * Route API + Service Pattern
	 */
	updateStock: async ({ request, params, locals }) => {
		try {
			// Auth check already done in layout
			const { siteId } = params;
			const formData = await request.formData();
			const productId = formData.get('productId') as string;
			const stockValue = formData.get('stock') as string;

			// Basic validation at route level
			if (!productId?.trim()) {
				return fail(400, {
					message: 'ไม่พบ ID สินค้า',
					type: 'stock',
				});
			}

			const stock = parseInt(stockValue);
			if (isNaN(stock) || stock < 0) {
				return fail(400, {
					message: 'จำนวนสต็อกต้องเป็นตัวเลขที่มากกว่าหรือเท่ากับ 0',
					type: 'stock',
				});
			}

			// Call service for business logic + backend API
			const response = await productService.updateProductStock(
				productId,
				{ stock },
				siteId,
				locals.token!
			);

			if (!response.success) {
				return fail(400, {
					message: response.error || 'เกิดข้อผิดพลาดในการอัปเดตสต็อก',
					type: 'stock',
				});
			}

			return {
				success: true,
				data: response.data,
				message: 'อัปเดตสต็อกสำเร็จ',
				type: 'stock',
			};
		} catch (error) {
			console.error('Update stock error:', error);
			return fail(500, {
				message: 'เกิดข้อผิดพลาดในการอัปเดตสต็อก',
				type: 'stock',
			});
		}
	},
};
