import { error, redirect } from '@sveltejs/kit';
import { categoryService } from '$lib/services/category';
import { productService } from '$lib/services/product';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ params, locals }) => {
	try {
		if (!locals.token || !locals.user) {
			throw redirect(302, '/signin');
		}

		const siteId = params.siteId;
		if (!siteId) {
			throw error(400, 'Site ID is required');
		}

		// Fetch categories for dropdown
		const categoriesResponse = await categoryService.getCategories(siteId, locals.token);

		if (!categoriesResponse.success) {
			throw error(500, categoriesResponse.error || 'Failed to fetch categories');
		}

		return {
			categories: categoriesResponse.data || [],
		};
	} catch (err) {
		console.error('Error loading create product page:', err);
		if (err instanceof Response) {
			throw err;
		}
		throw error(500, 'เกิดข้อผิดพลาดในการโหลดข้อมูล');
	}
};

export const actions: Actions = {
	default: async ({ request, params, locals }) => {
		try {
			if (!locals.token || !locals.user) {
				return { success: false, error: 'กรุณาเข้าสู่ระบบ' };
			}

			const siteId = params.siteId;
			if (!siteId) {
				return { success: false, error: 'Site ID is required' };
			}

			const formData = await request.formData();

			// Parse form data
			const productData = {
				name: formData.get('name') as string,
				description: formData.get('description') as string,
				price: parseFloat(formData.get('price') as string) || 0,
				comparePrice: parseFloat(formData.get('comparePrice') as string) || 0,
				cost: parseFloat(formData.get('cost') as string) || 0,
				sku: formData.get('sku') as string,
				barcode: formData.get('barcode') as string,
				stock: parseInt(formData.get('stock') as string) || 0,
				lowStockThreshold: parseInt(formData.get('lowStockThreshold') as string) || 5,
				categoryId: formData.get('categoryId') as string,
				brandId: formData.get('brandId') as string,
				tags: (formData.get('tags') as string)?.split(',').filter(Boolean) || [],
				isActive: formData.get('isActive') === 'on',
				isDigital: formData.get('isDigital') === 'on',
				weight: parseFloat(formData.get('weight') as string) || 0,
				dimensions: (() => {
					const dimensionsStr = formData.get('dimensions') as string;
					if (dimensionsStr) {
						try {
							return JSON.parse(dimensionsStr);
						} catch {
							return {
								length: parseFloat(formData.get('dimensions.length') as string) || 0,
								width: parseFloat(formData.get('dimensions.width') as string) || 0,
								height: parseFloat(formData.get('dimensions.height') as string) || 0,
							};
						}
					}
					return {
						length: parseFloat(formData.get('dimensions.length') as string) || 0,
						width: parseFloat(formData.get('dimensions.width') as string) || 0,
						height: parseFloat(formData.get('dimensions.height') as string) || 0,
					};
				})(),
				seoTitle: formData.get('seoTitle') as string,
				seoDescription: formData.get('seoDescription') as string,
				seoKeywords: (formData.get('seoKeywords') as string)?.split(',').filter(Boolean) || [],
				images: [], // Will be handled separately for file uploads
			};

			console.log('Product data:', productData);

			// Validation
			if (!productData.name) {
				return { success: false, error: 'กรุณากรอกชื่อสินค้า' };
			}

			if (productData.price <= 0) {
				return { success: false, error: 'กรุณากรอกราคาสินค้า' };
			}

			const response = await productService.createProduct(productData, siteId, locals.token);

			if (response.success) {
				return { success: true, message: 'สร้างสินค้าเรียบร้อย' };
			} else {
				return {
					success: false,
					error: response.error || 'เกิดข้อผิดพลาดในการสร้างสินค้า',
				};
			}
		} catch (err) {
			console.error('Error creating product:', err);
			if (err instanceof Response) {
				throw err;
			}
			return { success: false, error: 'เกิดข้อผิดพลาดในการสร้างสินค้า' };
		}
	},
};
