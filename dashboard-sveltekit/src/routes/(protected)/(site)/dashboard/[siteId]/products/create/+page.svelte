<script lang="ts">
	import Icon from '@iconify/svelte';
	import { t } from 'svelte-i18n';
	import { enhance } from '$app/forms';
	import SEO from '$lib/components/layout/SEO.svelte';
	import Checkbox from '$lib/components/ui/Checkbox.svelte';
	import { authStore } from '$lib/stores/auth.svelte';
	import { siteStore } from '$lib/stores/site.svelte';

	const { data, form } = $props<{
		data: {
			categories?: any[];
			error?: string;
		};
		form?: any;
	}>();

	const categories = $derived(data?.categories || []);
	const error = $derived(data?.error);

	// รอให้ authStore initialized
	const isReady = $derived(authStore.isInitialized);

	// Form action result
	const formResult = $derived(form);

	// Redirect after successful creation
	$effect(() => {
		if (formResult?.success) {
			setTimeout(() => {
				window.location.href = '../';
			}, 1500);
		}
	});

	// Form data
	const formData = {
		name: '',
		description: '',
		price: 0,
		comparePrice: 0,
		cost: 0,
		sku: '',
		barcode: '',
		stock: 0,
		lowStockThreshold: 5,
		categoryId: '',
		brandId: '',
		tags: [] as string[],
		isActive: true,
		isDigital: false,
		weight: 0,
		dimensions: {
			length: 0,
			width: 0,
			height: 0,
		},
		seoTitle: '',
		seoDescription: '',
		seoKeywords: [] as string[],
	};

	let tagInput = $state('');

	function addTag() {
		if (tagInput.trim() && !formData.tags.includes(tagInput.trim())) {
			formData.tags = [...formData.tags, tagInput.trim()];
			tagInput = '';
		}
	}

	function removeTag(tag: string) {
		formData.tags = formData.tags.filter(t => t !== tag);
	}

	function addKeyword() {
		if (tagInput.trim() && !formData.seoKeywords.includes(tagInput.trim())) {
			formData.seoKeywords = [...formData.seoKeywords, tagInput.trim()];
			tagInput = '';
		}
	}

	function removeKeyword(keyword: string) {
		formData.seoKeywords = formData.seoKeywords.filter(k => k !== keyword);
	}
</script>

<SEO
	title="สร้างสินค้าใหม่ - จัดการสินค้า"
	description="สร้างสินค้าใหม่ในระบบ"
	keywords="create product, สร้างสินค้า, เพิ่มสินค้า"
	url="/dashboard/products/create"
	noindex={true}
/>

{#if !isReady}
	<div class="flex items-center justify-center min-h-screen">
		<div class="loading loading-spinner loading-lg"></div>
	</div>
{:else}
	<div class="space-y-6 sm:space-y-6">
		<!-- Form Action Messages -->
		{#if formResult?.success}
			<div class="alert alert-success">
				<Icon icon="mdi:check-circle" class="w-5 h-5" />
				<span>{formResult.message}</span>
			</div>
		{:else if formResult?.error}
			<div class="alert alert-error">
				<Icon icon="mdi:alert-circle" class="w-5 h-5" />
				<span>{formResult.error}</span>
			</div>
		{/if}

		<!-- Header -->
		<div class="flex justify-between items-center">
			<div>
				<h1 class="text-3xl font-bold text-base-content">
					<Icon icon="mdi:package-plus" class="w-8 h-8 inline mr-2" />
					สร้างสินค้าใหม่
				</h1>
				<p class="text-base-content/60 mt-1">เพิ่มสินค้าใหม่เข้าสู่ระบบ</p>
			</div>
			<div class="flex gap-2">
				<a href="../products" class="btn btn-outline">
					<Icon icon="mdi:arrow-left" class="w-5 h-5" />
					กลับ
				</a>
			</div>
		</div>

		{#if error}
			<div class="alert alert-error">
				<Icon icon="mdi:alert-circle" class="w-5 h-5" />
				<span>{error}</span>
			</div>
		{/if}

		<!-- Form -->
		<div class="card bg-base-100 shadow-lg">
			<div class="card-body">
				<form
					method="POST"
					use:enhance={() => {
						return async ({ result, update }) => {
							await update();
						};
					}}
				>
					<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
						<!-- Basic Information -->
						<div class="space-y-4">
							<h3 class="text-lg font-semibold">ข้อมูลพื้นฐาน</h3>

							<div class="form-control">
								<label class="label" for="product-name">
									<span class="label-text">ชื่อสินค้า *</span>
								</label>
								<input
									id="product-name"
									type="text"
									name="name"
									bind:value={formData.name}
									class="input input-bordered"
									required
								/>
							</div>

							<div class="form-control">
								<label class="label" for="product-description">
									<span class="label-text">คำอธิบาย</span>
								</label>
								<textarea
									id="product-description"
									name="description"
									bind:value={formData.description}
									class="textarea textarea-bordered"
									rows="4"
									placeholder="รายละเอียดสินค้า..."
								></textarea>
							</div>

							<div class="grid grid-cols-1 md:grid-cols-3 gap-4">
								<div class="form-control">
									<label class="label" for="product-price">
										<span class="label-text">ราคา *</span>
									</label>
									<input
										id="product-price"
										type="number"
										name="price"
										bind:value={formData.price}
										class="input input-bordered"
										min="0"
										step="0.01"
										required
									/>
								</div>

								<div class="form-control">
									<label class="label" for="product-compare-price">
										<span class="label-text">ราคาเปรียบเทียบ</span>
									</label>
									<input
										id="product-compare-price"
										type="number"
										name="comparePrice"
										bind:value={formData.comparePrice}
										class="input input-bordered"
										min="0"
										step="0.01"
									/>
								</div>

								<div class="form-control">
									<label class="label" for="product-cost">
										<span class="label-text">ต้นทุน</span>
									</label>
									<input
										id="product-cost"
										type="number"
										name="cost"
										bind:value={formData.cost}
										class="input input-bordered"
										min="0"
										step="0.01"
									/>
								</div>
							</div>

							<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
								<div class="form-control">
									<label class="label" for="product-sku">
										<span class="label-text">SKU</span>
									</label>
									<input
										id="product-sku"
										type="text"
										name="sku"
										bind:value={formData.sku}
										class="input input-bordered"
									/>
								</div>

								<div class="form-control">
									<label class="label" for="product-barcode">
										<span class="label-text">บาร์โค้ด</span>
									</label>
									<input
										id="product-barcode"
										type="text"
										name="barcode"
										bind:value={formData.barcode}
										class="input input-bordered"
										placeholder="บาร์โค้ดสินค้า"
									/>
								</div>
							</div>

							<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
								<div class="form-control">
									<label class="label" for="product-stock">
										<span class="label-text">สต็อก *</span>
									</label>
									<input
										id="product-stock"
										type="number"
										name="stock"
										bind:value={formData.stock}
										class="input input-bordered"
										min="0"
										required
									/>
								</div>

								<div class="form-control">
									<label class="label" for="product-low-stock">
										<span class="label-text">สต็อกต่ำเตือน</span>
									</label>
									<input
										id="product-low-stock"
										type="number"
										name="lowStockThreshold"
										bind:value={formData.lowStockThreshold}
										class="input input-bordered"
										min="0"
									/>
								</div>
							</div>

							<div class="form-control">
								<label class="label" for="product-category">
									<span class="label-text">หมวดหมู่</span>
								</label>
								<select
									id="product-category"
									name="categoryId"
									bind:value={formData.categoryId}
									class="select select-bordered"
								>
									<option value="">เลือกหมวดหมู่</option>
									{#each categories as category}
										<option value={category._id}>{category.name}</option>
									{/each}
								</select>
							</div>

							<div class="form-control">
								<label class="label" for="product-brand">
									<span class="label-text">แบรนด์</span>
								</label>
								<input
									id="product-brand"
									type="text"
									name="brandId"
									bind:value={formData.brandId}
									class="input input-bordered"
									placeholder="ชื่อแบรนด์"
								/>
							</div>
						</div>

						<!-- Additional Information -->
						<div class="space-y-4">
							<h3 class="text-lg font-semibold">ข้อมูลเพิ่มเติม</h3>

							<div class="form-control">
								<label class="label" for="product-tags">
									<span class="label-text">แท็ก</span>
								</label>
								<div class="flex gap-2">
									<input
										id="product-tags"
										type="text"
										bind:value={tagInput}
										class="input input-bordered flex-1"
										placeholder="พิมพ์แท็กแล้วกด Enter"
										onkeydown={e => {
											if (e.key === 'Enter') {
												e.preventDefault();
												addTag();
											}
										}}
									/>
									<button type="button" class="btn btn-outline" onclick={addTag}>
										<Icon icon="mdi:plus" class="w-4 h-4" />
									</button>
								</div>
								{#if formData.tags.length > 0}
									<div class="flex flex-wrap gap-2 mt-2">
										{#each formData.tags as tag}
											<span class="badge badge-primary gap-1">
												{tag}
												<button
													type="button"
													class="btn btn-xs btn-ghost"
													onclick={() => removeTag(tag)}
												>
													<Icon icon="mdi:close" class="w-3 h-3" />
												</button>
											</span>
										{/each}
									</div>
								{/if}
								<input type="hidden" name="tags" value={formData.tags.join(',')} />
							</div>

							<div class="grid grid-cols-1 md:grid-cols-3 gap-4">
								<div class="form-control">
									<label class="label" for="product-weight">
										<span class="label-text">น้ำหนัก (กรัม)</span>
									</label>
									<input
										id="product-weight"
										type="number"
										name="weight"
										bind:value={formData.weight}
										class="input input-bordered"
										min="0"
										step="0.1"
									/>
								</div>

								<div class="form-control">
									<label class="label" for="product-length">
										<span class="label-text">ความยาว (ซม.)</span>
									</label>
									<input
										id="product-length"
										type="number"
										name="dimensions.length"
										bind:value={formData.dimensions.length}
										class="input input-bordered"
										min="0"
										step="0.1"
									/>
								</div>

								<div class="form-control">
									<label class="label" for="product-width">
										<span class="label-text">ความกว้าง (ซม.)</span>
									</label>
									<input
										id="product-width"
										type="number"
										name="dimensions.width"
										bind:value={formData.dimensions.width}
										class="input input-bordered"
										min="0"
										step="0.1"
									/>
								</div>
							</div>

							<div class="form-control">
								<label class="label" for="product-height">
									<span class="label-text">ความสูง (ซม.)</span>
								</label>
								<input
									id="product-height"
									type="number"
									name="dimensions.height"
									bind:value={formData.dimensions.height}
									class="input input-bordered"
									min="0"
									step="0.1"
								/>
							</div>

							<!-- Hidden inputs for dimensions -->
							<input type="hidden" name="dimensions" value={JSON.stringify(formData.dimensions)} />

							<Checkbox
								name="isActive"
								bind:checked={formData.isActive}
								class="checkbox checkbox-primary checkbox-lg"
								id="isActive"
								label="เปิดขาย"
							/>

							<Checkbox
								name="isDigital"
								bind:checked={formData.isDigital}
								id="isDigital"
								label="สินค้าดิจิทัล"
							/>

							<!-- Hidden inputs for form data -->
							<input type="hidden" name="isActive" value={formData.isActive ? 'on' : ''} />
							<input type="hidden" name="isDigital" value={formData.isDigital ? 'on' : ''} />
						</div>
					</div>

					<!-- SEO Section -->
					<div class="divider"></div>
					<div class="space-y-4">
						<h3 class="text-lg font-semibold">SEO</h3>

						<div class="form-control">
							<label for="seoTitle" class="label">
								<span class="label-text">SEO Title</span>
							</label>
							<input
								type="text"
								name="seoTitle"
								bind:value={formData.seoTitle}
								class="input input-bordered"
								placeholder="ชื่อหน้าเว็บสำหรับ SEO"
							/>
						</div>

						<div class="form-control">
							<label for="seoDescription" class="label">
								<span class="label-text">SEO Description</span>
							</label>
							<textarea
								name="seoDescription"
								bind:value={formData.seoDescription}
								class="textarea textarea-bordered"
								rows="3"
								placeholder="คำอธิบายหน้าเว็บสำหรับ SEO..."
							></textarea>
						</div>

						<div class="form-control">
							<label for="tagInput" class="label">
								<span class="label-text">SEO Keywords</span>
							</label>
							<div class="flex gap-2">
								<input
									type="text"
									bind:value={tagInput}
									class="input input-bordered flex-1"
									placeholder="พิมพ์คำค้นหาแล้วกด Enter"
									onkeydown={e => {
										if (e.key === 'Enter') {
											e.preventDefault();
											addKeyword();
										}
									}}
								/>
								<button type="button" class="btn btn-outline" onclick={addKeyword}>
									<Icon icon="mdi:plus" class="w-4 h-4" />
								</button>
							</div>
							{#if formData.seoKeywords.length > 0}
								<div class="flex flex-wrap gap-2 mt-2">
									{#each formData.seoKeywords as keyword}
										<span class="badge badge-secondary gap-1">
											{keyword}
											<button
												type="button"
												class="btn btn-xs btn-ghost"
												onclick={() => removeKeyword(keyword)}
											>
												<Icon icon="mdi:close" class="w-3 h-3" />
											</button>
										</span>
									{/each}
								</div>
							{/if}
							<input type="hidden" name="seoKeywords" value={formData.seoKeywords.join(',')} />
						</div>
					</div>

					<!-- Submit Buttons -->
					<div class="divider"></div>
					<div class="flex justify-end gap-2">
						<a href="../" class="btn btn-outline">ยกเลิก</a>
						<button type="submit" class="btn btn-primary">
							<Icon icon="mdi:content-save" class="w-5 h-5" />
							สร้างสินค้า
						</button>
					</div>
				</form>
			</div>
		</div>
	</div>
{/if}
