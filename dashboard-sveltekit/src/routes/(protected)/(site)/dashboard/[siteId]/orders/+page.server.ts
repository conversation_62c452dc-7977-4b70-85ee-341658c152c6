import { orderService } from '$lib/services/order';
// import { requireAuth } from '$lib/utils/auth';
import type { PageServerLoad } from './$types';

export const prerender = false;

export const load: PageServerLoad = async ({ locals, params, url }) => {
	const { siteId } = params;

	// ตรวจสอบ authentication
	// requireAuth(locals);

	try {
		// ตรวจสอบ token
		if (!locals.token) {
			console.log('Orders Page Server: No token found');
			return {
				siteId,
				orders: [],
				orderStats: null,
				error: 'ไม่พบ token สำหรับการเข้าถึง',
			};
		}

		// ดึง query parameters
		const page = parseInt(url.searchParams.get('page') || '1');
		const limit = parseInt(url.searchParams.get('limit') || '10');
		const status = url.searchParams.get('status') || undefined;
		const sortBy = url.searchParams.get('sortBy') || 'createdAt';
		const sortOrder = (url.searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc';

		// ดึงข้อมูล orders และ statistics พร้อมกัน
		const [ordersResult, statsResult] = await Promise.all([
			orderService.getOrders(siteId, locals.token, {
				page,
				limit,
				status: status as any,
				sortBy,
				sortOrder,
			}),
			orderService.getOrderStats(siteId, locals.token),
		]);

		// ตรวจสอบผลลัพธ์
		if (!ordersResult.success) {
			console.log('Orders Page Server: Failed to fetch orders:', ordersResult.error);
			return {
				siteId,
				orders: [],
				orderStats: null,
				error: ordersResult.error,
			};
		}

		if (!statsResult.success) {
			console.log('Orders Page Server: Failed to fetch stats:', statsResult.error);
			// ยังคงส่งข้อมูล orders แม้ stats จะล้มเหลว
			return {
				siteId,
				orders: ordersResult.data?.orders || [],
				pagination: ordersResult.data?.pagination,
				orderStats: null,
				error: `ไม่สามารถดึงสถิติได้: ${statsResult.error}`,
			};
		}

		console.log('Orders Page Server: Successfully loaded data');
		return {
			siteId,
			orders: ordersResult.data?.orders || [],
			pagination: ordersResult.data?.pagination,
			orderStats: statsResult.data,
			error: null,
		};
	} catch (error) {
		console.error('Orders Page Server: Error in load function:', error);
		return {
			siteId,
			orders: [],
			orderStats: null,
			error: error instanceof Error ? error.message : String(error),
		};
	}
};
