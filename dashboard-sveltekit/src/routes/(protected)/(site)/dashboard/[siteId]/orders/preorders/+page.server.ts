import { error } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ params, locals }) => {
	const { siteId } = params;
	const { apiClient } = locals;

	try {
		// ดึงข้อมูล pre-orders
		const preOrdersResponse = await apiClient.request(`/preorder/${siteId}`, {
			method: 'GET',
			headers: {
				Authorization: `Bearer ${locals.token}`,
			},
		});

		return {
			preOrders: preOrdersResponse.data?.preOrders || [],
		};
	} catch (err: any) {
		console.error('Error loading pre-orders data:', err);
		throw error(500, 'ไม่สามารถโหลดข้อมูล pre-orders ได้');
	}
};

// Server actions
export const actions = {
	updatePreOrderStatus: async ({ request, params, locals }) => {
		const { siteId } = params;
		const { apiClient } = locals;
		const formData = await request.formData();

		try {
			const preOrderId = formData.get('preOrderId') as string;
			const status = formData.get('status') as string;
			const estimatedDeliveryDate = formData.get('estimatedDeliveryDate') as string;
			const notes = formData.get('notes') as string;

			const updateData = {
				status,
				...(estimatedDeliveryDate && { estimatedDeliveryDate }),
				...(notes && { notes }),
			};

			const response = await apiClient.request(`/preorder/${siteId}/${preOrderId}`, {
				method: 'PUT',
				headers: {
					Authorization: `Bearer ${locals.token}`,
				},
				body: updateData,
			});

			return { success: true, data: response.data };
		} catch (err: any) {
			console.error('Error updating pre-order status:', err);
			return { success: false, message: err.message };
		}
	},

	cancelPreOrder: async ({ request, params, locals }) => {
		const { siteId } = params;
		const { apiClient } = locals;
		const formData = await request.formData();
		const preOrderId = formData.get('preOrderId') as string;

		try {
			await apiClient.request(`/preorder/${siteId}/${preOrderId}`, {
				method: 'PUT',
				headers: {
					Authorization: `Bearer ${locals.token}`,
				},
				body: { status: 'cancelled' },
			});

			return { success: true };
		} catch (err: any) {
			console.error('Error cancelling pre-order:', err);
			return { success: false, message: err.message };
		}
	},
}; 