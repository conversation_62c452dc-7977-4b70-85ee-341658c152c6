<script lang="ts">
	import Icon from '@iconify/svelte';
	import { onMount } from 'svelte';
	import { enhance } from '$app/forms';
	import { page } from '$app/state';
	import { Badge, Button, Card, Dialog, Input, Select, Table } from '$lib/components/ui';
	import { showErrorToast, showSuccessToast } from '$lib/utils/sweetalert';

	interface PreOrder {
		_id: string;
		orderId: string;
		productId: string;
		productName: string;
		productImage?: string;
		customerId: string;
		customerName: string;
		customerEmail: string;
		quantity: number;
		unitPrice: number;
		totalPrice: number;
		status:
			| 'pending'
			| 'confirmed'
			| 'production'
			| 'ready'
			| 'shipped'
			| 'delivered'
			| 'cancelled';
		estimatedDeliveryDate?: string;
		actualDeliveryDate?: string;
		notes?: string;
		createdAt: string;
		updatedAt: string;
	}

	// รับข้อมูลจาก server
	const { data } = $props();

	const preOrders = $state(data.preOrders);
	const loading = $state(false);
	let searchQuery = $state('');
	const statusFilter = $state('all');
	let selectedPreOrder = $state(null);
	let viewDialogOpen = $state(false);
	let updateDialogOpen = $state(false);
	const processing = $state(false);

	// ฟอร์มอัปเดตสถานะ
	let updateForm = $state({
		status: 'pending' as PreOrder['status'],
		estimatedDeliveryDate: '',
		notes: '',
	});

	const siteId = $derived(page.params.siteId);
	const filteredPreOrders = $derived(
		preOrders.filter(preOrder => {
			const matchesSearch =
				preOrder.productName.toLowerCase().includes(searchQuery.toLowerCase()) ||
				preOrder.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
				preOrder.orderId.toLowerCase().includes(searchQuery.toLowerCase());

			const matchesStatus = statusFilter === 'all' || preOrder.status === statusFilter;

			return matchesSearch && matchesStatus;
		})
	);

	// เปิดดูรายละเอียด
	function viewPreOrder(preOrder) {
		selectedPreOrder = preOrder;
		viewDialogOpen = true;
	}

	// เปิดฟอร์มอัปเดต
	function openUpdateDialog(preOrder: PreOrder) {
		selectedPreOrder = preOrder;
		updateForm = {
			status: preOrder.status,
			estimatedDeliveryDate: preOrder.estimatedDeliveryDate
				? new Date(preOrder.estimatedDeliveryDate).toISOString().split('T')[0]
				: '',
			notes: preOrder.notes || '',
		};
		updateDialogOpen = true;
	}

	// ฟังก์ชันสำหรับแสดงสีของ badge ตามสถานะ
	function getStatusBadgeVariant(status) {
		switch (status) {
			case 'pending':
				return 'outline';
			case 'confirmed':
				return 'solid';
			case 'production':
				return 'solid';
			case 'ready':
				return 'solid';
			case 'shipped':
				return 'solid';
			case 'delivered':
				return 'solid';
			case 'cancelled':
				return 'ghost';
			default:
				return 'outline';
		}
	}

	// ฟังก์ชันสำหรับแปลงสถานะเป็นภาษาไทย
	function getStatusText(status: string) {
		switch (status) {
			case 'pending':
				return 'รอการยืนยัน';
			case 'confirmed':
				return 'ยืนยันแล้ว';
			case 'production':
				return 'กำลังผลิต';
			case 'ready':
				return 'พร้อมจัดส่ง';
			case 'shipped':
				return 'จัดส่งแล้ว';
			case 'delivered':
				return 'ส่งมอบแล้ว';
			case 'cancelled':
				return 'ยกเลิกแล้ว';
			default:
				return status;
		}
	}

	// ฟังก์ชันสำหรับแปลงวันที่
	function formatDate(dateString: string) {
		return new Date(dateString).toLocaleDateString('th-TH', {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
		});
	}

	// ฟังก์ชันสำหรับแปลงเงิน
	function formatCurrency(amount: number) {
		return new Intl.NumberFormat('th-TH', {
			style: 'currency',
			currency: 'THB',
		}).format(amount);
	}

	// คำนวณสถิติ pre-order
	const preOrderStats = $derived({
		total: preOrders.length,
		pending: preOrders.filter(p => p.status === 'pending').length,
		production: preOrders.filter(p => p.status === 'production').length,
		ready: preOrders.filter(p => p.status === 'ready').length,
		totalValue: preOrders.reduce((sum, p) => sum + p.totalPrice, 0),
	});


</script>

<svelte:head>
	<title>จัดการ Pre-Order - Dashboard</title>
</svelte:head>

<div class="container mx-auto space-y-6 sm:space-y-6">
	<div class="flex items-center justify-between">
		<div>
			<h1 class="text-3xl font-bold tracking-tight">จัดการ Pre-Order</h1>
			<p class="text-muted-foreground">จัดการคำสั่งซื้อล่วงหน้าและติดตามสถานะการผลิต</p>
		</div>
	</div>

	<!-- สถิติ pre-order -->
	<div class="grid gap-4 md:grid-cols-5">
		<Card>
			<div class="flex flex-row items-center justify-between space-y-0 pb-2">
				<div class="text-sm font-medium">ทั้งหมด</div>
				<Icon icon="mdi:package" class="h-4 w-4 text-muted-foreground" />
			</div>
			<div>
				<div class="text-2xl font-bold">{preOrderStats.total}</div>
			</div>
		</Card>
		<Card>
			<div class="flex flex-row items-center justify-between space-y-0 pb-2">
				<div class="text-sm font-medium">รอยืนยัน</div>
				<Icon icon="mdi:clock" class="h-4 w-4 text-muted-foreground" />
			</div>
			<div>
				<div class="text-2xl font-bold text-orange-600">
					{preOrderStats.pending}
				</div>
			</div>
		</Card>
		<Card>
			<div class="flex flex-row items-center justify-between space-y-0 pb-2">
				<div class="text-sm font-medium">กำลังผลิต</div>
				<Icon icon="mdi:package" class="h-4 w-4 text-muted-foreground" /> 
			</div>
			<div>
				<div class="text-2xl font-bold text-blue-600">
					{preOrderStats.production}
				</div>
			</div>
		</Card>
		<Card>
			<div class="flex flex-row items-center justify-between space-y-0 pb-2">
				<div class="text-sm font-medium">พร้อมจัดส่ง</div>
				<Icon icon="mdi:truck" class="h-4 w-4 text-muted-foreground" />
			</div>
			<div>
				<div class="text-2xl font-bold text-green-600">
					{preOrderStats.ready}
				</div>
			</div>
		</Card>
		<Card>
			<div class="flex flex-row items-center justify-between space-y-0 pb-2">
				<div class="text-sm font-medium">มูลค่ารวม</div>
				<Icon icon="mdi:package" class="h-4 w-4 text-muted-foreground" />
			</div>
			<div>
				<div class="text-2xl font-bold">
					{formatCurrency(preOrderStats.totalValue)}
				</div>
			</div>
		</Card>
	</div>

	<!-- ตัวกรองและค้นหา -->
	<Card>
		<div class="p-4">
			<div class="flex flex-col md:flex-row gap-4">
				<div class="relative flex-1">
					<Icon
						icon="mdi:search"
						class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"
					/>
					<Input placeholder="ค้นหา pre-order..." bind:value={searchQuery} class="pl-10" />
				</div>
				<Select
					value={statusFilter}
					placeholder="สถานะ"
					className="w-full md:w-48"
					options={[
						{ value: 'all', label: 'ทั้งหมด' },
						{ value: 'pending', label: 'รอการยืนยัน' },
						{ value: 'confirmed', label: 'ยืนยันแล้ว' },
						{ value: 'production', label: 'กำลังผลิต' },
						{ value: 'ready', label: 'พร้อมจัดส่ง' },
						{ value: 'shipped', label: 'จัดส่งแล้ว' },
						{ value: 'delivered', label: 'ส่งมอบแล้ว' },
						{ value: 'cancelled', label: 'ยกเลิกแล้ว' },
					]}
				/>
			</div>
		</div>
	</Card>

	{#if loading}
		<div class="flex items-center justify-center py-12">
			<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
		</div>
	{:else}
		<Card>
			<div>
				<div class="flex items-center gap-2">
					<Icon icon="mdi:clock" class="h-5 w-5" />
					Pre-Order ทั้งหมด ({filteredPreOrders.length})
				</div>
			</div>
			<div>
				{#if filteredPreOrders.length === 0}
					<div class="text-center py-8 text-muted-foreground">
						{searchQuery || statusFilter !== 'all'
							? 'ไม่พบ pre-order ที่ตรงกับเงื่อนไข'
							: 'ยังไม่มี pre-order'}
					</div>
				{:else}
					<Table>
						<table class="table table-zebra w-full">
							<thead>
								<tr>
									<th>คำสั่งซื้อ</th>
									<th>สินค้า</th>
									<th>ลูกค้า</th>
									<th>จำนวน</th>
									<th>ราคา</th>
									<th>สถานะ</th>
									<th>วันที่สั่ง</th>
									<th class="text-right">การจัดการ</th>
								</tr>
							</thead>
							<tbody>
								{#each filteredPreOrders as preOrder (preOrder._id)}
									<tr>
										<td>
											<span class="font-mono text-sm">{preOrder.orderId}</span>
										</td>
										<td>
											<div class="flex items-center gap-2">
												{#if preOrder.productImage}
													<img
														src={preOrder.productImage}
														alt={preOrder.productName}
														class="h-8 w-8 rounded object-cover"
													/>
												{:else}
													<div class="h-8 w-8 rounded bg-muted flex items-center justify-center">
														<Icon icon="mdi:package" class="h-4 w-4 text-muted-foreground" />
													</div>
												{/if}
												<span class="font-medium">{preOrder.productName}</span>
											</div>
										</td>
										<td>
											<div class="flex items-center gap-2">
												<Icon icon="mdi:user" class="h-4 w-4 text-muted-foreground" />
												<div>
													<p class="font-medium">
														{preOrder.customerName}
													</p>
													<p class="text-xs text-muted-foreground">
														{preOrder.customerEmail}
													</p>
												</div>
											</div>
										</td>
										<td>
											<Badge variant="outline">{preOrder.quantity}</Badge>
										</td>
										<td>
											<div>
												<p class="font-medium">
													{formatCurrency(preOrder.totalPrice)}
												</p>
												<p class="text-xs text-muted-foreground">
													{formatCurrency(preOrder.unitPrice)} x {preOrder.quantity}
												</p>
											</div>
										</td>
										<td>
											<Badge variant={getStatusBadgeVariant(preOrder.status)}>
												{getStatusText(preOrder.status)}
											</Badge>
										</td>
										<td class="text-sm text-muted-foreground">
											{formatDate(preOrder.createdAt)}
										</td>
										<td class="text-right">
											<div class="flex items-center justify-end gap-2">
												<Button variant="ghost" size="sm" onclick={() => viewPreOrder(preOrder)}>
													<Icon icon="mdi:eye" class="h-4 w-4" />
												</Button>
												{#if preOrder.status !== 'cancelled' && preOrder.status !== 'delivered'}
													<Button
														variant="ghost"
														size="sm"
														onclick={() => openUpdateDialog(preOrder)}
													>
														<Icon icon="mdi:pencil" class="h-4 w-4" />
													</Button>
												{/if}
												{#if preOrder.status === 'pending'}
													<Button
														variant="ghost"
														size="sm"
														onclick={() => cancelPreOrder(preOrder._id)}
													>
														<Icon icon="mdi:close-circle" class="h-4 w-4" />
													</Button>
												{/if}
											</div>
										</td>
									</tr>
								{/each}
							</tbody>
						</table>
					</Table>
				{/if}
			</div>
		</Card>
	{/if}
</div>

<!-- Dialog ดูรายละเอียด -->
<Dialog open={viewDialogOpen} onClose={() => (viewDialogOpen = false)}>
	<div class="p-6">
		{#if selectedPreOrder}
			<div class="mb-4">
				<h2 class="text-lg font-bold">รายละเอียด Pre-Order</h2>
				<div class="text-sm text-muted-foreground">
					คำสั่งซื้อ {selectedPreOrder.orderId}
				</div>
			</div>

			<div class="space-y-4">
				<div class="grid grid-cols-2 gap-4">
					<div>
						<p class="text-sm font-medium">สินค้า</p>
						<p class="text-sm">{selectedPreOrder.productName}</p>
					</div>
					<div>
						<p class="text-sm font-medium">ลูกค้า</p>
						<p class="text-sm">{selectedPreOrder.customerName}</p>
						<p class="text-xs text-muted-foreground">
							{selectedPreOrder.customerEmail}
						</p>
					</div>
				</div>

				<div class="grid grid-cols-3 gap-4">
					<div>
						<p class="text-sm font-medium">จำนวน</p>
						<p class="text-sm">{selectedPreOrder.quantity}</p>
					</div>
					<div>
						<p class="text-sm font-medium">ราคาต่อหน่วย</p>
						<p class="text-sm">
							{formatCurrency(selectedPreOrder.unitPrice)}
						</p>
					</div>
					<div>
						<p class="text-sm font-medium">ราคารวม</p>
						<p class="text-sm font-bold">
							{formatCurrency(selectedPreOrder.totalPrice)}
						</p>
					</div>
				</div>

				<div class="grid grid-cols-2 gap-4">
					<div>
						<p class="text-sm font-medium">สถานะ</p>
						<Badge variant={getStatusBadgeVariant(selectedPreOrder.status)}>
							{getStatusText(selectedPreOrder.status)}
						</Badge>
					</div>
					<div>
						<p class="text-sm font-medium">วันที่สั่ง</p>
						<p class="text-sm">
							{formatDate(selectedPreOrder.createdAt)}
						</p>
					</div>
				</div>

				{#if selectedPreOrder.estimatedDeliveryDate}
					<div>
						<p class="text-sm font-medium">วันที่คาดว่าจะส่งมอบ</p>
						<p class="text-sm">
							{formatDate(selectedPreOrder.estimatedDeliveryDate)}
						</p>
					</div>
				{/if}

				{#if selectedPreOrder.actualDeliveryDate}
					<div>
						<p class="text-sm font-medium">วันที่ส่งมอบจริง</p>
						<p class="text-sm">
							{formatDate(selectedPreOrder.actualDeliveryDate)}
						</p>
					</div>
				{/if}

				{#if selectedPreOrder.notes}
					<div>
						<p class="text-sm font-medium">หมายเหตุ</p>
						<p class="text-sm whitespace-pre-wrap">
							{selectedPreOrder.notes}
						</p>
					</div>
				{/if}
			</div>
		{/if}
	</div>
</Dialog>

<!-- Dialog อัปเดตสถานะ -->
<Dialog open={updateDialogOpen} onClose={() => (updateDialogOpen = false)}>
	<div class="p-6">
		{#if selectedPreOrder}
			<div class="mb-4">
				<h2 class="text-lg font-bold">อัปเดตสถานะ Pre-Order</h2>
				<div class="text-sm text-muted-foreground">
					คำสั่งซื้อ {selectedPreOrder.orderId}
				</div>
			</div>

			<div class="space-y-4">
				<div class="space-y-2">
					<label class="text-sm font-medium">สถานะ</label>
					<Select
						 value={updateForm.status}
						options={[
							{ value: 'pending', label: 'รอการยืนยัน' },
							{ value: 'confirmed', label: 'ยืนยันแล้ว' },
							{ value: 'production', label: 'กำลังผลิต' },
							{ value: 'ready', label: 'พร้อมจัดส่ง' },
							{ value: 'shipped', label: 'จัดส่งแล้ว' },
							{ value: 'delivered', label: 'ส่งมอบแล้ว' },
							{ value: 'cancelled', label: 'ยกเลิกแล้ว' },
						]}
					/>
				</div>

				<div class="space-y-2">
					<label class="text-sm font-medium">วันที่คาดว่าจะส่งมอบ</label>
					<Input type="text" bind:value={updateForm.estimatedDeliveryDate} />
				</div>

				<div class="space-y-2">
					<label class="text-sm font-medium">หมายเหตุ</label>
					<textarea
						class="w-full p-2 border rounded-md"
						rows="3"
						placeholder="หมายเหตุเพิ่มเติม..."
						bind:value={updateForm.notes}
					></textarea>
				</div>

				<div class="flex gap-2">
					<Button onclick={updatePreOrderStatus} disabled={processing} class="flex-1">
						{#if processing}
							<div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
						{:else}
							<Icon icon="mdi:check-circle" class="h-4 w-4 mr-2" />
						{/if}
						อัปเดตสถานะ
					</Button>
					<Button variant="outline" onclick={() => (updateDialogOpen = false)}>ยกเลิก</Button>
				</div>
			</div>
		{/if}
	</div>
</Dialog>
