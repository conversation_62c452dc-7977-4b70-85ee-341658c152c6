import { subscriptionService } from '$lib/services/subscription';
import type { NotificationPageData } from '$lib/types/page';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({
	locals,
	params,
	url,
	parent,
}): Promise<NotificationPageData> => {
	const { siteId } = params;
	const token = locals.token;

	// ดึงข้อมูล site จาก parent layout
	const parentData = await parent();

	if (!token) {
		return {
			user: locals.user || null,
			site: parentData.site || null,
			notifications: [],
			pagination: null,
			error: 'ไม่พบ token การยืนยันตัวตน',
		};
	}

	try {
		// ดึงการแจ้งเตือนของ site
		const page = url.searchParams.get('page') || '1';
		const limit = url.searchParams.get('limit') || '20';
		const unreadOnly = url.searchParams.get('unreadOnly') || 'false';

		const notificationsResult = await subscriptionService.getSiteNotifications(
			siteId,
			{
				page,
				limit,
				unreadOnly,
			},
			token
		);

		return {
			user: locals.user || null,
			site: parentData.site || null,
			notifications: notificationsResult.success
				? notificationsResult.data?.notifications || []
				: [],
			pagination: notificationsResult.success ? notificationsResult.data?.pagination || null : null,
			error: null,
		};
	} catch (error) {
		console.error('Subscription notifications page load error:', error);
		return {
			user: locals.user || null,
			site: parentData.site || null,
			notifications: [],
			pagination: null,
			error: 'เกิดข้อผิดพลาดในการดึงข้อมูลการแจ้งเตือน',
		};
	}
};
