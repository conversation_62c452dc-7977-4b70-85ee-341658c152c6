import { error } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ params, locals }) => {
	const { siteId } = params;
	const { apiClient } = locals;

	try {
		// ดึงข้อมูลแคมเปญ
		const campaignsResponse = await apiClient.request(`/marketing/${siteId}/campaigns`, {
			method: 'GET',
			headers: {
				Authorization: `Bearer ${locals.token}`,
			},
		});

		// ดึงข้อมูลส่วนลด
		const discountsResponse = await apiClient.request(`/discount/${siteId}`, {
			method: 'GET',
			headers: {
				Authorization: `Bearer ${locals.token}`,
			},
		});

		return {
			campaigns: campaignsResponse.data?.campaigns || [],
			discounts: discountsResponse.data?.discounts || [],
		};
	} catch (err: any) {
		console.error('Error loading marketing data:', err);
		throw error(500, 'ไม่สามารถโหลดข้อมูลการตลาดได้');
	}
};

// Server actions
export const actions = {
	createCampaign: async ({ request, params, locals }) => {
		const { siteId } = params;
		const { apiClient } = locals;
		const formData = await request.formData();

		try {
			const campaignData = {
				name: formData.get('name') as string,
				type: formData.get('type') as string,
				status: formData.get('status') as string,
				description: formData.get('description') as string,
				startDate: formData.get('startDate') as string,
				endDate: formData.get('endDate') as string,
				targetAudience: formData.get('targetAudience') as string,
				budget: Number(formData.get('budget')) || 0,
			};

			const response = await apiClient.request(`/marketing/${siteId}/campaigns`, {
				method: 'POST',
				headers: {
					Authorization: `Bearer ${locals.token}`,
				},
				body: campaignData,
			});

			return { success: true, data: response.data };
		} catch (err: any) {
			console.error('Error creating campaign:', err);
			return { success: false, message: err.message };
		}
	},

	createDiscount: async ({ request, params, locals }) => {
		const { siteId } = params;
		const { apiClient } = locals;
		const formData = await request.formData();

		try {
			const discountData = {
				code: formData.get('code') as string,
				name: formData.get('name') as string,
				type: formData.get('type') as string,
				value: Number(formData.get('value')) || 0,
				minOrderAmount: Number(formData.get('minOrderAmount')) || 0,
				maxDiscount: Number(formData.get('maxDiscount')) || 0,
				usageLimit: Number(formData.get('usageLimit')) || 0,
				startDate: formData.get('startDate') as string,
				endDate: formData.get('endDate') as string,
			};

			const response = await apiClient.request(`/discount/${siteId}`, {
				method: 'POST',
				headers: {
					Authorization: `Bearer ${locals.token}`,
				},
				body: discountData,
			});

			return { success: true, data: response.data };
		} catch (err: any) {
			console.error('Error creating discount:', err);
			return { success: false, message: err.message };
		}
	},

	deleteCampaign: async ({ request, params, locals }) => {
		const { siteId } = params;
		const { apiClient } = locals;
		const formData = await request.formData();
		const campaignId = formData.get('campaignId') as string;

		try {
			await apiClient.request(`/marketing/${siteId}/campaigns/${campaignId}`, {
				method: 'DELETE',
				headers: {
					Authorization: `Bearer ${locals.token}`,
				},
			});

			return { success: true };
		} catch (err: any) {
			console.error('Error deleting campaign:', err);
			return { success: false, message: err.message };
		}
	},

	deleteDiscount: async ({ request, params, locals }) => {
		const { siteId } = params;
		const { apiClient } = locals;
		const formData = await request.formData();
		const discountId = formData.get('discountId') as string;

		try {
			await apiClient.request(`/discount/${siteId}/${discountId}`, {
				method: 'DELETE',
				headers: {
					Authorization: `Bearer ${locals.token}`,
				},
			});

			return { success: true };
		} catch (err: any) {
			console.error('Error deleting discount:', err);
			return { success: false, message: err.message };
		}
	},
};
