import { fail, redirect } from '@sveltejs/kit';
import type { CheckDomainData, CreateSiteData } from '$lib/schemas/site.schema';
import { siteService } from '$lib/services/site';
import { subscriptionService } from '$lib/services/subscription';
import type { Actions, PageServerLoad } from './$types';

/**
 * ✅ HYBRID APPROACH: SvelteKit Route API + Service Pattern
 * - Route-level validation and error handling
 * - Service layer for business logic
 * - Consistent fail() responses
 * - Type-safe data flow
 * - Progressive enhancement with use:enhance
 */

export const load: PageServerLoad = async ({ locals }) => {
	// Auth check already done in layout
	try {
		// ✅ Load packages for site creation
		const packagesResult = await subscriptionService.getPackages(locals.token!);

		return {
			packages:
				packagesResult.success && packagesResult.data
					? Array.isArray(packagesResult.data)
						? packagesResult.data
						: (packagesResult.data as any)?.packages || []
					: [],
		};
	} catch (error) {
		console.error('Error loading packages:', error);
		return {
			packages: [],
		};
	}
};

export const actions: Actions = {
	/**
	 * ✅ Check domain availability
	 */
	checkDomain: async ({ request, locals }) => {
		try {
			const data = await request.formData();

			// Extract form data
			const domainData: CheckDomainData = {
				typeDomain: data.get('typeDomain') as 'subdomain' | 'custom',
				subDomain: data.get('subDomain')?.toString(),
				mainDomain: data.get('mainDomain')?.toString(),
				customDomain: data.get('customDomain')?.toString(),
			};

			// Call site service (validation included)
			const result = await siteService.checkDomain(domainData, locals.token);

			if (!result.success) {
				return fail(400, {
					message: result.error,
					type: 'domain',
				});
			}

			return {
				success: true,
				data: result.data,
				message: result.data?.available ? 'โดเมนพร้อมใช้งาน' : 'โดเมนไม่พร้อมใช้งาน',
				type: 'domain',
			};
		} catch (error) {
			console.error('Check domain error:', error);
			return fail(500, {
				message: 'เกิดข้อผิดพลาดในการตรวจสอบโดเมน',
				type: 'domain',
			});
		}
	},

	/**
	 * ✅ Check discount code
	 */
	checkDiscount: async ({ request, locals }) => {
		try {
			const data = await request.formData();

			// Extract form data
			const discountCode = data.get('discountCode')?.toString()?.trim();
			const orderAmount = Number(data.get('orderAmount')) || 0;

			// Route-level validation
			if (!discountCode) {
				return fail(400, {
					message: 'กรุณากรอกรหัสส่วนลด',
					type: 'discount',
				});
			}

			if (discountCode.length < 2) {
				return fail(400, {
					message: 'รหัสส่วนลดต้องมีอย่างน้อย 2 ตัวอักษร',
					type: 'discount',
				});
			}

			if (orderAmount <= 0) {
				return fail(400, {
					message: 'ยอดสั่งซื้อไม่ถูกต้อง',
					type: 'discount',
				});
			}

			// Call subscription service for discount validation
			const result = await subscriptionService.validateDiscount(discountCode, locals.token!);

			if (!result.success) {
				return fail(400, {
					message: result.error || 'รหัสส่วนลดไม่ถูกต้องหรือหมดอายุ',
					type: 'discount',
				});
			}

			return {
				success: true,
				data: result.data,
				message: 'รหัสส่วนลดถูกต้อง',
				type: 'discount',
			};
		} catch (error) {
			console.error('Check discount error:', error);
			return fail(500, {
				message: 'เกิดข้อผิดพลาดในการตรวจสอบรหัสส่วนลด',
				type: 'discount',
			});
		}
	},

	/**
	 * ✅ Create new site with enhanced validation
	 */
	createSite: async ({ request, locals }) => {
		try {
			const data = await request.formData();

			// Extract form data
			const siteData: CreateSiteData = {
				siteName: data.get('siteName')?.toString()?.trim() || '',
				typeDomain: data.get('typeDomain') as 'subdomain' | 'custom',
				subDomain: data.get('subDomain')?.toString()?.trim(),
				mainDomain: data.get('mainDomain')?.toString()?.trim(),
				customDomain: data.get('customDomain')?.toString()?.trim(),
				packageType: data.get('packageType')?.toString()?.trim() || '',
			};

			// Route-level validation
			if (!siteData.siteName) {
				return fail(400, {
					message: 'กรุณากรอกชื่อเว็บไซต์',
					type: 'create',
				});
			}

			if (siteData.siteName.length < 2) {
				return fail(400, {
					message: 'ชื่อเว็บไซต์ต้องมีอย่างน้อย 2 ตัวอักษร',
					type: 'create',
				});
			}

			if (siteData.siteName.length > 100) {
				return fail(400, {
					message: 'ชื่อเว็บไซต์ต้องไม่เกิน 100 ตัวอักษร',
					type: 'create',
				});
			}

			if (!siteData.packageType) {
				return fail(400, {
					message: 'กรุณาเลือกแพ็คเกจ',
					type: 'create',
				});
			}

			// Domain validation based on type
			if (siteData.typeDomain === 'subdomain') {
				if (!siteData.subDomain) {
					return fail(400, {
						message: 'กรุณากรอกซับโดเมน',
						type: 'create',
					});
				}

				if (siteData.subDomain.length < 3) {
					return fail(400, {
						message: 'ซับโดเมนต้องมีอย่างน้อย 3 ตัวอักษร',
						type: 'create',
					});
				}

				// Check for invalid characters
				if (!/^[a-zA-Z0-9-]+$/.test(siteData.subDomain)) {
					return fail(400, {
						message: 'ซับโดเมนสามารถใช้ได้เฉพาะตัวอักษร ตัวเลข และเครื่องหมาย -',
						type: 'create',
					});
				}

				if (!siteData.mainDomain) {
					return fail(400, {
						message: 'กรุณาเลือกโดเมนหลัก',
						type: 'create',
					});
				}
			} else if (siteData.typeDomain === 'custom') {
				if (!siteData.customDomain) {
					return fail(400, {
						message: 'กรุณากรอกโดเมนส่วนตัว',
						type: 'create',
					});
				}

				// Basic domain validation
				const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\.[a-zA-Z]{2,}$/;
				if (!domainRegex.test(siteData.customDomain)) {
					return fail(400, {
						message: 'รูปแบบโดเมนไม่ถูกต้อง',
						type: 'create',
					});
				}
			}

			// Call site service (additional validation included)
			const result = await siteService.createSite(siteData, locals.token!);

			if (!result.success) {
				return fail(400, {
					message: result.error,
					type: 'create',
				});
			}

			// Redirect to the new site dashboard
			throw redirect(303, `/dashboard/${result.data?._id}`);
		} catch (error) {
			console.error('Create site error:', error);

			if (error instanceof Response) {
				throw error; // Re-throw redirects
			}

			return fail(500, {
				message: 'เกิดข้อผิดพลาดในการสร้างเว็บไซต์',
				type: 'create',
			});
		}
	},
};
