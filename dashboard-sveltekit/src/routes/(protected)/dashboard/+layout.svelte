<script lang="ts">
	import Icon from '@iconify/svelte';
	import { onDestroy, onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import UserMenu from '$lib/components/layout/UserMenu.svelte';
	import LoadingScreen from '$lib/components/loading/LoadingScreen.svelte';
	import EnhancedPageTransition from '$lib/components/transitions/EnhancedPageTransition.svelte';
	import { authStore } from '$lib/stores/auth.svelte';
	import { languageStore } from '$lib/stores/language.svelte';
	import { loadingStore } from '$lib/stores/loading.svelte';
	import { themeStore } from '$lib/stores/theme.svelte';

	const { data, children } = $props<{
		data: { user?: any; token?: string };
	}>();

	// ใช้ authStore เป็นหลักหลังจาก initialized แล้ว
	const user = $derived(authStore.isInitialized ? authStore.user : data?.user || authStore.user);
	const isAuthenticated = $derived(
		authStore.isInitialized ? authStore.isAuthenticated : !!data?.user
	);
	const isInitialized = $derived(authStore.isInitialized);

	onMount(async () => {
		// ตั้งค่า user จาก SSR
		if (data?.user) {
			authStore.setUserFromSSR(data.user);
		}

		// ตรวจสอบ authentication หลังจาก initialized
		if (isInitialized && !isAuthenticated) {
			console.log('Not authenticated, attempting token refresh...');

			// ลอง refresh token ก่อน
			const refreshSuccess = await authStore.refreshToken();

			if (!refreshSuccess) {
				console.log('Token refresh failed, redirecting to signin...');
				goto('/signin');
			}
		}
	});

	onDestroy(() => {
		// Cleanup stores
		authStore.destroy();
		themeStore.destroy();
		languageStore.destroy();
	});
</script>

{#if isInitialized && isAuthenticated}
	<div class="space-y-4">
		<!-- Header -->
		<header>
			<div class="bg-base-200">
				<div class="container mx-auto">
					<header class="navbar">
						<div class="navbar-start flex-1 flex flex-row gap-1">
							<a
								href="/dashboard"
								class="btn {page.url.pathname === '/dashboard' ? 'btn-primary' : 'btn-ghost'}"
							>
								<Icon icon="solar:home-smile-angle-bold" class="size-6" /> จัดการเว็บไซต์
							</a>

							<a
								href="/dashboard/create"
								class="btn {page.url.pathname === '/dashboard/create'
									? 'btn-primary'
									: 'btn-ghost'}"
							>
								<Icon icon="solar:home-add-angle-bold" class="size-6" /> สร้างเว็บไซต์
							</a>

							<a
								href="/dashboard/join"
								class="btn {page.url.pathname === '/dashboard/join' ? 'btn-primary' : 'btn-ghost'}"
							>
								<Icon icon="solar:users-group-rounded-bold" class="size-6" /> เข้าร่วมเว็บไซต์
							</a>

							<a
								href="/dashboard/loading-demo"
								class="btn {page.url.pathname === '/dashboard/loading-demo'
									? 'btn-primary'
									: 'btn-ghost'}"
							>
								<Icon icon="solar:refresh-circle-bold" class="size-6" /> Loading Demo
							</a>
						</div>

						<!-- Actions -->
						<div class="navbar-end w-fit">
							<UserMenu />
						</div>
					</header>
				</div>
			</div>
		</header>

		<!-- Main Content -->
		<main class="container mx-auto px-4 sm:px-6 lg:px-8">
			<LoadingScreen
				isLoading={loadingStore.isLoading}
				type={loadingStore.type}
				size={loadingStore.size}
				color={loadingStore.color}
				message={loadingStore.message}
				showProgress={loadingStore.showProgress}
				progress={loadingStore.progress}
				overlay={loadingStore.overlay}
				blur={loadingStore.blur}
				animation={loadingStore.animation}
				duration={loadingStore.duration}
			>
				<EnhancedPageTransition
					type="fade"
					duration={400}
					easing="cubic-out"
					showLoadingBar={true}
					loadingBarColor="primary"
				>
					{@render children()}
				</EnhancedPageTransition>
			</LoadingScreen>
		</main>
	</div>
{:else}
	<!-- Loading state -->
	<div class="flex items-center justify-center min-h-screen">
		<div class="loading loading-spinner loading-lg"></div>
	</div>
{/if}
