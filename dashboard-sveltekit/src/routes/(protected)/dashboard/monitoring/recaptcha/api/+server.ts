import { json } from '@sveltejs/kit';
import { LogCategory, logger } from '$lib/utils/logger';
import {
	getRecaptchaConfig,
	getRecaptchaStats,
	resetRecaptchaStats,
} from '$lib/utils/recaptcha-verify';
import type { RequestHandler } from './$types';

export const GET: RequestHandler = async ({ locals }) => {
	try {
		// ตรวจสอบสิทธิ์ admin
		if (!locals.user || locals.user.role !== 'admin') {
			return json({ error: 'Unauthorized' }, { status: 403 });
		}

		const stats = getRecaptchaStats();
		const config = getRecaptchaConfig();

		return json({
			stats,
			config,
			timestamp: new Date().toISOString(),
		});
	} catch (error) {
		logger.error(LogCategory.SYSTEM, 'recaptcha_stats_error', 'Failed to get reCAPTCHA stats', {
			error: error instanceof Error ? error.message : 'Unknown error',
			userId: locals.user?._id,
		});

		return json({ error: 'Internal server error' }, { status: 500 });
	}
};

export const POST: RequestHandler = async ({ request, locals }) => {
	try {
		// ตรวจสอบสิทธิ์ admin
		if (!locals.user || locals.user.role !== 'admin') {
			return json({ error: 'Unauthorized' }, { status: 403 });
		}

		const { action } = await request.json();

		if (action === 'reset') {
			resetRecaptchaStats();

			logger.info(LogCategory.SYSTEM, 'recaptcha_stats_reset', 'reCAPTCHA stats reset', {
				userId: locals.user._id,
				userEmail: locals.user.email,
			});

			return json({
				success: true,
				message: 'reCAPTCHA stats reset successfully',
				timestamp: new Date().toISOString(),
			});
		}

		return json({ error: 'Invalid action' }, { status: 400 });
	} catch (error) {
		logger.error(
			LogCategory.SYSTEM,
			'recaptcha_stats_action_error',
			'Failed to perform reCAPTCHA stats action',
			{
				error: error instanceof Error ? error.message : 'Unknown error',
				userId: locals.user?._id,
			}
		);

		return json({ error: 'Internal server error' }, { status: 500 });
	}
};
