import { redirect } from '@sveltejs/kit';
import type { LayoutServerLoad } from './$types';

export const load: LayoutServerLoad = async ({ locals, url }) => {
	console.log('Protected Layout Server: Checking authentication');
	console.log('Protected Layout Server: locals.user:', locals.user ? 'exists' : 'null');
	console.log('Protected Layout Server: locals.token:', locals.token ? 'exists' : 'null');
	console.log('Protected Layout Server: Current path:', url.pathname);

	// ✅ ตรวจสอบ authentication - ถ้าไม่มี redirect ไปหน้า signin
	if (!locals.user || !locals.token) {
		console.log('Protected Layout Server: No authentication found, redirecting to signin');
		console.log('Protected Layout Server: Missing user:', !locals.user);
		console.log('Protected Layout Server: Missing token:', !locals.token);

		// ✅ Redirect ไปหน้า signin พร้อม return URL
		const returnUrl = encodeURIComponent(url.pathname + url.search);
		throw redirect(302, `/signin?returnUrl=${returnUrl}`);
	}

	console.log('Protected Layout Server: Authentication successful');
	console.log('Protected Layout Server: User ID:', locals.user._id);
	console.log('Protected Layout Server: User email:', locals.user.email);

	return {
		user: locals.user,
		token: locals.token,
	};
};
