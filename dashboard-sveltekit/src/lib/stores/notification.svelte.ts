import { notificationService } from '$lib/services/notification';
import type { Notification, NotificationSettings, NotificationStats } from '$lib/types';

interface NotificationState {
	notifications: Notification[];
	unreadCount: number;
	settings: NotificationSettings | null;
	stats: NotificationStats[];
	isLoading: boolean;
	error: string | null;
	pagination: {
		page: number;
		limit: number;
		total: number;
		pages: number;
	};
}

class NotificationStore {
	private state = $state<NotificationState>({
		notifications: [],
		unreadCount: 0,
		settings: null,
		stats: [],
		isLoading: false,
		error: null,
		pagination: {
			page: 1,
			limit: 20,
			total: 0,
			pages: 0,
		},
	});

	// Getters
	get notifications() {
		return this.state.notifications;
	}
	get unreadCount() {
		return this.state.unreadCount;
	}
	get settings() {
		return this.state.settings;
	}
	get stats() {
		return this.state.stats;
	}
	get isLoading() {
		return this.state.isLoading;
	}
	get error() {
		return this.state.error;
	}
	get pagination() {
		return this.state.pagination;
	}

	// Actions
	async loadNotifications(
		params: {
			page?: number;
			limit?: number;
			type?: string;
			status?: string;
		} = {}
	) {
		this.state.isLoading = true;
		this.state.error = null;

		try {
			const response = await notificationService.getNotifications({
				page: params.page,
				limit: params.limit,
				type: params.type as any,
				status: params.status as any,
			});

			if (response.success) {
				this.state.notifications = response.data.notifications;
				this.state.unreadCount = response.data.unreadCount;
				this.state.pagination = response.data.pagination;
			}
		} catch (error) {
			this.state.error = error instanceof Error ? error.message : 'เกิดข้อผิดพลาด';
			console.error('Error loading notifications:', error);
		} finally {
			this.state.isLoading = false;
		}
	}

	async loadUnreadCount() {
		try {
			const response = await notificationService.getUnreadCount();

			if (response.success) {
				this.state.unreadCount = response.data;
			}
		} catch (error) {
			console.error('Error loading unread count:', error);
		}
	}

	async markAsRead(notificationIds: string[]) {
		try {
			const response = await notificationService.markAsRead(notificationIds);

			if (response.success) {
				// อัปเดต state
				this.state.notifications = this.state.notifications.map(notification =>
					notificationIds.includes(notification._id)
						? { ...notification, status: 'read' as const, readAt: new Date() }
						: notification
				);

				// อัปเดต unread count
				const markedCount = notificationIds.length;
				this.state.unreadCount = Math.max(0, this.state.unreadCount - markedCount);
			}
		} catch (error) {
			this.state.error = error instanceof Error ? error.message : 'เกิดข้อผิดพลาด';
			console.error('Error marking as read:', error);
		}
	}

	async markAllAsRead() {
		const unreadNotifications = this.state.notifications
			.filter(n => n.status === 'unread')
			.map(n => n._id);

		if (unreadNotifications.length > 0) {
			await this.markAsRead(unreadNotifications);
		}
	}

	async deleteNotification(notificationId: string) {
		try {
			const response = await notificationService.deleteNotification(notificationId);

			if (response.success) {
				// ลบออกจาก state
				const deletedNotification = this.state.notifications.find(n => n._id === notificationId);
				this.state.notifications = this.state.notifications.filter(n => n._id !== notificationId);

				// อัปเดต unread count ถ้าเป็น unread
				if (deletedNotification?.status === 'unread') {
					this.state.unreadCount = Math.max(0, this.state.unreadCount - 1);
				}
			}
		} catch (error) {
			this.state.error = error instanceof Error ? error.message : 'เกิดข้อผิดพลาด';
			console.error('Error deleting notification:', error);
		}
	}

	async loadSettings() {
		try {
			const response = await notificationService.getSettings();

			if (response.success) {
				this.state.settings = response.data;
			}
		} catch (error) {
			this.state.error = error instanceof Error ? error.message : 'เกิดข้อผิดพลาด';
			console.error('Error loading settings:', error);
		}
	}

	async updateSettings(settings: Partial<NotificationSettings>) {
		try {
			const response = await notificationService.updateSettings(settings);

			if (response.success) {
				this.state.settings = response.data;
			}
		} catch (error) {
			this.state.error = error instanceof Error ? error.message : 'เกิดข้อผิดพลาด';
			console.error('Error updating settings:', error);
		}
	}

	async loadStats() {
		try {
			const response = await notificationService.getStats();

			if (response.success) {
				this.state.stats = response.data;
			}
		} catch (error) {
			this.state.error = error instanceof Error ? error.message : 'เกิดข้อผิดพลาด';
			console.error('Error loading stats:', error);
		}
	}

	// Utility methods
	clearError() {
		this.state.error = null;
	}

	reset() {
		this.state.notifications = [];
		this.state.unreadCount = 0;
		this.state.settings = null;
		this.state.stats = [];
		this.state.isLoading = false;
		this.state.error = null;
		this.state.pagination = {
			page: 1,
			limit: 20,
			total: 0,
			pages: 0,
		};
	}

	// Real-time updates (สำหรับใช้กับ WebSocket หรือ SSE ในอนาคต)
	addNotification(notification: Notification) {
		this.state.notifications.unshift(notification);
		if (notification.status === 'unread') {
			this.state.unreadCount += 1;
		}
	}

	updateNotification(notificationId: string, updates: Partial<Notification>) {
		const index = this.state.notifications.findIndex(n => n._id === notificationId);
		if (index !== -1) {
			this.state.notifications[index] = {
				...this.state.notifications[index],
				...updates,
			};
		}
	}

	destroy() {
		this.reset();
	}
}

// Export singleton instance
export const notificationStore = new NotificationStore();
