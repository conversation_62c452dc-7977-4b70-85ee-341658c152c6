import { browser } from '$app/environment';
import { goto } from '$app/navigation';
import { authService } from '../services/auth';
import { cacheUtils, tokenCache } from '../utils/cache';
import { LogCategory, logger, logSecurityEvent, measurePerformance } from '../utils/logger';
import { generateSessionId, SECURITY_CONFIG } from '../utils/security';
import { securityMonitor } from '../utils/security-monitor';
import {
	showLoginError,
	showLoginSuccess,
	showRegisterError,
	showRegisterSuccess,
	showSignoutSuccess,
} from '../utils/sweetalert';

export interface User {
	_id: string;
	email: string;
	firstName?: string;
	lastName?: string;
	avatar?: string;
	cover?: string;
	isEmailVerified: boolean;
	moneyPoint: number;
	goldPoint: number;
	role?: 'admin' | 'user' | 'moderator';
	status?: 'active' | 'inactive';
	createdAt: string;
	updatedAt: string;
}

export interface SigninData {
	email: string;
	password: string;
	rememberMe?: boolean;
}

export interface SignupData {
	email: string;
	password: string;
	confirmPassword: string;
}

class AuthStore {
	private _user = $state<User | null>(null);
	private _isLoading = $state(false);
	private _error = $state<string | null>(null);
	private _refreshToken = $state<string | null>(null);
	private _accessToken = $state<string | null>(null);
	private _isRefreshing = $state(false);
	private _broadcastChannel: BroadcastChannel | null = null;
	private _storageListener: ((event: StorageEvent) => void) | null = null;
	private _isInitialized = $state(false);
	private _currentTabId: string = '';
	private lastRefreshTime: number = 0;

	constructor() {
		if (browser) {
			this._currentTabId = generateSessionId();
			this.setupCrossTabSync();
			this.setupTokenRefresh();
			// โหลดข้อมูลจาก localStorage เมื่อเริ่มต้น
			this.loadUserFromStorage();
			// ตั้งค่า isInitialized เป็น true เมื่อเริ่มต้น
			this._isInitialized = true;

			logger.info(LogCategory.AUTH, 'store_initialized', 'Auth store initialized', {
				tabId: this._currentTabId,
				hasUser: !!this._user,
				hasAccessToken: !!this._accessToken,
				hasRefreshToken: !!this._refreshToken,
			});
		}
	}

	get user() {
		return this._user;
	}

	get isAuthenticated() {
		return this._user !== null;
	}

	get isLoading() {
		return this._isLoading;
	}

	get error() {
		return this._error;
	}

	get isAdmin() {
		return this._user?.role === 'admin';
	}

	get isModerator() {
		return this._user?.role === 'moderator' || this._user?.role === 'admin';
	}

	get isInitialized() {
		return this._isInitialized;
	}

	// ตั้งค่า user จาก SSR
	setUserFromSSR(user: User | null) {
		logger.info(LogCategory.AUTH, 'ssr_user_set', 'Setting user from SSR', {
			hasUser: !!user,
			userId: user?._id,
			tabId: this._currentTabId,
		});

		this._user = user;
		this._isInitialized = true;

		// Cache user data if available
		if (user) {
			tokenCache.cacheUser(user._id, user);
		}
	}

	private setupCrossTabSync() {
		// ใช้ BroadcastChannel API สำหรับ modern browsers
		if ('BroadcastChannel' in window) {
			try {
				this._broadcastChannel = new BroadcastChannel('auth-sync');
				this._broadcastChannel.onmessage = event => {
					this.handleAuthMessage(event.data);
				};
			} catch (error) {
				console.error('Failed to create BroadcastChannel:', error);
			}
		}

		// ใช้ localStorage events สำหรับ fallback
		this._storageListener = event => {
			if (event.key === 'auth-event' && event.newValue) {
				try {
					const data = JSON.parse(event.newValue);
					this.handleAuthMessage(data);
				} catch (error) {
					console.error('Error parsing auth event:', error);
				}
			}
		};

		window.addEventListener('storage', this._storageListener);
	}

	private broadcastAuthEvent(type: string, payload?: any) {
		const message = {
			type,
			payload,
			timestamp: Date.now(),
			tabId: this._currentTabId, // ใช้ tabId เดียวกัน
		};
		console.log('Broadcasting auth event:', message);

		// ส่งผ่าน BroadcastChannel (ใช้ try-catch แทนการตรวจสอบ readyState)
		if (this._broadcastChannel) {
			try {
				this._broadcastChannel.postMessage(message);
				console.log('Message sent via BroadcastChannel');
			} catch (error) {
				console.error('Failed to send message via BroadcastChannel:', error);
			}
		}

		// ส่งผ่าน localStorage (fallback) - เพิ่มการตรวจสอบ
		if (browser && type !== 'token-refresh') {
			// ไม่ส่ง token-refresh ผ่าน localStorage
			try {
				localStorage.setItem('auth-event', JSON.stringify(message));
				// ลบ event หลังจากส่ง
				setTimeout(() => {
					localStorage.removeItem('auth-event');
				}, 100);
				console.log('Message sent via localStorage');
			} catch (error) {
				console.error('Failed to send message via localStorage:', error);
			}
		}
	}

	private handleAuthMessage(data: { type: string; payload?: any; tabId?: string }) {
		console.log('Received auth message:', data);

		// ตรวจสอบว่าเป็น event จากแท็บเดียวกันหรือไม่
		if (data.tabId === this._currentTabId) {
			console.log('Ignoring event from same tab');
			return;
		}

		switch (data.type) {
			case 'login':
				if (data.payload) {
					console.log('Syncing login across tabs');
					console.log('Previous user:', $state.snapshot(this._user));
					console.log('New user:', data.payload.user);

					// อัปเดต state
					this._user = data.payload.user;
					this._refreshToken = data.payload.refreshToken;
					this.saveUserToStorage(data.payload.user, data.payload.refreshToken);
					this._isInitialized = true;

					console.log('Updated user:', $state.snapshot(this._user));
					console.log('isAuthenticated:', this.isAuthenticated);

					// ตรวจสอบว่า state ถูกอัปเดตจริงหรือไม่
					setTimeout(() => {
						console.log('User state after update:', $state.snapshot(this._user));
						console.log('isAuthenticated after update:', this.isAuthenticated);

						// Redirect ไป dashboard ถ้าอยู่ที่หน้า signin
						if (window.location.pathname === '/signin') {
							console.log('Redirecting to dashboard...');
							goto('/dashboard');
						}
					}, 100);
				}
				break;
			case 'signout':
				console.log('Syncing signout across tabs');
				console.log('Previous user:', $state.snapshot(this._user));

				this._user = null;
				this._refreshToken = null;
				this._accessToken = null;
				this.clearStorage();
				this._isInitialized = true;

				console.log('Updated user:', $state.snapshot(this._user));
				console.log('isAuthenticated:', this.isAuthenticated);

				goto('/signin');
				break;
			case 'token-refresh':
				if (data.payload) {
					console.log('Syncing token refresh across tabs');
					this._user = data.payload.user;
					this._refreshToken = data.payload.refreshToken;
					this._accessToken = data.payload.token;
					this.saveUserToStorage(data.payload.user, data.payload.refreshToken, data.payload.token);
				}
				break;
		}
	}

	private loadUserFromStorage() {
		try {
			// ✅ ลบการใช้ localStorage สำหรับ user และ refreshToken
			// ใช้เฉพาะ cookies ที่มี httpOnly สำหรับความปลอดภัย
			const cookies = document.cookie.split(';').reduce(
				(acc, cookie) => {
					const [key, value] = cookie.trim().split('=');
					acc[key] = value;
					return acc;
				},
				{} as Record<string, string>
			);

			const accessToken = cookies['auth_token'];
			const refreshToken = cookies['refreshToken'];

			console.log('AuthStore: Loading from cookies only', {
				hasAccessToken: !!accessToken,
				hasRefreshToken: !!refreshToken,
				cookies: Object.keys(cookies),
			});

			// ✅ ไม่โหลด user จาก localStorage แล้ว
			// user data จะมาจาก SSR หรือ API call เท่านั้น
			if (accessToken) {
				this._accessToken = accessToken;
				this._refreshToken = refreshToken;

				console.log('AuthStore: Loaded tokens from cookies', {
					hasAccessToken: !!this._accessToken,
					hasRefreshToken: !!this._refreshToken,
				});
			}
		} catch (error) {
			console.error('AuthStore: Error loading from cookies:', error);
			this.clearStorage();
		}
	}

	private saveUserToStorage(user: User, refreshToken: string, accessToken?: string) {
		if (browser) {
			// ✅ ลบการเก็บข้อมูลใน localStorage
			// ข้อมูล user และ tokens จะเก็บใน httpOnly cookies เท่านั้น
			// เก็บเฉพาะใน memory สำหรับการใช้งานใน client
			this._user = user;
			if (accessToken) {
				this._accessToken = accessToken;
			}
			this._refreshToken = refreshToken;

			console.log('AuthStore: User data saved to memory only (no localStorage)', {
				userId: user._id,
				hasAccessToken: !!accessToken,
				hasRefreshToken: !!refreshToken,
			});
		}
	}

	private clearStorage() {
		if (browser) {
			console.log('AuthStore: Clearing all storage and cookies');

			// ✅ ไม่ต้องลบ localStorage เพราะไม่ได้ใช้แล้ว

			// ลบ cookies อย่างละเอียด (ต้อง match กับ server-side)
			const cookiesToClear = [
				'auth_token',
				'refreshToken',
				'session_id',
				'session',
				'csrf_token',
				'remember_me',
			];

			cookiesToClear.forEach(cookieName => {
				console.log(`Client-side: Clearing cookie ${cookieName}`);

				// ลบ cookie หลายแบบเพื่อให้แน่ใจ
				const expireDate = 'Thu, 01 Jan 1970 00:00:00 UTC';

				// Basic deletion
				document.cookie = `${cookieName}=; expires=${expireDate}; path=/;`;

				// With current domain
				document.cookie = `${cookieName}=; expires=${expireDate}; path=/; domain=${window.location.hostname};`;

				// With dot domain (for subdomains)
				if (window.location.hostname.includes('.')) {
					const rootDomain = window.location.hostname.split('.').slice(-2).join('.');
					document.cookie = `${cookieName}=; expires=${expireDate}; path=/; domain=.${rootDomain};`;
				}

				// With secure and sameSite (for production)
				document.cookie = `${cookieName}=; expires=${expireDate}; path=/; secure; samesite=strict;`;
			});

			// ลบ state variables
			this._refreshToken = null;
			this._accessToken = null;

			console.log('AuthStore: Storage and cookies cleared');
		}
	}

	private setupTokenRefresh() {
		// ✅ Improved auto refresh configuration
		const AUTO_REFRESH_CONFIG = {
			// ลดความถี่ในการ refresh เพื่อประหยัด bandwidth และลด server load
			CHECK_INTERVAL: 2 * 60 * 60 * 1000, // ทุก 2 ชั่วโมง (แทน 30 นาที)

			// เพิ่มการตรวจสอบก่อน refresh
			MIN_TIME_BEFORE_REFRESH: 10 * 60 * 1000, // ไม่ refresh ถ้าเพิ่ง refresh ไปไม่ถึง 10 นาที
		};

		// ✅ ใช้ on-demand refresh แทน periodic refresh
		// ตรวจสอบ token เฉพาะเมื่อจำเป็น (เช่น เมื่อมี API call ที่ fail)
		this.lastRefreshTime = 0;

		// ✅ Setup visibility change listener สำหรับ refresh เมื่อ tab กลับมา active
		if (browser) {
			document.addEventListener('visibilitychange', () => {
				if (!document.hidden && this.isAuthenticated) {
					// ตรวจสอบว่าควร refresh หรือไม่เมื่อ tab กลับมา active
					const timeSinceLastRefresh = Date.now() - this.lastRefreshTime;
					if (timeSinceLastRefresh > AUTO_REFRESH_CONFIG.CHECK_INTERVAL) {
						this.checkAndRefreshToken();
					}
				}
			});
		}

		// ✅ Periodic check แต่ลดความถี่ลง
		setInterval(() => {
			// เช็คเฉพาะเมื่อ user active และมี authentication
			if (!document.hidden && this.isAuthenticated) {
				this.checkAndRefreshToken();
			}
		}, AUTO_REFRESH_CONFIG.CHECK_INTERVAL);
	}

	private async checkAndRefreshToken() {
		if (this._isRefreshing) return;

		// ✅ ตรวจสอบว่าเพิ่ง refresh ไปหรือไม่
		const timeSinceLastRefresh = Date.now() - this.lastRefreshTime;
		const MIN_TIME_BEFORE_REFRESH = 10 * 60 * 1000; // 10 นาที

		if (timeSinceLastRefresh < MIN_TIME_BEFORE_REFRESH) {
			console.log('AuthStore: Skipping refresh - too soon since last refresh');
			return;
		}

		try {
			this._isRefreshing = true;
			console.log('AuthStore: Starting automatic token refresh');

			// ใช้ authService แทนการเรียก API โดยตรง
			const result = await authService.refreshToken(this._refreshToken || '');

			if (result.success && result.data) {
				this._user = result.data.user;
				this._refreshToken = result.data.refreshToken;
				this._accessToken = result.data.token;
				this.lastRefreshTime = Date.now(); // ✅ อัปเดต lastRefreshTime

				this.saveUserToStorage(result.data.user, result.data.refreshToken, result.data.token);

				// ส่ง event ไปยังแท็บอื่น
				this.broadcastAuthEvent('token-refresh', {
					user: result.data.user,
					refreshToken: result.data.refreshToken,
					token: result.data.token,
				});

				console.log('AuthStore: Token refreshed successfully (automatic)');
			} else {
				// Token หมดอายุ แต่ไม่ redirect อัตโนมัติ
				console.log('AuthStore: Token refresh failed, but not redirecting automatically');
				// ไม่เรียก this.signout() อัตโนมัติ
			}
		} catch (error) {
			console.error('AuthStore: Token refresh failed:', error);
			// ไม่เรียก this.signout() อัตโนมัติ
		} finally {
			this._isRefreshing = false;
		}
	}

	// ✅ Enhanced manual refresh token with validation
	async refreshToken() {
		try {
			// ✅ ตรวจสอบว่ามี refresh token หรือไม่
			if (!this._refreshToken) {
				console.log('AuthStore: No refresh token available for manual refresh');
				this.signout();
				return false;
			}

			// ✅ ตรวจสอบ rate limiting ก่อน refresh
			const timeSinceLastRefresh = Date.now() - this.lastRefreshTime;
			const MIN_TIME_BEFORE_MANUAL_REFRESH = 5 * 60 * 1000; // 5 นาที

			if (timeSinceLastRefresh < MIN_TIME_BEFORE_MANUAL_REFRESH) {
				console.log('AuthStore: Manual refresh rate limited');
				return false;
			}

			console.log('AuthStore: Starting manual token refresh');

			// ใช้ authService แทนการเรียก API โดยตรง
			const result = await authService.refreshToken(this._refreshToken);

			if (result.success && result.data) {
				this._user = result.data.user;
				this._refreshToken = result.data.refreshToken;
				this.lastRefreshTime = Date.now(); // ✅ อัปเดต lastRefreshTime

				this.saveUserToStorage(result.data.user, result.data.refreshToken);

				// ส่ง event ไปยังแท็บอื่น
				this.broadcastAuthEvent('token-refresh', {
					user: result.data.user,
					refreshToken: result.data.refreshToken,
					token: result.data.token,
				});

				console.log('AuthStore: Token refreshed manually');
				return true;
			} else {
				console.log('AuthStore: Manual token refresh failed');
				// ✅ Log security event
				securityMonitor.logEvent('token_refresh_failed', 'medium', {
					type: 'manual',
					error: result.error || 'Unknown error',
				});
				this.signout();
				return false;
			}
		} catch (error) {
			console.error('AuthStore: Manual token refresh error:', error);
			// ✅ Log security event
			securityMonitor.logEvent('token_refresh_failed', 'high', {
				type: 'manual',
				error: error instanceof Error ? error.message : 'Unknown error',
			});
			this.signout();
			return false;
		}
	}

	async signin(credentials: SigninData): Promise<boolean> {
		const endTimer = measurePerformance('store_signin');
		this._isLoading = true;
		this._error = null;

		logger.info(LogCategory.AUTH, 'store_signin_attempt', 'User attempting signin from store', {
			email: credentials.email?.substring(0, 3) + '***',
			rememberMe: credentials.rememberMe,
			tabId: this._currentTabId,
		});

		try {
			// ใช้ authService แทนการเรียก API โดยตรง
			const result = await authService.signin(credentials);

			if (result.success && result.data) {
				this._user = result.data.user;
				this._accessToken = result.data.token;
				this.saveUserToStorage(result.data.user, result.data.refreshToken, result.data.token);

				// Cache user data
				if (result.data.user) {
					tokenCache.cacheUser(result.data.user._id, result.data.user);
					if (result.data.token) {
						tokenCache.cacheToken(result.data.user._id, result.data.token);
					}
				}

				// ส่ง event ไปยังแท็บอื่น
				this.broadcastAuthEvent('login', {
					user: result.data.user,
					refreshToken: result.data.refreshToken,
					token: result.data.token,
				});

				logger.info(
					LogCategory.AUTH,
					'store_signin_success',
					'User signed in successfully from store',
					{
						userId: result.data.user._id,
						sessionId: result.data.sessionId,
						tabId: this._currentTabId,
					}
				);

				// แสดง SweetAlert success
				showLoginSuccess();

				// Redirect ไป dashboard
				setTimeout(() => {
					goto('/dashboard');
				}, 1500);

				endTimer();
				return true;
			} else {
				throw new Error(result.error || 'Login failed');
			}
		} catch (error) {
			endTimer();

			const errorMessage = error instanceof Error ? error.message : 'Login failed';
			this._error = errorMessage;

			logger.error(LogCategory.AUTH, 'store_signin_error', 'Signin error in store', {
				error: errorMessage,
				tabId: this._currentTabId,
			});

			// แสดง SweetAlert error
			showLoginError(errorMessage);

			return false;
		} finally {
			this._isLoading = false;
		}
	}

	async signup(credentials: SignupData): Promise<boolean> {
		this._isLoading = true;
		this._error = null;

		try {
			// ใช้ authService แทนการเรียก API โดยตรง
			const result = await authService.signup(credentials);

			if (result.success && result.data) {
				this._user = result.data.user;
				this.saveUserToStorage(result.data.user, result.data.refreshToken);

				// ส่ง event ไปยังแท็บอื่น
				this.broadcastAuthEvent('login', {
					user: result.data.user,
					refreshToken: result.data.refreshToken,
				});

				// แสดง SweetAlert success
				showRegisterSuccess();

				// Redirect ไป dashboard
				setTimeout(() => {
					goto('/dashboard');
				}, 1500);

				return true;
			} else {
				throw new Error(result.error || 'Registration failed');
			}
		} catch (error) {
			const errorMessage = error instanceof Error ? error.message : 'Registration failed';
			this._error = errorMessage;

			// แสดง SweetAlert error
			showRegisterError(errorMessage);

			return false;
		} finally {
			this._isLoading = false;
		}
	}

	async signout() {
		console.log('AuthStore: Starting signout process', {
			hasUser: !!this._user,
			hasAccessToken: !!this._accessToken,
			hasRefreshToken: !!this._refreshToken,
		});

		this._isLoading = true;

		try {
			// ✅ Use server action for secure signout (preferred method)
			const formData = new FormData();

			// ✅ Add CSRF token if available
			const csrfToken = this.getCSRFToken();
			console.log('AuthStore: CSRF token:', csrfToken ? 'exists' : 'not found');
			if (csrfToken) {
				formData.append('csrf_token', csrfToken);
			}

			// ✅ Use authService for consistent error handling
			console.log('AuthStore: Calling authService.serverSignout()');
			const result = await authService.serverSignout(formData);
			console.log('AuthStore: serverSignout result:', result);

			if (result.success) {
				console.log('AuthStore: Server signout successful');
			} else {
				// Check if it's a redirect error (status 302/301)
				if (
					result.error?.includes('302') ||
					result.error?.includes('301') ||
					result.error?.includes('redirect')
				) {
					console.log('AuthStore: Server signout successful with redirect');
				} else {
					console.warn(
						'AuthStore: Server signout failed, falling back to client-side cleanup',
						result.error
					);
					throw new Error(result.error);
				}
			}

			// ✅ Clear local state immediately (for both success and redirect cases)
			this.clearAuthState();

			// ✅ Broadcast signout to other tabs
			this.broadcastAuthEvent('signout');

			// ✅ Show success message
			await showSignoutSuccess({
				timer: 100000,
			});

			// ✅ Redirect to signin
			goto('/signin');

			return;
		} catch (error) {
			console.error('AuthStore: Server signout error:', error);
		}

		// ✅ Fallback: Client-side cleanup if server signout fails
		try {
			// Try API signout as fallback
			const accessToken = this._accessToken || this.getTokenFromCookies();
			const refreshToken = this._refreshToken || this.getRefreshTokenFromCookies();

			if (accessToken || refreshToken) {
				// ✅ Convert null to undefined for TypeScript compatibility
				const result = await authService.signout(
					accessToken || undefined,
					refreshToken || undefined
				);
				console.log('AuthStore: API signout result', result);
			}
		} catch (error) {
			console.error('AuthStore: API signout failed:', error);
		} finally {
			// ✅ Always clear local state
			this.clearAuthState();

			// ✅ Broadcast signout to other tabs
			this.broadcastAuthEvent('signout');

			// ✅ Force redirect to signin
			window.location.href = '/signin?signout=client';
		}
	}

	/**
	 * ✅ Clear authentication state
	 */
	private clearAuthState() {
		console.log('AuthStore: Clearing authentication state');

		this._user = null;
		this._accessToken = null;
		this._refreshToken = null;
		this._isLoading = false;

		this.clearStorage();
	}

	/**
	 * ✅ Get CSRF token from cookies
	 */
	private getCSRFToken(): string | null {
		if (!browser) return null;

		const cookies = document.cookie.split(';').reduce(
			(acc, cookie) => {
				const [key, value] = cookie.trim().split('=');
				acc[key] = value;
				return acc;
			},
			{} as Record<string, string>
		);

		return cookies['csrf_token'] || null;
	}

	/**
	 * ✅ Get access token from cookies
	 */
	private getTokenFromCookies(): string | null {
		if (!browser) return null;

		const cookies = document.cookie.split(';').reduce(
			(acc, cookie) => {
				const [key, value] = cookie.trim().split('=');
				acc[key] = value;
				return acc;
			},
			{} as Record<string, string>
		);

		return cookies['auth_token'] || null;
	}

	/**
	 * ✅ Get refresh token from cookies
	 */
	private getRefreshTokenFromCookies(): string | null {
		if (!browser) return null;

		const cookies = document.cookie.split(';').reduce(
			(acc, cookie) => {
				const [key, value] = cookie.trim().split('=');
				acc[key] = value;
				return acc;
			},
			{} as Record<string, string>
		);

		return cookies['refreshToken'] || null;
	}

	async refreshUser() {
		try {
			// ใช้ authService แทนการเรียก API โดยตรง
			const result = await authService.getCurrentUser(this._refreshToken || '');

			if (result.success && result.data) {
				this._user = result.data;
				if (this._refreshToken) {
					this.saveUserToStorage(result.data, this._refreshToken);
				}
			} else {
				this.signout();
			}
		} catch (error) {
			console.error('Failed to refresh user:', error);
			this.signout();
		}
	}

	clearError() {
		this._error = null;
	}

	// ฟังก์ชันสำหรับอัปเดตข้อมูล user
	updateUser(user: User) {
		console.log('AuthStore: Updating user data', {
			oldUser: this._user,
			newUser: user,
			oldMoneyPoint: this._user?.moneyPoint,
			newMoneyPoint: user.moneyPoint,
		});

		this._user = user;
		if (this._refreshToken) {
			console.log('AuthStore: Saving user data to storage', {
				user: user,
				refreshToken: this._refreshToken,
			});
			this.saveUserToStorage(user, this._refreshToken);
		}

		// ส่ง event ไปยังแท็บอื่น
		this.broadcastAuthEvent('user-updated', { user });

		console.log('AuthStore: User updated successfully', {
			currentUser: this._user,
			moneyPoint: this._user?.moneyPoint,
		});
	}

	/**
	 * ✅ Check token health and validity
	 */
	async checkTokenHealth(): Promise<{
		isValid: boolean;
		needsRefresh: boolean;
		timeUntilExpiry?: number;
		error?: string;
	}> {
		try {
			// ตรวจสอบว่ามี tokens หรือไม่
			if (!this._accessToken && !this._refreshToken) {
				return {
					isValid: false,
					needsRefresh: false,
					error: 'No tokens available',
				};
			}

			// ตรวจสอบ access token ด้วยการเรียก API
			if (this._accessToken) {
				const result = await authService.getCurrentUser(this._accessToken);

				if (result.success) {
					return {
						isValid: true,
						needsRefresh: false,
					};
				}
			}

			// ถ้า access token ไม่ valid แต่มี refresh token
			if (this._refreshToken) {
				return {
					isValid: false,
					needsRefresh: true,
					error: 'Access token expired, refresh needed',
				};
			}

			return {
				isValid: false,
				needsRefresh: false,
				error: 'All tokens invalid',
			};
		} catch (error) {
			console.error('AuthStore: Token health check failed:', error);
			return {
				isValid: false,
				needsRefresh: !!this._refreshToken,
				error: error instanceof Error ? error.message : 'Health check failed',
			};
		}
	}

	/**
	 * ✅ Smart refresh - only refresh when needed
	 */
	async smartRefresh(): Promise<boolean> {
		const healthCheck = await this.checkTokenHealth();

		if (healthCheck.isValid) {
			console.log('AuthStore: Token is healthy, no refresh needed');
			return true;
		}

		if (healthCheck.needsRefresh) {
			console.log('AuthStore: Token needs refresh, attempting refresh');
			return await this.refreshToken();
		}

		console.log('AuthStore: Tokens are invalid, signing out');
		this.signout();
		return false;
	}

	// Cleanup method สำหรับ destroy store
	destroy() {
		// ไม่ปิด BroadcastChannel เพื่อให้ยังสามารถส่ง message ได้
		// BroadcastChannel จะถูกปิดอัตโนมัติเมื่อ tab ถูกปิด
		if (this._storageListener) {
			window.removeEventListener('storage', this._storageListener);
		}
	}
}

export const authStore = new AuthStore();
