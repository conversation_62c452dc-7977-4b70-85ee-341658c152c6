/**
 * Auto Refresh Configuration
 * การตั้งค่าเวลา auto refresh token
 */

export const AUTO_REFRESH_CONFIG = {
	// Frontend auto refresh settings
	FRONTEND: {
		// ตรวจสอบ token ทุก 5 นาที (default)
		CHECK_INTERVAL: 5 * 60 * 1000,

		// การตั้งค่าเพิ่มเติม
		// CHECK_INTERVAL: 1 * 60 * 1000, // ทุก 1 นาที (สำหรับความปลอดภัยสูง)
		// CHECK_INTERVAL: 10 * 60 * 1000, // ทุก 10 นาที (สำหรับประสิทธิภาพ)
		// CHECK_INTERVAL: 30 * 60 * 1000, // ทุก 30 นาที (สำหรับประหยัด bandwidth)
	},

	// Backend cleanup settings
	BACKEND: {
		// ลบ expired tokens ทุก 1 ชั่วโมง (default)
		CLEANUP_INTERVAL: 60 * 60 * 1000,

		// การตั้งค่าเพิ่มเติม
		// CLEANUP_INTERVAL: 30 * 60 * 1000, // ทุก 30 นาที (ลบเร็วขึ้น)
		// CLEANUP_INTERVAL: 2 * 60 * 60 * 1000, // ทุก 2 ชั่วโมง (ลบช้าลง)

		// ระยะเวลาก่อนลบ revoked tokens (7 วัน)
		REVOKED_TOKEN_CLEANUP_DELAY: 7 * 24 * 60 * 60 * 1000,
	},

	// Token rotation settings
	TOKEN_ROTATION: {
		// ระยะเวลาสูงสุดของ refresh token (30 วัน)
		TOKEN_LIFETIME: 30 * 24 * 60 * 60 * 1000,

		// ระยะเวลาก่อนจะ rotate token (24 ชั่วโมง)
		ROTATION_THRESHOLD: 24 * 60 * 60 * 1000,

		// จำนวนครั้งสูงสุดที่สามารถ rotate token ได้
		MAX_ROTATION_COUNT: 10,

		// ความยาวของ refresh token (bytes)
		REFRESH_TOKEN_LENGTH: 64,
	},

	// JWT settings (แบบเก่า)
	JWT: {
		// Access token expiration (1 ชั่วโมง)
		ACCESS_TOKEN_EXPIRES_IN: '1h',

		// Refresh token expiration (7 วัน)
		REFRESH_TOKEN_EXPIRES_IN: '7d',
	},
} as const;

/**
 * ประเภทการตั้งค่า auto refresh
 */
export type AutoRefreshType = 'high_security' | 'balanced' | 'performance' | 'convenience';

/**
 * ฟังก์ชันสำหรับตั้งค่า auto refresh ตามประเภท
 */
export function getAutoRefreshConfig(type: AutoRefreshType = 'balanced') {
	switch (type) {
		case 'high_security':
			return {
				frontend: {
					CHECK_INTERVAL: 1 * 60 * 1000, // ทุก 1 นาที
				},
				backend: {
					CLEANUP_INTERVAL: 30 * 60 * 1000, // ทุก 30 นาที
					REVOKED_TOKEN_CLEANUP_DELAY: 1 * 24 * 60 * 60 * 1000, // 1 วัน
				},
				tokenRotation: {
					TOKEN_LIFETIME: 7 * 24 * 60 * 60 * 1000, // 7 วัน
					ROTATION_THRESHOLD: 12 * 60 * 60 * 1000, // 12 ชั่วโมง
				},
			};

		case 'performance':
			return {
				frontend: {
					CHECK_INTERVAL: 30 * 60 * 1000, // ทุก 30 นาที
				},
				backend: {
					CLEANUP_INTERVAL: 2 * 60 * 60 * 1000, // ทุก 2 ชั่วโมง
					REVOKED_TOKEN_CLEANUP_DELAY: 14 * 24 * 60 * 60 * 1000, // 14 วัน
				},
				tokenRotation: {
					TOKEN_LIFETIME: 90 * 24 * 60 * 60 * 1000, // 90 วัน
					ROTATION_THRESHOLD: 7 * 24 * 60 * 60 * 1000, // 7 วัน
				},
			};

		case 'convenience':
			return {
				frontend: {
					CHECK_INTERVAL: 60 * 60 * 1000, // ทุก 1 ชั่วโมง
				},
				backend: {
					CLEANUP_INTERVAL: 4 * 60 * 60 * 1000, // ทุก 4 ชั่วโมง
					REVOKED_TOKEN_CLEANUP_DELAY: 30 * 24 * 60 * 60 * 1000, // 30 วัน
				},
				tokenRotation: {
					TOKEN_LIFETIME: 365 * 24 * 60 * 60 * 1000, // 365 วัน
					ROTATION_THRESHOLD: 30 * 24 * 60 * 60 * 1000, // 30 วัน
				},
			};

		default: // balanced
			return {
				frontend: {
					CHECK_INTERVAL: 5 * 60 * 1000, // ทุก 5 นาที
				},
				backend: {
					CLEANUP_INTERVAL: 60 * 60 * 1000, // ทุก 1 ชั่วโมง
					REVOKED_TOKEN_CLEANUP_DELAY: 7 * 24 * 60 * 60 * 1000, // 7 วัน
				},
				tokenRotation: {
					TOKEN_LIFETIME: 30 * 24 * 60 * 60 * 1000, // 30 วัน
					ROTATION_THRESHOLD: 24 * 60 * 60 * 1000, // 24 ชั่วโมง
				},
			};
	}
}
