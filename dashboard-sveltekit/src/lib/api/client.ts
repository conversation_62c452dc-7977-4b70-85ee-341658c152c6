import { apiUrl } from '$lib/config';
import { <PERSON><PERSON>r<PERSON>andler } from '$lib/utils/error-handler';

// ✅ Base API client with native fetch
export interface ApiClientOptions {
	method?: string;
	headers?: Record<string, string>;
	body?: any;
	timeout?: number;
	retry?: number;
	retryDelay?: number;
}

class ApiClient {
	private baseURL: string;
	private defaultHeaders: Record<string, string>;
	private timeout: number;
	private retry: number;
	private retryDelay: number;

	constructor() {
		this.baseURL = apiUrl;
		this.defaultHeaders = {
			'Content-Type': 'application/json',
		};
		this.timeout = 30000;
		this.retry = 1;
		this.retryDelay = 1000;
	}

	async request<T>(endpoint: string, options: ApiClientOptions = {}): Promise<T> {
		const url = `${this.baseURL}${endpoint}`;
		const method = options.method || 'GET';

		// Merge headers
		const headers = {
			...this.defaultHeaders,
			...options.headers,
		};

		// Prepare body
		let body: string | undefined;
		if (options.body && method !== 'GET') {
			body = typeof options.body === 'string' ? options.body : JSON.stringify(options.body);
		}

		// Global request interceptor
		console.log('🚀 API Request:', method, url);

		let lastError: Error | null = null;

		// Disable retry for auth endpoints to prevent duplicate calls
		const isAuthEndpoint =
			endpoint.includes('/user/signin') ||
			endpoint.includes('/user/signup') ||
			endpoint.includes('/user/signout');

		// Smart retry strategy:
		// - No retry for auth endpoints (prevent duplicate calls)
		// - No retry for client errors (4xx)
		// - Retry only for server errors (5xx) and network issues
		const maxRetries = isAuthEndpoint ? 0 : (options.retry ?? this.retry);

		for (let attempt = 0; attempt <= maxRetries; attempt++) {
			try {
				const controller = new AbortController();
				const timeoutId = setTimeout(() => controller.abort(), options.timeout ?? this.timeout);

				const response = await fetch(url, {
					method,
					headers,
					body,
					signal: controller.signal,
				});

				clearTimeout(timeoutId);

				// Global response interceptor
				console.log('📡 API Response:', response.status, response.url);

				if (!response.ok) {
					let errorData: any;
					try {
						errorData = await response.json();
					} catch {
						errorData = { message: response.statusText };
					}

					// Global error interceptor - ปรับปรุงการแสดง error
					const errorMessage =
						errorData?.message || errorData?.error || `HTTP ${response.status} Error`;

					console.error('❌ API Error:', {
						status: response.status,
						url: response.url,
						data: errorData,
						message: errorMessage,
					});

					// ใช้ ErrorHandler สำหรับการจัดการ error ที่สอดคล้องกัน
					const userMessage = ErrorHandler.handleApiError(
						new Error(errorMessage),
						`HTTP ${response.status}`
					);

					// สร้าง Error object ที่มี message ที่ชัดเจน
					const apiError = new Error(userMessage);
					(apiError as any).status = response.status;
					(apiError as any).data = errorData;

					// Don't retry for client errors (4xx) - these are usually user errors
					if (response.status >= 400 && response.status < 500) {
						throw apiError;
					}

					// Only retry for server errors (5xx) and network issues
					if (response.status >= 500) {
						if (attempt < maxRetries) {
							console.log(
								`🔄 Retrying due to server error (${response.status}) - attempt ${attempt + 1}/${maxRetries + 1}`
							);
							await new Promise(resolve =>
								setTimeout(resolve, options.retryDelay ?? this.retryDelay)
							);
							continue;
						}
					}

					throw apiError;
				}

				return await response.json();
			} catch (error: unknown) {
				lastError = error instanceof Error ? error : new Error('Unknown error');

				// Only retry for network errors, not for HTTP errors
				if (lastError.name === 'AbortError' || lastError.message.includes('fetch')) {
					if (attempt < maxRetries) {
						console.log(
							`🔄 Retrying due to network error - attempt ${attempt + 1}/${maxRetries + 1}`
						);
						await new Promise(resolve =>
							setTimeout(resolve, options.retryDelay ?? this.retryDelay)
						);
						continue;
					}
				}

				// ใช้ ErrorHandler สำหรับการจัดการ error ที่สอดคล้องกัน
				const userMessage = ErrorHandler.handleApiError(lastError, 'Request failed');
				throw new Error(userMessage);
			}
		}

		throw lastError || new Error('Request failed');
	}
}

export const apiClient = new ApiClient();

// ✅ Helper function to create auth headers
export function createAuthHeaders(token: string) {
	return {
		Authorization: `Bearer ${token}`,
	};
}

// ✅ Helper function to get token (for client-side)
export function getToken(): string | null {
	if (typeof window !== 'undefined') {
		return localStorage.getItem('auth_token');
	}
	return null;
}
