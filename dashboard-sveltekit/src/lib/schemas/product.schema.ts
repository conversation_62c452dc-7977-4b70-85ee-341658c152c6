import { z } from 'zod';

// ✅ Zod Schemas
export const productVariantSchema = z.object({
	name: z.string().min(1, 'กรุณากรอกชื่อตัวเลือก').max(100, 'ชื่อตัวเลือกต้องไม่เกิน 100 ตัวอักษร'),
	price: z.number().min(0, 'ราคาต้องไม่น้อยกว่า 0'),
	stock: z.number().int().min(0, 'สต็อกต้องไม่น้อยกว่า 0').max(999999, 'สต็อกต้องไม่เกิน 999,999'),
	sku: z.string().optional(),
});

export const createProductSchema = z.object({
	name: z
		.string()
		.min(2, 'ชื่อสินค้าต้องมีอย่างน้อย 2 ตัวอักษร')
		.max(100, 'ชื่อสินค้าต้องไม่เกิน 100 ตัวอักษร'),
	description: z.string().max(1000, 'คำอธิบายต้องไม่เกิน 1000 ตัวอักษร').optional(),
	price: z.number().min(0, 'ราคาต้องไม่น้อยกว่า 0'),
	stock: z.number().int().min(0, 'สต็อกต้องไม่น้อยกว่า 0').max(999999, 'สต็อกต้องไม่เกิน 999,999'),
	categoryId: z.string().optional(),
	images: z.array(z.string().url('รูปแบบ URL ไม่ถูกต้อง')).optional(),
	isActive: z.boolean().optional().default(true),
	isDigital: z.boolean().optional().default(false),
	variants: z.array(productVariantSchema).optional(),
});

export const updateProductSchema = createProductSchema.partial().extend({
	id: z.string().optional(),
});

export const productStockSchema = z.object({
	stock: z.number().int().min(0, 'สต็อกต้องไม่น้อยกว่า 0').max(999999, 'สต็อกต้องไม่เกิน 999,999'),
	variantId: z.string().optional(),
});

// ✅ Type inference from schemas
export type CreateProductData = z.infer<typeof createProductSchema>;
export type UpdateProductData = z.infer<typeof updateProductSchema>;
export type ProductStockData = z.infer<typeof productStockSchema>;
export type ProductVariantData = z.infer<typeof productVariantSchema>;

// ✅ Validation Functions using Zod
export function validateCreateProductData(data: unknown): {
	success: boolean;
	data?: CreateProductData;
	error?: string;
} {
	const result = createProductSchema.safeParse(data);
	if (result.success) {
		return { success: true, data: result.data };
	}

	const firstError = result.error.issues[0];
	return { success: false, error: firstError?.message || 'ข้อมูลไม่ถูกต้อง' };
}

export function validateUpdateProductData(data: unknown): {
	success: boolean;
	data?: UpdateProductData;
	error?: string;
} {
	const result = updateProductSchema.safeParse(data);
	if (result.success) {
		return { success: true, data: result.data };
	}

	const firstError = result.error.issues[0];
	return { success: false, error: firstError?.message || 'ข้อมูลไม่ถูกต้อง' };
}

export function validateProductStockData(data: unknown): {
	success: boolean;
	data?: ProductStockData;
	error?: string;
} {
	const result = productStockSchema.safeParse(data);
	if (result.success) {
		return { success: true, data: result.data };
	}

	const firstError = result.error.issues[0];
	return { success: false, error: firstError?.message || 'ข้อมูลไม่ถูกต้อง' };
}

// ✅ Sanitization Functions
export function sanitizeProductData(
	data: CreateProductData | UpdateProductData
): CreateProductData | UpdateProductData {
	const sanitized = { ...data };

	if (sanitized.name) {
		sanitized.name = sanitized.name.trim();
	}

	if (sanitized.description) {
		sanitized.description = sanitized.description.trim();
	}

	if (sanitized.price !== undefined) {
		sanitized.price = Number(sanitized.price);
	}

	if (sanitized.stock !== undefined) {
		sanitized.stock = Number(sanitized.stock);
	}

	if (sanitized.images?.length) {
		sanitized.images = sanitized.images.filter(img => img && img.trim());
	}

	return sanitized;
}

// ✅ Type Guards using Zod
export function isValidProductData(data: unknown): data is CreateProductData {
	return createProductSchema.safeParse(data).success;
}

export function isValidProductStockData(data: unknown): data is ProductStockData {
	return productStockSchema.safeParse(data).success;
}
