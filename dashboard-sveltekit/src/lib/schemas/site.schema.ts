// ✅ Site Schema - Types และ Validation

// Site Form Data Types
export interface CreateSiteData {
	siteName: string;
	typeDomain: 'subdomain' | 'custom';
	subDomain?: string;
	mainDomain?: string;
	customDomain?: string;
	packageType: string;
}

export interface CheckDomainData {
	typeDomain: 'subdomain' | 'custom';
	subDomain?: string;
	mainDomain?: string;
	customDomain?: string;
}

export interface UpdateSiteData {
	siteName?: string;
	description?: string;
	logo?: string;
	favicon?: string;
	customCss?: string;
	customJs?: string;
}

export interface SitePaginationParams {
	page?: string;
	limit?: string;
	search?: string;
	status?: string;
}

// Validation Functions
export function validateCreateSiteData(data: CreateSiteData): string | null {
	// Site name validation
	if (!data.siteName?.trim()) {
		return 'กรุณากรอกชื่อเว็บไซต์';
	}

	if (data.siteName.trim().length < 3) {
		return 'ชื่อเว็บไซต์ต้องมีอย่างน้อย 3 ตัวอักษร';
	}

	if (data.siteName.trim().length > 100) {
		return 'ชื่อเว็บไซต์ต้องไม่เกิน 100 ตัวอักษร';
	}

	// Domain validation
	const domainError = validateDomainData({
		typeDomain: data.typeDomain,
		subDomain: data.subDomain,
		mainDomain: data.mainDomain,
		customDomain: data.customDomain,
	});

	if (domainError) {
		return domainError;
	}

	// Package validation
	if (!data.packageType?.trim()) {
		return 'กรุณาเลือกแพ็คเกจ';
	}

	return null;
}

export function validateDomainData(data: CheckDomainData): string | null {
	if (!data.typeDomain) {
		return 'กรุณาเลือกประเภทโดเมน';
	}

	if (data.typeDomain === 'subdomain') {
		if (!data.subDomain?.trim()) {
			return 'กรุณากรอก subdomain';
		}

		if (!data.mainDomain?.trim()) {
			return 'กรุณาเลือก main domain';
		}

		// Validate subdomain format
		const subdomainRegex = /^[a-z0-9]([a-z0-9-]*[a-z0-9])?$/;
		if (!subdomainRegex.test(data.subDomain.trim())) {
			return 'Subdomain ต้องประกอบด้วยตัวอักษรภาษาอังกฤษ ตัวเลข และ - เท่านั้น';
		}

		if (data.subDomain.trim().length < 3) {
			return 'Subdomain ต้องมีอย่างน้อย 3 ตัวอักษร';
		}

		if (data.subDomain.trim().length > 50) {
			return 'Subdomain ต้องไม่เกิน 50 ตัวอักษร';
		}
	}

	if (data.typeDomain === 'custom') {
		if (!data.customDomain?.trim()) {
			return 'กรุณากรอก custom domain';
		}

		// Validate custom domain format
		const domainRegex =
			/^[a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?)*$/;
		if (!domainRegex.test(data.customDomain.trim())) {
			return 'รูปแบบ custom domain ไม่ถูกต้อง';
		}
	}

	return null;
}

export function validateUpdateSiteData(data: UpdateSiteData): string | null {
	if (data.siteName !== undefined) {
		if (!data.siteName.trim()) {
			return 'ชื่อเว็บไซต์ไม่สามารถเป็นค่าว่างได้';
		}

		if (data.siteName.trim().length < 3) {
			return 'ชื่อเว็บไซต์ต้องมีอย่างน้อย 3 ตัวอักษร';
		}

		if (data.siteName.trim().length > 100) {
			return 'ชื่อเว็บไซต์ต้องไม่เกิน 100 ตัวอักษร';
		}
	}

	if (data.description !== undefined && data.description.length > 500) {
		return 'คำอธิบายต้องไม่เกิน 500 ตัวอักษร';
	}

	return null;
}

export function validatePaginationParams(params: SitePaginationParams): SitePaginationParams {
	return {
		page: params.page && /^\d+$/.test(params.page) ? params.page : '1',
		limit:
			params.limit && /^\d+$/.test(params.limit) && parseInt(params.limit) <= 100
				? params.limit
				: '10',
		search: params.search?.trim() || '',
		status:
			params.status && ['active', 'inactive', 'suspended'].includes(params.status)
				? params.status
				: '',
	};
}

// Helper Functions
export function sanitizeSiteData<T extends Record<string, any>>(data: T): T {
	const sanitized = { ...data };

	// Trim string fields
	Object.keys(sanitized).forEach(key => {
		if (typeof sanitized[key] === 'string') {
			(sanitized as any)[key] = (sanitized[key] as string).trim();
		}
	});

	return sanitized;
}

export function buildDomainUrl(data: CheckDomainData): string {
	if (data.typeDomain === 'subdomain' && data.subDomain && data.mainDomain) {
		return `${data.subDomain}.${data.mainDomain}`;
	}

	if (data.typeDomain === 'custom' && data.customDomain) {
		return data.customDomain;
	}

	return '';
}
