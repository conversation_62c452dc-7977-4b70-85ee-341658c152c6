import { z } from 'zod';

// ✅ Zod Schemas
export const createCategorySchema = z.object({
	name: z
		.string()
		.min(2, 'ชื่อหมวดหมู่ต้องมีอย่างน้อย 2 ตัวอักษร')
		.max(50, 'ชื่อหมวดหมู่ต้องไม่เกิน 50 ตัวอักษร'),
	description: z.string().max(500, 'คำอธิบายต้องไม่เกิน 500 ตัวอักษร').optional(),
	parentId: z.string().optional(),
	isActive: z.boolean().optional().default(true),
	image: z.string().url('รูปแบบ URL ไม่ถูกต้อง').optional(),
	slug: z.string().max(100, 'Slug ต้องไม่เกิน 100 ตัวอักษร').optional(),
});

export const updateCategorySchema = createCategorySchema.partial().extend({
	id: z.string().optional(),
});

// ✅ Type inference from schemas
export type CreateCategoryData = z.infer<typeof createCategorySchema>;
export type UpdateCategoryData = z.infer<typeof updateCategorySchema>;

// ✅ Validation Functions using Zod
export function validateCreateCategoryData(data: unknown): {
	success: boolean;
	data?: CreateCategoryData;
	error?: string;
} {
	const result = createCategorySchema.safeParse(data);
	if (result.success) {
		return { success: true, data: result.data };
	}

	const firstError = result.error.issues[0];
	return { success: false, error: firstError?.message || 'ข้อมูลไม่ถูกต้อง' };
}

export function validateUpdateCategoryData(data: unknown): {
	success: boolean;
	data?: UpdateCategoryData;
	error?: string;
} {
	const result = updateCategorySchema.safeParse(data);
	if (result.success) {
		return { success: true, data: result.data };
	}

	const firstError = result.error.issues[0];
	return { success: false, error: firstError?.message || 'ข้อมูลไม่ถูกต้อง' };
}

// ✅ Sanitization Functions
export function sanitizeCategoryData(
	data: CreateCategoryData | UpdateCategoryData
): CreateCategoryData | UpdateCategoryData {
	const sanitized = { ...data };

	if (sanitized.name) {
		sanitized.name = sanitized.name.trim();
	}

	if (sanitized.description) {
		sanitized.description = sanitized.description.trim();
	}

	if (sanitized.slug) {
		sanitized.slug = sanitized.slug.trim().toLowerCase().replace(/\s+/g, '-');
	}

	return sanitized;
}

// ✅ Type Guards using Zod
export function isValidCategoryData(data: unknown): data is CreateCategoryData {
	return createCategorySchema.safeParse(data).success;
}

export function isValidUpdateCategoryData(data: unknown): data is UpdateCategoryData {
	return updateCategorySchema.safeParse(data).success;
}
