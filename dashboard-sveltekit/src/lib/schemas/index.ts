// ✅ Schemas Index - Export ทุกอย่างจากที่เดียว

// ✅ Re-export specific types to avoid conflicts
export type {
	AuthResponse,
	ChangePasswordData as AuthChangePasswordData,
	ForgotPasswordData,  
	ProfileUpdateData as AuthProfileUpdateData,
	RefreshTokenResponse, 
	ResetPasswordData,
	// Auth Types
	SigninData,
	SignupData,
	VerifyEmailData,
} from './auth.schema';
// ✅ Export schemas (avoiding conflicts)
// ✅ Re-export validation functions
export {
	changePasswordSchema as authChangePasswordSchema,
	forgotPasswordSchema,
	isValidEmail as isValidAuthEmail,
	loginSchema,
	passwordResetSchema,
	profileUpdateSchema,
	registerSchema,
	resetPasswordSchema,
	sanitizeAuthData,
	signinSchema,
	signupSchema,
	validateChangePasswordData,
	validateForgotPasswordData,
	validateLoginForm,
	validateProfileUpdateData,
	validateRegisterForm,
	validateResetPasswordData,
	// Auth validations
	validateSigninData,
	validateSigninForm,
	validateSignupData,
	validateSignupForm,
	verifyEmailSchema,
} from './auth.schema';
export type { CreateCategoryData, UpdateCategoryData } from './category.schema';
export {
	createCategorySchema,
	isValidCategoryData,
	isValidUpdateCategoryData,
	sanitizeCategoryData,
	updateCategorySchema,
	// Category validations
	validateCreateCategoryData,
	validateUpdateCategoryData,
} from './category.schema';
export type {
	AddressData,
	CheckDomainData as CommonCheckDomainData,
	CreateSiteData as CommonCreateSiteData,
	FileUploadData,
	PaginationData,
	SearchData,
	SettingsData,
	SocialMediaData,
} from './common.schema';
export {
	addressSchema,
	checkDomainSchema as commonCheckDomainSchema,
	createSiteSchema as commonCreateSiteSchema,
	emailSchema,
	fileUploadSchema,
	isValidAddressData,
	isValidCheckDomainData,
	isValidCreateSiteData,
	isValidEmail,
	isValidFileUploadData,
	isValidSearchData,
	isValidSettingsData,
	isValidSocialMediaData,
	isValidUrl,
	nameSchema,
	paginationSchema,
	passwordSchema,
	phoneSchema,
	sanitizeStringData,
	searchSchema,
	settingsSchema,
	socialMediaSchema,
	urlSchema,
	validateAddressForm,
	validateCheckDomainForm,
	validateCreateSiteForm,
	validateFileUploadForm,
	// Common validations
	validateSearchForm,
	validateSettingsForm,
	validateSocialMediaForm,
} from './common.schema';
export type { CreateCustomerData, UpdateCustomerData } from './customer.schema';
export {
	createCustomerSchema,
	isValidCustomerData,
	isValidUpdateCustomerData,
	sanitizeCustomerData,
	updateCustomerSchema,
	// Customer validations
	validateCreateCustomerData,
	validateUpdateCustomerData,
} from './customer.schema';
export type {
	CreateOrderData,
	OrderItemData,
	ShippingAddressData,
	UpdateOrderData,
} from './order.schema';
export {
	createOrderSchema,
	isValidOrderData,
	isValidOrderItemData,
	isValidShippingAddressData,
	isValidUpdateOrderData,
	sanitizeOrderData,
	updateOrderSchema,
	// Order validations
	validateCreateOrderData,
	validateUpdateOrderData,
} from './order.schema';
export type {
	CreateProductData,
	ProductStockData,
	ProductVariantData,
	UpdateProductData,
} from './product.schema';
export {
	createProductSchema,
	isValidProductData,
	isValidProductStockData,
	productStockSchema,
	productVariantSchema,
	sanitizeProductData,
	updateProductSchema,
	// Product validations
	validateCreateProductData,
	validateProductStockData,
	validateUpdateProductData,
} from './product.schema';
export type {
	CheckDomainData as SiteCheckDomainData,
	CreateSiteData as SiteCreateSiteData,
	SitePaginationParams,
	UpdateSiteData,
} from './site.schema';
export {
	buildDomainUrl,
	sanitizeSiteData,
	// Site validations
	validateCreateSiteData,
	validateDomainData,
	validatePaginationParams,
	validateUpdateSiteData,
} from './site.schema';
export type {
	ChangePasswordData as UserChangePasswordData,
	UpdateAvatarData,
	UpdateProfileData,
} from './user.schema';
export {
	changePasswordSchema as userChangePasswordSchema,
	formatUserDisplayName,
	getUserInitials,
	isValidChangePasswordData,
	isValidUpdateAvatarData,
	isValidUpdateProfileData,
	sanitizeUserData,
	updateAvatarSchema,
	updateProfileSchema,
	validateAvatarFile,
	validateChangePasswordData as validateUserChangePasswordData,
	// User validations
	validateUpdateProfileData,
} from './user.schema';
