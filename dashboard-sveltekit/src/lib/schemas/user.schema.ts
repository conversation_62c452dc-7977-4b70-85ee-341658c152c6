// ✅ User Schema - Zod Validation
import { z } from 'zod';

// ✅ Zod Schemas
export const updateProfileSchema = z.object({
	firstName: z
		.string()
		.min(1, 'ชื่อจริงไม่สามารถเป็นค่าว่างได้')
		.max(50, 'ชื่อจริงต้องไม่เกิน 50 ตัวอักษร')
		.optional(),
	lastName: z
		.string()
		.min(1, 'นามสกุลไม่สามารถเป็นค่าว่างได้')
		.max(50, 'นามสกุลต้องไม่เกิน 50 ตัวอักษร')
		.optional(),
	avatar: z.string().url('รูปแบบ URL ไม่ถูกต้อง').optional(),
	cover: z.string().url('รูปแบบ URL ไม่ถูกต้อง').optional(),
});

export const changePasswordSchema = z
	.object({
		currentPassword: z.string().min(1, 'กรุณากรอกรหัสผ่านปัจจุบัน'),
		newPassword: z
			.string()
			.min(6, 'รหัสผ่านใหม่ต้องมีอย่างน้อย 6 ตัวอักษร')
			.max(128, 'รหัสผ่านใหม่ต้องไม่เกิน 128 ตัวอักษร'),
		confirmPassword: z.string().optional(),
	})
	.refine(data => !data.confirmPassword || data.newPassword === data.confirmPassword, {
		message: 'รหัสผ่านใหม่ไม่ตรงกัน',
		path: ['confirmPassword'],
	})
	.refine(data => data.currentPassword !== data.newPassword, {
		message: 'รหัสผ่านใหม่ต้องแตกต่างจากรหัสผ่านปัจจุบัน',
		path: ['newPassword'],
	});

export const updateAvatarSchema = z.object({
	avatar: z
		.instanceof(File)
		.refine(file => ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'].includes(file.type), {
			message: 'รองรับเฉพาะไฟล์รูปภาพ (JPEG, PNG, WebP)',
		})
		.refine(file => file.size <= 5 * 1024 * 1024, {
			message: 'ขนาดไฟล์ต้องไม่เกิน 5MB',
		}),
});

// ✅ Type inference from schemas
export type UpdateProfileData = z.infer<typeof updateProfileSchema>;
export type ChangePasswordData = z.infer<typeof changePasswordSchema>;
export type UpdateAvatarData = z.infer<typeof updateAvatarSchema>;

// ✅ Validation Functions using Zod
export function validateUpdateProfileData(data: unknown): {
	success: boolean;
	data?: UpdateProfileData;
	error?: string;
} {
	const result = updateProfileSchema.safeParse(data);
	if (result.success) {
		return { success: true, data: result.data };
	}

	const firstError = result.error.issues[0];
	return { success: false, error: firstError?.message || 'ข้อมูลไม่ถูกต้อง' };
}

export function validateChangePasswordData(data: unknown): {
	success: boolean;
	data?: ChangePasswordData;
	error?: string;
} {
	const result = changePasswordSchema.safeParse(data);
	if (result.success) {
		return { success: true, data: result.data };
	}

	const firstError = result.error.issues[0];
	return { success: false, error: firstError?.message || 'ข้อมูลไม่ถูกต้อง' };
}

export function validateAvatarFile(file: File): {
	success: boolean;
	error?: string;
} {
	const result = updateAvatarSchema.safeParse({ avatar: file });
	if (result.success) {
		return { success: true };
	}

	const firstError = result.error.issues[0];
	return { success: false, error: firstError?.message || 'ไฟล์ไม่ถูกต้อง' };
}

// ✅ Sanitization Functions
export function sanitizeUserData<T extends Record<string, any>>(data: T): T {
	const sanitized = { ...data };

	// Trim string fields
	Object.keys(sanitized).forEach(key => {
		if (typeof sanitized[key] === 'string') {
			(sanitized as any)[key] = (sanitized[key] as string).trim();
		}
	});

	return sanitized;
}

export function formatUserDisplayName(user: any): string {
	if (user.firstName && user.lastName) {
		return `${user.firstName} ${user.lastName}`;
	}

	if (user.firstName) {
		return user.firstName;
	}

	if (user.lastName) {
		return user.lastName;
	}

	return user.email?.split('@')[0] || 'Unknown'; // ใช้ส่วนแรกของอีเมลเป็นชื่อ
}

export function getUserInitials(user: any): string {
	if (user.firstName && user.lastName) {
		return `${user.firstName.charAt(0)}${user.lastName.charAt(0)}`.toUpperCase();
	}

	if (user.firstName) {
		return user.firstName.charAt(0).toUpperCase();
	}

	if (user.lastName) {
		return user.lastName.charAt(0).toUpperCase();
	}

	return user.email?.charAt(0).toUpperCase() || 'U';
}

// ✅ Type Guards using Zod
export function isValidUpdateProfileData(data: unknown): data is UpdateProfileData {
	return updateProfileSchema.safeParse(data).success;
}

export function isValidChangePasswordData(data: unknown): data is ChangePasswordData {
	return changePasswordSchema.safeParse(data).success;
}

export function isValidUpdateAvatarData(data: unknown): data is UpdateAvatarData {
	return updateAvatarSchema.safeParse(data).success;
}
