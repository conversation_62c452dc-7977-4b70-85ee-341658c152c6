<script lang="ts">
	import type { Snippet } from 'svelte';
	import { onMount } from 'svelte';
	import { cubicOut, quintOut } from 'svelte/easing';
	import { fade, fly, scale } from 'svelte/transition';
	import { loadingConfig } from '$lib/config/loading';

	interface Props {
		children: Snippet;
		isLoading?: boolean;
		type?: 'spinner' | 'dots' | 'bars' | 'pulse' | 'skeleton' | 'progress';
		size?: 'sm' | 'md' | 'lg' | 'xl';
		color?: 'primary' | 'secondary' | 'accent' | 'info' | 'success' | 'warning' | 'error';
		message?: string;
		showProgress?: boolean;
		progress?: number;
		overlay?: boolean;
		blur?: boolean;
		animation?: 'fade' | 'slide-up' | 'scale' | 'none';
		duration?: number;
		disabled?: boolean;
	}

	const {
		children,
		isLoading = false,
		type = loadingConfig.type,
		size = loadingConfig.size,
		color = loadingConfig.color,
		message = loadingConfig.message,
		showProgress = loadingConfig.showProgress,
		progress = 0,
		overlay = loadingConfig.overlay,
		blur = loadingConfig.blur,
		animation = loadingConfig.animation,
		duration = loadingConfig.duration,
		disabled = loadingConfig.disabled,
	}: Props = $props();

	// Loading state management
	let mounted = $state(false);
	const showLoading = $derived(isLoading && !disabled);
	let currentProgress = $state(progress);

	// Animation parameters
	const getAnimationParams = () => {
		const baseParams = {
			duration,
			easing: cubicOut,
		};

		switch (animation) {
			case 'slide-up':
				return { ...baseParams, y: 20 };
			case 'scale':
				return { ...baseParams, start: 0.8 };
			case 'fade':
			default:
				return baseParams;
		}
	};

	// Progress animation
	$effect(() => {
		if (showProgress && progress !== currentProgress) {
			const increment = progress > currentProgress ? 1 : -1;
			const interval = setInterval(() => {
				if (currentProgress === progress) {
					clearInterval(interval);
					return;
				}
				currentProgress += increment;
			}, 10);

			return () => clearInterval(interval);
		}
	});

	onMount(() => {
		mounted = true;
	});

	// Loading component classes
	const getLoadingClasses = () => {
		const baseClass = 'loading';
		const typeClass = `loading-${type}`;
		const sizeClass = `loading-${size}`;
		const colorClass = color !== 'primary' ? `text-${color}` : '';

		return `${baseClass} ${typeClass} ${sizeClass} ${colorClass}`.trim();
	};

	const getContainerClasses = () => {
		let classes = 'loading-container';

		if (overlay) classes += ' loading-overlay';
		if (blur) classes += ' loading-blur';

		return classes;
	};
</script>

{#if showLoading && mounted}
	<div
		class={getContainerClasses()}
		in:fade|global={{ duration, easing: cubicOut }}
		out:fade|global={{ duration: duration * 0.6, easing: quintOut }}
	>
		<div class="loading-content">
			<!-- Loading Icon/Animation -->
			<div class="loading-icon">
				{#if type === 'spinner'}
					<div class={getLoadingClasses()}></div>
				{:else if type === 'dots'}
					<div class="loading-dots">
						<div class="dot dot-1"></div>
						<div class="dot dot-2"></div>
						<div class="dot dot-3"></div>
					</div>
				{:else if type === 'bars'}
					<div class="loading-bars">
						<div class="bar bar-1"></div>
						<div class="bar bar-2"></div>
						<div class="bar bar-3"></div>
						<div class="bar bar-4"></div>
					</div>
				{:else if type === 'pulse'}
					<div class="loading-pulse">
						<div class="pulse-circle"></div>
					</div>
				{:else if type === 'skeleton'}
					<div class="loading-skeleton-container">
						<div class="skeleton-line skeleton-line-1"></div>
						<div class="skeleton-line skeleton-line-2"></div>
						<div class="skeleton-line skeleton-line-3"></div>
					</div>
				{:else if type === 'progress'}
					<div class="loading-progress-container">
						<div class="progress-circle">
							<svg class="progress-svg" viewBox="0 0 100 100">
								<circle
									class="progress-bg"
									cx="50"
									cy="50"
									r="45"
									fill="none"
									stroke="currentColor"
									stroke-width="8"
									opacity="0.2"
								/>
								<circle
									class="progress-fill"
									cx="50"
									cy="50"
									r="45"
									fill="none"
									stroke="currentColor"
									stroke-width="8"
									stroke-linecap="round"
									stroke-dasharray="283"
									stroke-dashoffset={283 - (283 * currentProgress) / 100}
									style="transition: stroke-dashoffset 0.3s ease;"
								/>
							</svg>
							{#if showProgress}
								<div class="progress-text">{Math.round(currentProgress)}%</div>
							{/if}
						</div>
					</div>
				{/if}
			</div>

			<!-- Loading Message -->
			{#if message}
				<div class="loading-message">
					{message}
				</div>
			{/if}

			<!-- Progress Bar -->
			{#if showProgress && type !== 'progress'}
				<div class="progress-bar-container">
					<div class="progress-bar">
						<div class="progress-bar-fill" style="width: {currentProgress}%"></div>
					</div>
					<div class="progress-text">{Math.round(currentProgress)}%</div>
				</div>
			{/if}
		</div>
	</div>
{:else if !showLoading}
	{@render children()}
{/if}

<style>
	.loading-container {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 9999;
		background: hsl(var(--b1));
	}

	.loading-overlay {
		background: hsl(var(--b1) / 0.9);
		backdrop-filter: blur(4px);
	}

	.loading-blur {
		backdrop-filter: blur(8px);
	}

	.loading-content {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 1rem;
		text-align: center;
		padding: 2rem;
	}

	.loading-icon {
		display: flex;
		align-items: center;
		justify-content: center;
	}

	/* Dots Animation */
	.loading-dots {
		display: flex;
		gap: 0.5rem;
	}

	.dot {
		width: 0.75rem;
		height: 0.75rem;
		background: hsl(var(--p));
		border-radius: 50%;
		animation: dot-bounce 1.4s ease-in-out infinite both;
	}

	.dot-1 {
		animation-delay: -0.32s;
	}
	.dot-2 {
		animation-delay: -0.16s;
	}
	.dot-3 {
		animation-delay: 0s;
	}

	@keyframes dot-bounce {
		0%,
		80%,
		100% {
			transform: scale(0);
		}
		40% {
			transform: scale(1);
		}
	}

	/* Bars Animation */
	.loading-bars {
		display: flex;
		gap: 0.25rem;
		align-items: end;
		height: 2rem;
	}

	.bar {
		width: 0.25rem;
		background: hsl(var(--p));
		border-radius: 0.125rem;
		animation: bar-scale 1.2s ease-in-out infinite;
	}

	.bar-1 {
		animation-delay: -0.4s;
	}
	.bar-2 {
		animation-delay: -0.3s;
	}
	.bar-3 {
		animation-delay: -0.2s;
	}
	.bar-4 {
		animation-delay: -0.1s;
	}

	@keyframes bar-scale {
		0%,
		40%,
		100% {
			height: 0.5rem;
		}
		20% {
			height: 2rem;
		}
	}

	/* Pulse Animation */
	.loading-pulse {
		position: relative;
		width: 3rem;
		height: 3rem;
	}

	.pulse-circle {
		width: 100%;
		height: 100%;
		background: hsl(var(--p));
		border-radius: 50%;
		animation: pulse-scale 2s ease-in-out infinite;
	}

	@keyframes pulse-scale {
		0% {
			transform: scale(0);
			opacity: 1;
		}
		100% {
			transform: scale(1);
			opacity: 0;
		}
	}

	/* Skeleton Animation */
	.loading-skeleton-container {
		width: 12rem;
		display: flex;
		flex-direction: column;
		gap: 0.5rem;
	}

	.skeleton-line {
		height: 1rem;
		background: linear-gradient(90deg, hsl(var(--b2)) 25%, hsl(var(--b3)) 50%, hsl(var(--b2)) 75%);
		background-size: 200% 100%;
		animation: shimmer 2s infinite;
		border-radius: 0.5rem;
	}

	.skeleton-line-1 {
		width: 100%;
	}
	.skeleton-line-2 {
		width: 80%;
	}
	.skeleton-line-3 {
		width: 60%;
	}

	@keyframes shimmer {
		0% {
			background-position: -200px 0;
		}
		100% {
			background-position: calc(200px + 100%) 0;
		}
	}

	/* Progress Circle */
	.loading-progress-container {
		position: relative;
	}

	.progress-circle {
		position: relative;
		width: 4rem;
		height: 4rem;
		color: hsl(var(--p));
	}

	.progress-svg {
		width: 100%;
		height: 100%;
		transform: rotate(-90deg);
	}

	.progress-text {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		font-size: 0.875rem;
		font-weight: 600;
		color: hsl(var(--bc));
	}

	/* Progress Bar */
	.progress-bar-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 0.5rem;
		width: 12rem;
	}

	.progress-bar {
		width: 100%;
		height: 0.5rem;
		background: hsl(var(--b3));
		border-radius: 0.25rem;
		overflow: hidden;
	}

	.progress-bar-fill {
		height: 100%;
		background: hsl(var(--p));
		border-radius: 0.25rem;
		transition: width 0.3s ease;
	}

	/* Loading Message */
	.loading-message {
		font-size: 1rem;
		color: hsl(var(--bc) / 0.7);
		max-width: 20rem;
	}

	/* Responsive */
	@media (max-width: 640px) {
		.loading-content {
			padding: 1rem;
		}

		.progress-bar-container {
			width: 10rem;
		}

		.loading-skeleton-container {
			width: 10rem;
		}
	}

	/* Reduced Motion */
	@media (prefers-reduced-motion: reduce) {
		.dot,
		.bar,
		.pulse-circle,
		.skeleton-line {
			animation: none;
		}

		.progress-bar-fill {
			transition: none;
		}
	}
</style>
