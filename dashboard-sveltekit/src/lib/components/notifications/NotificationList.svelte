<script lang="ts">
	import Icon from '@iconify/svelte';
	import StaggeredList from '$lib/components/transitions/StaggeredList.svelte';
	import { notificationStore } from '$lib/stores/notification.svelte';
	import type { Notification } from '$lib/types';
	import NotificationListItem from './NotificationListItem.svelte';

	interface Props {
		notifications: Notification[];
		onRefresh?: () => void;
	}

	const { notifications, onRefresh }: Props = $props();

	// จัดการการคลิกการแจ้งเตือน
	async function handleNotificationClick(notification: Notification) {
		// ทำเครื่องหมายว่าอ่านแล้วถ้ายังไม่อ่าน
		if (notification.status === 'unread') {
			await notificationStore.markAsRead([notification._id]);
		}

		// นำทางไปยัง URL ถ้ามี
		if (notification.data?.url) {
			window.open(notification.data.url, '_blank');
		}
	}

	// จัดการการลบการแจ้งเตือน
	async function handleDeleteNotification(notificationId: string) {
		await notificationStore.deleteNotification(notificationId);
		onRefresh?.();
	}

	// จัดการการทำเครื่องหมายอ่าน/ไม่อ่าน
	async function handleToggleRead(notification: Notification) {
		if (notification.status === 'unread') {
			await notificationStore.markAsRead([notification._id]);
		}
		// Note: Backend ไม่มี API สำหรับ mark as unread ตอนนี้
	}

	// จัดกลุ่มการแจ้งเตือนตามวันที่
	const groupedNotifications = $derived(() => {
		const groups: Record<string, Notification[]> = {};

		notifications.forEach(notification => {
			const date = new Date(notification.createdAt);
			const today = new Date();
			const yesterday = new Date(today);
			yesterday.setDate(yesterday.getDate() - 1);

			let groupKey: string;

			if (date.toDateString() === today.toDateString()) {
				groupKey = 'วันนี้';
			} else if (date.toDateString() === yesterday.toDateString()) {
				groupKey = 'เมื่อวาน';
			} else {
				groupKey = date.toLocaleDateString('th-TH', {
					weekday: 'long',
					year: 'numeric',
					month: 'long',
					day: 'numeric',
				});
			}

			if (!groups[groupKey]) {
				groups[groupKey] = [];
			}
			groups[groupKey].push(notification);
		});

		return groups;
	});
</script>

<div class="divide-y divide-base-200">
	{#each Object.entries(groupedNotifications()) as [dateGroup, groupNotifications]}
		<!-- Date Group Header -->
		<div class="px-6 py-3 bg-base-50">
			<h3 class="text-sm font-medium text-base-content/70">
				{dateGroup}
				<span class="ml-2 text-xs">
					({groupNotifications.length} รายการ)
				</span>
			</h3>
		</div>

		<!-- Notifications in Group -->
		<div class="divide-y divide-base-200">
			<StaggeredList
				items={groupNotifications.map(n => ({ ...n, id: n._id }))}
				staggerDelay={75}
				animationType="fly"
				direction="up"
			>
				{#snippet children(notification)}
					<NotificationListItem
						{notification}
						onClick={() => handleNotificationClick(notification)}
						onDelete={() => handleDeleteNotification(notification._id)}
						onToggleRead={() => handleToggleRead(notification)}
					/>
				{/snippet}
			</StaggeredList>
		</div>
	{/each}
</div>

<style>
	.bg-base-50 {
		background-color: hsl(var(--b1) / 0.5);
	}
</style>
