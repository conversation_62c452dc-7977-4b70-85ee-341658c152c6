<script lang="ts">
	import type { Snippet } from 'svelte';
	import { cubicOut } from 'svelte/easing';
	import { fade } from 'svelte/transition';
	import { page } from '$app/state';

	interface Props {
		children: Snippet;
		showLoadingBar?: boolean;
		loadingBarColor?: string;
	}

	const { children, showLoadingBar = true, loadingBarColor = 'primary' }: Props = $props();

	// ใช้ page state แทน navigating ที่ deprecated
	let isNavigating = $state(false);
	const currentPath = $derived(page.url.pathname);
	let previousPath = $state('');

	// ตรวจสอบการเปลี่ยนหน้า
	$effect(() => {
		if (currentPath !== previousPath && previousPath !== '') {
			isNavigating = true;
			// ใช้ requestAnimationFrame เพื่อ performance ที่ดีกว่า
			const timeoutId = setTimeout(() => {
				isNavigating = false;
			}, 400);

			return () => clearTimeout(timeoutId);
		}
		previousPath = currentPath;
	});
</script>

<div class="navigation-container">
	<!-- Navigation Loading Bar -->
	{#if isNavigating && showLoadingBar}
		<div
			in:fade={{ duration: 150, easing: cubicOut }}
			out:fade={{ duration: 200, easing: cubicOut }}
			class="fixed top-0 left-0 right-0 z-50"
		>
			<div class="h-1 bg-{loadingBarColor}/20">
				<div class="h-full bg-{loadingBarColor} animate-loading-bar will-change-transform"></div>
			</div>
		</div>
	{/if}

	<!-- Page Content -->
	<div
		class="content-container transition-opacity duration-300 ease-out"
		class:opacity-80={isNavigating}
		style="will-change: opacity;"
	>
		{@render children()}
	</div>
</div>

<style>
	@keyframes loading-bar {
		0% {
			width: 0%;
			margin-left: 0%;
		}
		50% {
			width: 75%;
			margin-left: 12.5%;
		}
		100% {
			width: 100%;
			margin-left: 0%;
		}
	}

	.animate-loading-bar {
		animation: loading-bar 2s ease-in-out infinite;
	}
</style>
