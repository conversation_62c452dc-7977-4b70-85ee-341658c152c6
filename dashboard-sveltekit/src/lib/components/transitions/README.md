# 🎭 Enhanced Page Transitions

ระบบ Page Transitions ที่ปรับปรุงแล้วสำหรับ SvelteKit พร้อม smooth animations และ performance optimizations

## 🚀 Features

### ✨ Enhanced Animations

- **Fade Effects**: fade in/out พร้อม opacity transitions
- **Slide Effects**: slide up/down/left/right พร้อม transform animations
- **Scale Effects**: scale และ zoom พร้อม GPU acceleration
- **Slide Transitions**: horizontal/vertical slide transitions

### ⚡ Performance Optimizations

- GPU acceleration ด้วย `transform3d()` และ `translateZ(0)`
- `will-change` properties สำหรับ browser optimization
- `contain: layout style paint` สำหรับ layout containment
- Reduced motion support สำหรับ accessibility

### 🎨 Customizable Options

- Duration control (100ms - 1000ms)
- Easing functions (cubic-out, quint-out, ease variants)
- Loading bar colors และ visibility
- Disable transitions option

## 📦 Components

### EnhancedPageTransition

Component หลักที่รวม page transitions และ navigation loading bar

```svelte
<EnhancedPageTransition
	type="fade"
	duration={400}
	easing="cubic-out"
	showLoadingBar={true}
	loadingBarColor="primary"
>
	<YourPageContent />
</EnhancedPageTransition>
```

### PageTransition (Legacy)

Component เดิมที่รองรับ transition types หลากหลาย

```svelte
<PageTransition type="slide-up" duration={300} easing="quint-out">
	<YourContent />
</PageTransition>
```

### NavigationTransition (Legacy)

Component สำหรับ navigation loading bar

```svelte
<NavigationTransition showLoadingBar={true} loadingBarColor="primary">
	<YourContent />
</NavigationTransition>
```

## 🎯 Transition Types

| Type               | Description               | Performance |
| ------------------ | ------------------------- | ----------- |
| `fade`             | Simple opacity transition | ⭐⭐⭐⭐⭐  |
| `slide-up`         | Slide from bottom         | ⭐⭐⭐⭐    |
| `slide-down`       | Slide from top            | ⭐⭐⭐⭐    |
| `slide-left`       | Slide from right          | ⭐⭐⭐⭐    |
| `slide-right`      | Slide from left           | ⭐⭐⭐⭐    |
| `scale`            | Scale from 80%            | ⭐⭐⭐⭐    |
| `zoom`             | Scale from 30%            | ⭐⭐⭐      |
| `slide-horizontal` | Horizontal slide          | ⭐⭐⭐      |
| `slide-vertical`   | Vertical slide            | ⭐⭐⭐      |

## 🎨 Easing Options

- `ease` - Standard easing
- `ease-in` - Slow start
- `ease-out` - Slow end
- `ease-in-out` - Slow start and end
- `cubic-out` - Cubic bezier (recommended)
- `quint-out` - Quintic easing

## 🔧 CSS Classes

### Animation Classes

```css
.animate-fade-in          /* Fade in effect */
.animate-fade-out         /* Fade out effect */
.animate-slide-in-left    /* Slide in from left */
.animate-slide-in-right   /* Slide in from right */
.animate-scale-in         /* Scale in effect */
.animate-zoom-in          /* Zoom in effect */
```

### Performance Classes

```css
.gpu-accelerated          /* GPU acceleration */
.smooth-scroll           /* Smooth scrolling */
.loading-skeleton        /* Skeleton loading */
.loading-pulse          /* Pulse loading */
```

### Hover Effects

```css
.hover-lift-smooth       /* Smooth lift on hover */
.hover-scale-smooth      /* Smooth scale on hover */
.hover-glow-smooth       /* Smooth glow on hover */
.focus-ring-smooth       /* Smooth focus ring */
```

## 📱 Responsive & Accessibility

### Reduced Motion Support

```css
@media (prefers-reduced-motion: reduce) {
	/* Animations are automatically disabled */
}
```

### Dark Mode Optimizations

```css
@media (prefers-color-scheme: dark) {
	/* Enhanced shadows and glows for dark mode */
}
```

## 🚀 Usage Examples

### Basic Usage

```svelte
<script>
	import EnhancedPageTransition from '$lib/components/transitions/EnhancedPageTransition.svelte';
</script>

<EnhancedPageTransition>
	{@render children()}
</EnhancedPageTransition>
```

### Advanced Usage

```svelte
<EnhancedPageTransition
	type="slide-up"
	duration={500}
	easing="quint-out"
	showLoadingBar={true}
	loadingBarColor="accent"
	disabled={$prefersReducedMotion}
>
	{@render children()}
</EnhancedPageTransition>
```

### Route-Specific Transitions

```svelte
<script>
	import { page } from '$app/state';

	const transitionType = $derived(
		page.url.pathname.includes('/dashboard')
			? 'fade'
			: page.url.pathname.includes('/settings')
				? 'slide-right'
				: 'slide-up'
	);
</script>

<EnhancedPageTransition type={transitionType}>
	{@render children()}
</EnhancedPageTransition>
```

## 🔧 Performance Tips

1. **ใช้ `fade` สำหรับ performance ที่ดีที่สุด**
2. **หลีกเลี่ยง complex transitions บนอุปกรณ์ช้า**
3. **ใช้ `disabled` prop เมื่อต้องการปิด animations**
4. **ตั้งค่า `duration` ให้เหมาะสมกับ content**

## 🐛 Troubleshooting

### Transitions ไม่ทำงาน

- ตรวจสอบว่า `{#key}` block ทำงานถูกต้อง
- ตรวจสอบ `prefers-reduced-motion` settings
- ตรวจสอบ CSS imports

### Performance Issues

- ลด `duration` ลง
- ใช้ `fade` แทน complex transitions
- เพิ่ม `disabled` prop สำหรับอุปกรณ์ช้า

### Loading Bar ไม่แสดง

- ตรวจสอบ `showLoadingBar={true}`
- ตรวจสอบ CSS classes สำหรับ loading bar
- ตรวจสอบ z-index conflicts
