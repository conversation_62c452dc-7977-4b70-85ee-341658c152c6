<script lang="ts">
	import { page } from '$app/state';
	import EnhancedPageTransition from './EnhancedPageTransition.svelte';

	interface Props {
		children: any;
	}

	const { children }: Props = $props();

	// Demo state
	const currentTransition = $state('fade');
	const currentDuration = $state(400);
	const currentEasing = $state('cubic-out');
	const showLoadingBar = $state(true);
	const loadingBarColor = $state('primary');

	const transitionTypes = [
		{ value: 'fade', label: 'Fade', description: 'Simple opacity transition' },
		{ value: 'slide-up', label: 'Slide Up', description: 'Slide from bottom' },
		{ value: 'slide-down', label: 'Slide Down', description: 'Slide from top' },
		{ value: 'slide-left', label: 'Slide Left', description: 'Slide from right' },
		{
			value: 'slide-right',
			label: 'Slide Right',
			description: 'Slide from left',
		},
		{ value: 'scale', label: 'Scale', description: 'Scale from 80%' },
		{ value: 'zoom', label: 'Zoom', description: 'Scale from 30%' },
		{
			value: 'slide-horizontal',
			label: 'Slide Horizontal',
			description: 'Horizontal slide',
		},
		{
			value: 'slide-vertical',
			label: 'Slide Vertical',
			description: 'Vertical slide',
		},
	];

	const easingTypes = [
		{ value: 'ease', label: 'Ease' },
		{ value: 'ease-in', label: 'Ease In' },
		{ value: 'ease-out', label: 'Ease Out' },
		{ value: 'ease-in-out', label: 'Ease In Out' },
		{ value: 'cubic-out', label: 'Cubic Out (Recommended)' },
		{ value: 'quint-out', label: 'Quint Out' },
	];

	const colorOptions = [
		{ value: 'primary', label: 'Primary' },
		{ value: 'secondary', label: 'Secondary' },
		{ value: 'accent', label: 'Accent' },
		{ value: 'info', label: 'Info' },
		{ value: 'success', label: 'Success' },
		{ value: 'warning', label: 'Warning' },
		{ value: 'error', label: 'Error' },
	];

	// Force re-render for demo
	let demoKey = $state(0);
	function triggerDemo() {
		demoKey++;
	}
</script>

<div class="transition-demo space-y-6 sm:space-y-6">
	<div class="demo-header">
		<h2 class="text-2xl font-bold mb-4">🎭 Enhanced Page Transitions Demo</h2>
		<p class="text-base-content/70 mb-6">
			ทดสอบ page transitions ที่ปรับปรุงแล้วพร้อม performance optimizations
		</p>
	</div>

	<!-- Controls -->
	<div
		class="demo-controls grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4 bg-base-200 rounded-lg"
	>
		<!-- Transition Type -->
		<div class="form-control">
			<label class="label">
				<span class="label-text font-medium">Transition Type</span>
			</label>
			<select
				class="select select-bordered w-full"
				bind:value={currentTransition}
				onchange={triggerDemo}
			>
				{#each transitionTypes as type}
					<option value={type.value}>{type.label}</option>
				{/each}
			</select>
			<label class="label">
				<span class="label-text-alt text-xs">
					{transitionTypes.find(t => t.value === currentTransition)?.description}
				</span>
			</label>
		</div>

		<!-- Duration -->
		<div class="form-control">
			<label class="label">
				<span class="label-text font-medium">Duration (ms)</span>
			</label>
			<input
				type="range"
				min="100"
				max="1000"
				step="50"
				class="range range-primary"
				bind:value={currentDuration}
				onchange={triggerDemo}
			/>
			<div class="flex justify-between text-xs px-2">
				<span>100ms</span>
				<span class="font-medium">{currentDuration}ms</span>
				<span>1000ms</span>
			</div>
		</div>

		<!-- Easing -->
		<div class="form-control">
			<label class="label">
				<span class="label-text font-medium">Easing</span>
			</label>
			<select
				class="select select-bordered w-full"
				bind:value={currentEasing}
				onchange={triggerDemo}
			>
				{#each easingTypes as easing}
					<option value={easing.value}>{easing.label}</option>
				{/each}
			</select>
		</div>

		<!-- Loading Bar -->
		<div class="form-control">
			<label class="label cursor-pointer">
				<span class="label-text font-medium">Show Loading Bar</span>
				<input type="checkbox" class="toggle toggle-primary" bind:checked={showLoadingBar} />
			</label>
		</div>

		<!-- Loading Bar Color -->
		<div class="form-control">
			<label class="label">
				<span class="label-text font-medium">Loading Bar Color</span>
			</label>
			<select
				class="select select-bordered w-full"
				bind:value={loadingBarColor}
				disabled={!showLoadingBar}
			>
				{#each colorOptions as color}
					<option value={color.value}>{color.label}</option>
				{/each}
			</select>
		</div>

		<!-- Trigger Button -->
		<div class="form-control">
			<label class="label">
				<span class="label-text font-medium">Test Transition</span>
			</label>
			<button class="btn btn-primary" onclick={triggerDemo}> 🎬 Trigger Demo </button>
		</div>
	</div>

	<!-- Demo Area -->
	<div class="demo-area">
		<h3 class="text-lg font-semibold mb-4">Preview Area</h3>
		<div
			class="border-2 border-dashed border-base-300 rounded-lg min-h-[400px] relative overflow-hidden"
		>
			{#key demoKey}
				<EnhancedPageTransition
					type={currentTransition}
					duration={currentDuration}
					easing={currentEasing}
					{showLoadingBar}
					{loadingBarColor}
				>
					<div class="p-8 h-full flex flex-col items-center justify-center space-y-4">
						<div class="text-6xl">🎭</div>
						<h4 class="text-2xl font-bold">Transition Demo</h4>
						<p class="text-center text-base-content/70 max-w-md">
							นี่คือตัวอย่างการใช้งาน Enhanced Page Transition พร้อม {currentTransition} effect
						</p>
						<div class="stats shadow">
							<div class="stat">
								<div class="stat-title">Type</div>
								<div class="stat-value text-lg">{currentTransition}</div>
							</div>
							<div class="stat">
								<div class="stat-title">Duration</div>
								<div class="stat-value text-lg">{currentDuration}ms</div>
							</div>
							<div class="stat">
								<div class="stat-title">Easing</div>
								<div class="stat-value text-lg">{currentEasing}</div>
							</div>
						</div>
						<div class="flex flex-wrap gap-2 justify-center">
							<div class="badge badge-primary">GPU Accelerated</div>
							<div class="badge badge-secondary">Smooth</div>
							<div class="badge badge-accent">Responsive</div>
							{#if showLoadingBar}
								<div class="badge badge-info">Loading Bar</div>
							{/if}
						</div>
					</div>
				</EnhancedPageTransition>
			{/key}
		</div>
	</div>

	<!-- Performance Info -->
	<div class="performance-info bg-info/10 p-4 rounded-lg">
		<h4 class="font-semibold text-info mb-2">⚡ Performance Tips</h4>
		<ul class="text-sm space-y-1 text-info/80">
			<li>• <strong>Fade</strong> transitions มี performance ดีที่สุด</li>
			<li>• <strong>Duration 300-400ms</strong> เหมาะสมที่สุดสำหรับ UX</li>
			<li>• <strong>cubic-out</strong> easing ให้ความรู้สึกที่ natural</li>
			<li>• Transitions จะถูกปิดอัตโนมัติเมื่อ <code>prefers-reduced-motion</code></li>
		</ul>
	</div>
</div>

<style>
	.transition-demo {
		contain: layout style paint;
	}

	.demo-area {
		position: relative;
	}

	.stats {
		background: hsl(var(--b2));
	}

	code {
		background: hsl(var(--b3));
		padding: 0.125rem 0.25rem;
		border-radius: 0.25rem;
		font-size: 0.875em;
	}
</style>
