<script lang="ts">
	import type { Snippet } from 'svelte';
	import { cubicOut, quintOut } from 'svelte/easing';
	import { fade, fly, scale } from 'svelte/transition';
	import { page } from '$app/state';

	interface Props {
		children: Snippet;
		duration?: number;
		type?: 'fade' | 'slide-up' | 'slide-down' | 'slide-left' | 'slide-right' | 'scale' | 'zoom';
		easing?: 'ease' | 'ease-in' | 'ease-out' | 'ease-in-out' | 'cubic-out' | 'quint-out';
	}

	const { children, duration = 400, type = 'fade', easing = 'cubic-out' }: Props = $props();

	// ใช้ page.url.pathname เป็น key สำหรับ transition
	const currentPath = $derived(page.url.pathname);

	// เลือก easing function
	const easingFunctions = {
		ease: cubicOut,
		'ease-in': cubicOut,
		'ease-out': cubicOut,
		'ease-in-out': cubicOut,
		'cubic-out': cubicOut,
		'quint-out': quintOut,
	};

	const selectedEasing = easingFunctions[easing] || cubicOut;
	const outDuration = Math.floor(duration * 0.6); // ทำให้ out transition เร็วกว่า

	// กำหนด transition parameters ตาม type
	const fadeParams = $derived({
		duration,
		easing: selectedEasing,
	});

	const flyParams = $derived({
		duration,
		easing: selectedEasing,
		y: type === 'slide-up' ? 30 : type === 'slide-down' ? -30 : 0,
		x: type === 'slide-left' ? 30 : type === 'slide-right' ? -30 : 0,
	});

	const scaleParams = $derived({
		duration,
		easing: selectedEasing,
		start: type === 'zoom' ? 0.3 : 0.8,
	});
</script>

{#key currentPath}
	{#if type === 'fade'}
		<div
			class="page-transition-container"
			in:fade|global={fadeParams}
			out:fade|global={{ ...fadeParams, duration: outDuration }}
		>
			{@render children()}
		</div>
	{:else if type.startsWith('slide')}
		<div
			class="page-transition-container"
			in:fly|global={flyParams}
			out:fly|global={{ ...flyParams, duration: outDuration }}
		>
			{@render children()}
		</div>
	{:else if type === 'scale' || type === 'zoom'}
		<div
			class="page-transition-container"
			in:scale|global={scaleParams}
			out:scale|global={{ ...scaleParams, duration: outDuration }}
		>
			{@render children()}
		</div>
	{:else}
		<div class="page-transition-container">
			{@render children()}
		</div>
	{/if}
{/key}

<style>
	.page-transition-container {
		width: 100%;
		height: 100%;
		contain: layout style paint;
		will-change: transform, opacity;
	}
</style>
