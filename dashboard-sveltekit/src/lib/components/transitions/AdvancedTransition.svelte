<script lang="ts">
	import { quintOut } from 'svelte/easing';
	import { fade } from 'svelte/transition';
	import { page } from '$app/state';

	interface Props {
		children: any;
		type?: 'fast' | 'normal' | 'slow';
		duration?: number;
	}

	const { children, type = 'normal', duration }: Props = $props();

	const currentPath = $derived(page.url.pathname);

	// กำหนด fade duration ตาม type
	function getFadeDuration() {
		if (duration) return duration;

		switch (type) {
			case 'fast':
				return 250;
			case 'slow':
				return 600;
			default: // normal
				return 400;
		}
	}

	const fadeParams = $derived({
		duration: getFadeDuration(),
		easing: quintOut,
	});
</script>

{#key currentPath}
	<div
		in:fade={fadeParams}
		out:fade={{
			...fadeParams,
			duration: fadeParams.duration / 2,
		}}
		class="w-full"
	>
		{@render children()}
	</div>
{/key}
