<script lang="ts">
	import { fade } from 'svelte/transition';

	interface Props {
		isLoading: boolean;
		children: any;
		loadingText?: string;
		showSkeleton?: boolean;
	}

	const {
		isLoading,
		children,
		loadingText = 'กำลังโหลด...',
		showSkeleton = false,
	}: Props = $props();
</script>

<div class="relative min-h-32">
	{#if isLoading}
		<div
			in:fade={{ duration: 200 }}
			out:fade={{ duration: 200 }}
			class="absolute inset-0 flex items-center justify-center bg-base-100/80 backdrop-blur-sm z-10"
		>
			{#if showSkeleton}
				<!-- Skeleton Loading -->
				<div class="w-full space-y-4 p-6">
					<div class="skeleton h-8 w-3/4"></div>
					<div class="skeleton h-4 w-full"></div>
					<div class="skeleton h-4 w-5/6"></div>
					<div class="skeleton h-32 w-full"></div>
				</div>
			{:else}
				<!-- Spinner Loading -->
				<div in:fade={{ duration: 400 }} class="text-center">
					<span class="loading loading-spinner loading-lg mb-4"></span>
					<p class="text-base-content/70">{loadingText}</p>
				</div>
			{/if}
		</div>
	{/if}

	<div
		class="transition-opacity duration-300"
		class:opacity-30={isLoading}
		class:pointer-events-none={isLoading}
	>
		{@render children()}
	</div>
</div>
