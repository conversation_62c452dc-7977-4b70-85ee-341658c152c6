<script lang="ts">
	import type { Snippet } from 'svelte';
	import { cubicOut, quintOut } from 'svelte/easing';
	import { fade, fly, scale, slide } from 'svelte/transition';
	import { page } from '$app/state';

	interface Props {
		children: Snippet;
		duration?: number;
		type?:
			| 'fade'
			| 'slide-up'
			| 'slide-down'
			| 'slide-left'
			| 'slide-right'
			| 'scale'
			| 'zoom'
			| 'slide-horizontal'
			| 'slide-vertical';
		easing?: 'ease' | 'ease-in' | 'ease-out' | 'ease-in-out' | 'cubic-out' | 'quint-out';
		showLoadingBar?: boolean;
		loadingBarColor?: string;
		disabled?: boolean;
	}

	const {
		children,
		duration = 400,
		type = 'fade',
		easing = 'cubic-out',
		showLoadingBar = true,
		loadingBarColor = 'primary',
		disabled = false,
	}: Props = $props();

	// ใช้ page.url.pathname เป็น key สำหรับ transition
	const currentPath = $derived(page.url.pathname);

	// Navigation state
	let isNavigating = $state(false);
	let previousPath = $state('');

	// เลือก easing function
	const easingFunctions = {
		ease: cubicOut,
		'ease-in': cubicOut,
		'ease-out': cubicOut,
		'ease-in-out': cubicOut,
		'cubic-out': cubicOut,
		'quint-out': quintOut,
	};

	const selectedEasing = easingFunctions[easing] || cubicOut;
	const outDuration = Math.floor(duration * 0.6);

	// ตรวจสอบการเปลี่ยนหน้า
	$effect(() => {
		if (currentPath !== previousPath && previousPath !== '' && !disabled) {
			isNavigating = true;
			const timeoutId = setTimeout(() => {
				isNavigating = false;
			}, duration + 100);

			return () => clearTimeout(timeoutId);
		}
		previousPath = currentPath;
	});

	// กำหนด transition parameters ตาม type
	const getTransitionParams = (isOut = false) => {
		const baseDuration = isOut ? outDuration : duration;
		const baseParams = {
			duration: baseDuration,
			easing: selectedEasing,
		};

		switch (type) {
			case 'slide-up':
				return { ...baseParams, y: 30 };
			case 'slide-down':
				return { ...baseParams, y: -30 };
			case 'slide-left':
				return { ...baseParams, x: 30 };
			case 'slide-right':
				return { ...baseParams, x: -30 };
			case 'slide-horizontal':
				return { ...baseParams, axis: 'x' as const };
			case 'slide-vertical':
				return { ...baseParams, axis: 'y' as const };
			case 'scale':
				return { ...baseParams, start: 0.8 };
			case 'zoom':
				return { ...baseParams, start: 0.3 };
			default: // fade
				return baseParams;
		}
	};
</script>

{#if disabled}
	<div class="enhanced-transition-container">
		{@render children()}
	</div>
{:else}
	<div class="enhanced-transition-container">
		<!-- Navigation Loading Bar -->
		{#if isNavigating && showLoadingBar}
			<div
				in:fade={{ duration: 150, easing: cubicOut }}
				out:fade={{ duration: 200, easing: cubicOut }}
				class="fixed top-0 left-0 right-0 z-50"
			>
				<div class="h-1 bg-{loadingBarColor}/20">
					<div class="h-full bg-{loadingBarColor} animate-loading-bar will-change-transform"></div>
				</div>
			</div>
		{/if}

		<!-- Page Content with Transitions -->
		{#key currentPath}
			{#if type === 'fade'}
				<div
					class="transition-content"
					in:fade|global={getTransitionParams()}
					out:fade|global={getTransitionParams(true)}
				>
					{@render children()}
				</div>
			{:else if type.startsWith('slide') && !type.includes('horizontal') && !type.includes('vertical')}
				<div
					class="transition-content"
					in:fly|global={getTransitionParams()}
					out:fly|global={getTransitionParams(true)}
				>
					{@render children()}
				</div>
			{:else if type === 'slide-horizontal' || type === 'slide-vertical'}
				<div
					class="transition-content"
					in:slide|global={getTransitionParams()}
					out:slide|global={getTransitionParams(true)}
				>
					{@render children()}
				</div>
			{:else if type === 'scale' || type === 'zoom'}
				<div
					class="transition-content"
					in:scale|global={getTransitionParams()}
					out:scale|global={getTransitionParams(true)}
				>
					{@render children()}
				</div>
			{:else}
				<div class="transition-content">
					{@render children()}
				</div>
			{/if}
		{/key}
	</div>
{/if}

<style>
	.enhanced-transition-container {
		position: relative;
		width: 100%;
		height: 100%;
	}

	.transition-content {
		width: 100%;
		height: 100%;
		contain: layout style paint;
		will-change: transform, opacity;
	}

	/* Loading bar animation */
	@keyframes loading-bar {
		0% {
			width: 0%;
			margin-left: 0%;
		}
		50% {
			width: 75%;
			margin-left: 12.5%;
		}
		100% {
			width: 100%;
			margin-left: 0%;
		}
	}

	.animate-loading-bar {
		animation: loading-bar 2s ease-in-out infinite;
	}

	/* Responsive animations */
	@media (prefers-reduced-motion: reduce) {
		.transition-content {
			will-change: auto;
		}

		.animate-loading-bar {
			animation: none;
		}
	}
</style>
