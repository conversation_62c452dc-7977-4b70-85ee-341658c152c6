<script lang="ts">
	import Icon from '@iconify/svelte';

	interface Props {
		for?: string;
		text: string;
		required?: boolean;
		tooltip?: string;
		size?: 'xs' | 'sm' | 'md' | 'lg';
		className?: string;
	}

	const {
		for: htmlFor,
		text,
		required = false,
		tooltip,
		size = 'md',
		className = '',
	}: Props = $props();

	const sizeClasses = {
		xs: 'text-xs',
		sm: 'text-sm',
		md: 'text-base',
		lg: 'text-lg',
	};
</script>

<label for={htmlFor} class="label {sizeClasses[size]} {className}">
	<span class="label-text flex items-center gap-1">
		{text}
		{#if required}
			<span class="text-error">*</span>
		{/if}
		{#if tooltip}
			<div class="tooltip tooltip-right" data-tip={tooltip}>
				<Icon icon="mdi:help-circle" class="size-5 text-base-content/50" />
			</div>
		{/if}
	</span>
</label>
