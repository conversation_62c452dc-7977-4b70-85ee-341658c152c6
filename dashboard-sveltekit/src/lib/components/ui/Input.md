# Input Component

Component สำหรับ input ที่รองรับหลายประเภทและฟีเจอร์ครบครัน

## การใช้งาน

### Basic Usage

```svelte
<script>
	import { Input } from '$lib/components/ui';

	let value = '';
</script>

<Input 
	placeholder="กรอกข้อมูล..." 
	bind:value 
/>
```

### Number Input

```svelte
<script>
	import { Input } from '$lib/components/ui';

	let price = 0;
	let quantity = 1;
</script>

<!-- Number input พื้นฐาน -->
<Input 
	type="number" 
	bind:value={price} 
	placeholder="ราคา" 
/>

<!-- Number input พร้อม min, max, step -->
<Input 
	type="number" 
	bind:value={quantity} 
	min="1" 
	max="100" 
	step="1" 
	placeholder="จำนวน" 
/>

<!-- Number input พร้อม formatting -->
<Input 
	type="number" 
	bind:value={price} 
	formatNumber={true}
	numberFormat={{
		locale: 'th-TH',
		currency: 'THB',
		minimumFractionDigits: 2,
		maximumFractionDigits: 2
	}}
	placeholder="ราคา" 
/>
```

### Password Input

```svelte
<script>
	import { Input } from '$lib/components/ui';

	let password = '';
</script>

<Input 
	type="password" 
	bind:value={password} 
	placeholder="รหัสผ่าน" 
	showPasswordToggle={true}
/>
```

### Input with Icon

```svelte
<script>
	import { Input } from '$lib/components/ui';

	let email = '';
</script>

<Input 
	type="email" 
	bind:value={email} 
	placeholder="อีเมล" 
	icon="lucide:mail"
/>
```

### Input with Validation

```svelte
<script>
	import { Input } from '$lib/components/ui';

	let username = '';
	let hasError = false;
</script>

<Input 
	bind:value={username} 
	placeholder="ชื่อผู้ใช้" 
	error={hasError ? 'ชื่อผู้ใช้ไม่ถูกต้อง' : ''}
	validate="กรุณากรอกชื่อผู้ใช้"
/>
```

## Props

### Basic Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `type` | `'text' \| 'email' \| 'password' \| 'number' \| 'tel' \| 'url' \| 'search'` | `'text'` | ประเภทของ input |
| `value` | `string \| number` | `''` | ค่าของ input |
| `placeholder` | `string` | - | ข้อความ placeholder |
| `label` | `string` | - | ป้ายกำกับ |
| `error` | `string` | - | ข้อความ error |
| `info` | `string` | - | ข้อความข้อมูลเพิ่มเติม |
| `disabled` | `boolean` | `false` | ปิดการใช้งาน |
| `required` | `boolean` | `false` | จำเป็นต้องกรอก |
| `readonly` | `boolean` | `false` | อ่านอย่างเดียว |

### Styling Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `size` | `'xs' \| 'sm' \| 'md' \| 'lg' \| 'xl'` | `'md'` | ขนาดของ input |
| `color` | `'ghost' \| 'primary' \| 'secondary' \| 'accent' \| 'info' \| 'success' \| 'warning' \| 'error'` | - | สีของ input |
| `variant` | `'bordered' \| 'soft' \| 'ghost' \| 'outlined'` | `'bordered'` | รูปแบบของ input |
| `rounded` | `'none' \| 'sm' \| 'md' \| 'lg' \| 'full'` | `'sm'` | ความโค้งของมุม |
| `class` | `string` | - | CSS class เพิ่มเติม |

### Icon Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `icon` | `string` | - | ไอคอนด้านซ้าย |
| `iconRight` | `string` | - | ไอคอนด้านขวา |

### Number Input Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `min` | `number \| string` | - | ค่าต่ำสุด |
| `max` | `number \| string` | - | ค่าสูงสุด |
| `step` | `number \| string` | - | ขั้นการเพิ่ม/ลด |
| `formatNumber` | `boolean` | `false` | จัดรูปแบบตัวเลข |
| `numberFormat` | `object` | - | การตั้งค่าการจัดรูปแบบตัวเลข |

### Text Input Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `maxlength` | `number` | - | ความยาวสูงสุด |
| `pattern` | `string` | - | รูปแบบที่อนุญาต |

### Password Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `showPasswordToggle` | `boolean` | `false` | แสดงปุ่มซ่อน/แสดงรหัสผ่าน |

### Validation Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `validate` | `string` | - | ข้อความ validation |
| `showRequired` | `boolean` | `false` | แสดงเครื่องหมาย * สำหรับ required |

### Event Handlers

| Prop | Type | Description |
|------|------|-------------|
| `oninput` | `(event: Event) => void` | เรียกเมื่อมีการพิมพ์ |
| `onchange` | `(event: Event) => void` | เรียกเมื่อค่ามีการเปลี่ยนแปลง |
| `onfocus` | `(event: FocusEvent) => void` | เรียกเมื่อได้รับ focus |
| `onblur` | `(event: FocusEvent) => void` | เรียกเมื่อเสีย focus |
| `onkeydown` | `(event: KeyboardEvent) => void` | เรียกเมื่อกดปุ่ม |

## Number Formatting

### Basic Number Formatting

```svelte
<Input 
	type="number" 
	bind:value={price} 
	formatNumber={true}
	placeholder="ราคา" 
/>
```

### Custom Number Formatting

```svelte
<Input 
	type="number" 
	bind:value={price} 
	formatNumber={true}
	numberFormat={{
		locale: 'en-US',
		currency: 'USD',
		minimumFractionDigits: 2,
		maximumFractionDigits: 2
	}}
	placeholder="ราคา" 
/>
```

### Number Format Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `locale` | `string` | `'th-TH'` | ภาษาและประเทศ |
| `currency` | `string` | `'THB'` | สกุลเงิน (ถ้าไม่ระบุจะใช้ decimal) |
| `minimumFractionDigits` | `number` | `0` | ทศนิยมขั้นต่ำ |
| `maximumFractionDigits` | `number` | `2` | ทศนิยมสูงสุด |

## Examples

### Currency Input

```svelte
<script>
	import { Input } from '$lib/components/ui';

	let price = 0;
</script>

<Input 
	type="number" 
	bind:value={price} 
	formatNumber={true}
	numberFormat={{
		locale: 'th-TH',
		currency: 'THB',
		minimumFractionDigits: 2,
		maximumFractionDigits: 2
	}}
	placeholder="ราคา" 
	label="ราคาสินค้า"
/>
```

### Quantity Input

```svelte
<script>
	import { Input } from '$lib/components/ui';

	let quantity = 1;
</script>

<Input 
	type="number" 
	bind:value={quantity} 
	min="1" 
	max="999" 
	step="1" 
	placeholder="จำนวน" 
	label="จำนวนสินค้า"
/>
```

### Email Input with Validation

```svelte
<script>
	import { Input } from '$lib/components/ui';

	let email = '';
	let emailError = '';

	function validateEmail(value) {
		const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
		if (!value) {
			emailError = 'กรุณากรอกอีเมล';
		} else if (!emailRegex.test(value)) {
			emailError = 'รูปแบบอีเมลไม่ถูกต้อง';
		} else {
			emailError = '';
		}
	}
</script>

<Input 
	type="email" 
	bind:value={email} 
	placeholder="อีเมล" 
	icon="lucide:mail"
	error={emailError}
	onchange={() => validateEmail(email)}
/>
```

### Search Input

```svelte
<script>
	import { Input } from '$lib/components/ui';

	let searchQuery = '';
</script>

<Input 
	type="search" 
	bind:value={searchQuery} 
	placeholder="ค้นหา..." 
	icon="lucide:search"
	iconRight="lucide:filter"
/>
```

## Features

- ✅ **Multiple Types**: รองรับ text, email, password, number, tel, url, search
- ✅ **Number Formatting**: จัดรูปแบบตัวเลขและสกุลเงิน
- ✅ **Validation**: รองรับ error และ validation messages
- ✅ **Icons**: รองรับไอคอนด้านซ้ายและขวา
- ✅ **Password Toggle**: ปุ่มซ่อน/แสดงรหัสผ่าน
- ✅ **Responsive**: ทำงานได้ดีในทุกขนาดหน้าจอ
- ✅ **Accessibility**: รองรับ screen readers
- ✅ **Customizable**: ปรับแต่งได้ตามต้องการ

## Styling

Component ใช้ DaisyUI classes และสามารถปรับแต่งได้ผ่าน `class` prop:

```svelte
<Input 
	class="w-96 bg-gray-100" 
	placeholder="Custom styling" 
/>
```

## Validation

### Error Display

```svelte
<Input 
	error="ข้อความ error" 
	placeholder="Input with error" 
/>
```

### Info Display

```svelte
<Input 
	info="ข้อความข้อมูลเพิ่มเติม" 
	placeholder="Input with info" 
/>
```

### Required Indicator

```svelte
<Input 
	label="ชื่อผู้ใช้"
	showRequired={true}
	placeholder="กรอกชื่อผู้ใช้" 
/>
``` 