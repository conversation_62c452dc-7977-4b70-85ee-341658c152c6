import {
	type CreateCustomerData,
	sanitizeCustomerData,
	type UpdateCustomerData,
	validateCreateCustomerData,
	validateUpdateCustomerData,
} from '$lib/schemas/customer.schema';
import type { ApiResponse } from '$lib/types/common';
import type { Customer, CustomerListResponse } from '$lib/types/customer';
import { BaseService } from './base';

/**
 * ✅ Customer Service - ใช้ BaseService และ native fetch
 * - Validation ก่อนส่ง API
 * - Consistent error handling
 * - Type-safe responses
 */
export class CustomerService extends BaseService {
	/**
	 * ✅ Get customers with pagination and filters
	 */
	async getCustomers(
		siteId: string,
		token: string,
		params?: {
			page?: number;
			limit?: number;
			search?: string;
			status?: 'active' | 'inactive';
			sortBy?: string;
			sortOrder?: 'asc' | 'desc';
		}
	): Promise<ApiResponse<CustomerListResponse>> {
		if (!token?.trim()) {
			return {
				success: false,
				error: 'Token ไม่ถูกต้อง',
			};
		}

		if (!siteId?.trim()) {
			return {
				success: false,
				error: 'Site ID ไม่ถูกต้อง',
			};
		}

		return this.handleRequest(async () => {
			const queryParams = new URLSearchParams();
			if (params?.page) queryParams.append('page', params.page.toString());
			if (params?.limit) queryParams.append('limit', params.limit.toString());
			if (params?.search) queryParams.append('search', params.search);
			if (params?.status) queryParams.append('status', params.status);
			if (params?.sortBy) queryParams.append('sortBy', params.sortBy);
			if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);

			const result = await this.makeAuthenticatedRequest<{
				data: CustomerListResponse;
			}>(`/customer/dashboard/${siteId}/list?${queryParams}`, token);
			return result.data;
		});
	}

	/**
	 * ✅ Get single customer by ID
	 */
	async getCustomer(
		customerId: string,
		siteId: string,
		token: string
	): Promise<ApiResponse<Customer>> {
		if (!token?.trim()) {
			return {
				success: false,
				error: 'Token ไม่ถูกต้อง',
			};
		}

		if (!customerId?.trim()) {
			return {
				success: false,
				error: 'Customer ID ไม่ถูกต้อง',
			};
		}

		if (!siteId?.trim()) {
			return {
				success: false,
				error: 'Site ID ไม่ถูกต้อง',
			};
		}

		return this.handleRequest(async () => {
			const result = await this.makeAuthenticatedRequest<{ data: Customer }>(
				`/customer/dashboard/${siteId}/detail/${customerId}`,
				token
			);
			return result.data;
		});
	}

	/**
	 * ✅ Create new customer
	 */
	async createCustomer(
		data: CreateCustomerData,
		siteId: string,
		token: string
	): Promise<ApiResponse<Customer>> {
		if (!token?.trim()) {
			return {
				success: false,
				error: 'Token ไม่ถูกต้อง',
			};
		}

		if (!siteId?.trim()) {
			return {
				success: false,
				error: 'Site ID ไม่ถูกต้อง',
			};
		}

		// Validate input
		const sanitizedData = sanitizeCustomerData(data);
		const validationError = validateCreateCustomerData(sanitizedData);

		if (validationError) {
			return {
				success: false,
				error: validationError,
			};
		}

		return this.handleRequest(async () => {
			const result = await this.makeAuthenticatedRequest<{ data: Customer }>(
				`/customer/dashboard/${siteId}/create`,
				token,
				{
					method: 'POST',
					body: sanitizedData,
				}
			);
			return result.data;
		});
	}

	/**
	 * ✅ Update existing customer
	 */
	async updateCustomer(
		customerId: string,
		data: UpdateCustomerData,
		siteId: string,
		token: string
	): Promise<ApiResponse<Customer>> {
		if (!token?.trim()) {
			return {
				success: false,
				error: 'Token ไม่ถูกต้อง',
			};
		}

		if (!customerId?.trim()) {
			return {
				success: false,
				error: 'Customer ID ไม่ถูกต้อง',
			};
		}

		if (!siteId?.trim()) {
			return {
				success: false,
				error: 'Site ID ไม่ถูกต้อง',
			};
		}

		// Validate input
		const sanitizedData = sanitizeCustomerData(data);
		const validationError = validateUpdateCustomerData(sanitizedData);

		if (validationError) {
			return {
				success: false,
				error: validationError,
			};
		}

		return this.handleRequest(async () => {
			const result = await this.makeAuthenticatedRequest<{ data: Customer }>(
				`/customer/dashboard/${siteId}/update/${customerId}`,
				token,
				{
					method: 'PUT',
					body: sanitizedData,
				}
			);
			return result.data;
		});
	}

	/**
	 * ✅ Delete customer
	 */
	async deleteCustomer(
		customerId: string,
		siteId: string,
		token: string
	): Promise<ApiResponse<void>> {
		if (!token?.trim()) {
			return {
				success: false,
				error: 'Token ไม่ถูกต้อง',
			};
		}

		if (!customerId?.trim()) {
			return {
				success: false,
				error: 'Customer ID ไม่ถูกต้อง',
			};
		}

		if (!siteId?.trim()) {
			return {
				success: false,
				error: 'Site ID ไม่ถูกต้อง',
			};
		}

		return this.handleRequest(async () => {
			await this.makeAuthenticatedRequest<void>(
				`/customer/dashboard/${siteId}/delete/${customerId}`,
				token,
				{
					method: 'DELETE',
				}
			);
		});
	}

	/**
	 * ✅ Get customer statistics
	 */
	async getCustomerStats(siteId: string, token: string): Promise<ApiResponse<any>> {
		if (!token?.trim()) {
			return {
				success: false,
				error: 'Token ไม่ถูกต้อง',
			};
		}

		if (!siteId?.trim()) {
			return {
				success: false,
				error: 'Site ID ไม่ถูกต้อง',
			};
		}

		return this.handleRequest(async () => {
			const result = await this.makeAuthenticatedRequest<{ data: any }>(
				`/customer/dashboard/${siteId}/stats`,
				token
			);
			return result.data;
		});
	}
}

export const customerService = new CustomerService();
