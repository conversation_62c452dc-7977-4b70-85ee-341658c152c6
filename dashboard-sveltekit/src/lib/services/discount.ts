import type { ApiResponse, DiscountInfo, DiscountValidationRequest } from '$lib/types';
import { BaseService } from './base';

class DiscountService extends BaseService {
	/**
	 * ตรวจสอบความถูกต้องของโค้ดส่วนลด
	 */
	async validateDiscount(
		discountCode: string,
		validationData: Omit<DiscountValidationRequest, 'discountCode'> = {},
		token?: string
	): Promise<ApiResponse<DiscountInfo>> {
		if (!discountCode || discountCode.trim().length === 0) {
			return {
				success: false,
				error: 'กรุณากรอกรหัสส่วนลด',
			};
		}

		const requestBody = {
			discountCode: discountCode.trim(),
			target: validationData.target || 'site',
			orderAmount: validationData.orderAmount || 0,
			items: validationData.items || [],
		};

		return this.handleRequest(async () => {
			const result = await this.makePublicRequest<{ data: DiscountInfo }>('/discount/validate', {
				method: 'POST',
				body: JSON.stringify(requestBody),
				...(token && { headers: { Authorization: `Bearer ${token}` } }),
			});
			return result.data;
		});
	}

	/**
	 * ดึงโค้ดส่วนลดตาม code
	 */
	async getDiscountByCode(
		code: string,
		siteId: string,
		token?: string
	): Promise<ApiResponse<DiscountInfo>> {
		if (!code || !siteId) {
			return {
				success: false,
				error: 'กรุณาระบุรหัสส่วนลดและ siteId',
			};
		}

		return this.handleRequest(async () => {
			const result = await this.makePublicRequest<{ data: DiscountInfo }>(
				`/discount/code/${code}?siteId=${siteId}`,
				{
					...(token && { headers: { Authorization: `Bearer ${token}` } }),
				}
			);
			return result.data;
		});
	}

	/**
	 * ดึงรายการส่วนลดที่ใช้งานได้
	 */
	async getActiveDiscounts(siteId: string, token?: string): Promise<ApiResponse<DiscountInfo[]>> {
		if (!siteId) {
			return {
				success: false,
				error: 'กรุณาระบุ siteId',
			};
		}

		return this.handleRequest(async () => {
			const result = await this.makePublicRequest<{ data: DiscountInfo[] }>(
				`/discount/active?siteId=${siteId}`,
				{
					...(token && { headers: { Authorization: `Bearer ${token}` } }),
				}
			);
			return result.data;
		});
	}

	/**
	 * ค้นหาส่วนลดที่ใช้ได้กับออเดอร์
	 */
	async findApplicableDiscounts(
		siteId: string,
		orderData: {
			orderAmount: number;
			items?: unknown[];
			userId?: string;
			isFirstTime?: boolean;
		},
		token?: string
	): Promise<ApiResponse<DiscountInfo[]>> {
		if (!siteId) {
			return {
				success: false,
				error: 'กรุณาระบุ siteId',
			};
		}

		const requestBody = {
			siteId,
			...orderData,
		};

		return this.handleRequest(async () => {
			const result = await this.makePublicRequest<{ data: DiscountInfo[] }>(
				'/discount/find-applicable',
				{
					method: 'POST',
					body: JSON.stringify(requestBody),
					...(token && { headers: { Authorization: `Bearer ${token}` } }),
				}
			);
			return result.data;
		});
	}
}

export const discountService = new DiscountService();
