// Auth utility functions for SvelteKit

import type { RequestEvent } from '@sveltejs/kit';
import { fail } from '@sveltejs/kit';

/**
 * Helper function to check authentication in server actions
 * ใช้สำหรับ DRY principle ในการตรวจสอบ auth
 */
export function requireAuth(locals: RequestEvent['locals']) {
	if (!locals.user) {
		return fail(401, {
			error: 'กรุณาเข้าสู่ระบบ',
			success: false,
		});
	}
	return null; // Auth passed
}

/**
 * Helper function to get user info safely
 */
export function getAuthUser(locals: RequestEvent['locals']) {
	return {
		user: locals.user,
		token: locals.token,
		isAuthenticated: !!locals.user,
	};
}

/**
 * Type guard for authenticated user
 */
export function isAuthenticated(
	locals: RequestEvent['locals']
): locals is RequestEvent['locals'] & {
	user: NonNullable<RequestEvent['locals']['user']>;
} {
	return !!locals.user;
}

/**
 * Helper function to get auth token
 */
export function getAuthToken(locals: RequestEvent['locals']): string | null {
	return locals.token || null;
}
