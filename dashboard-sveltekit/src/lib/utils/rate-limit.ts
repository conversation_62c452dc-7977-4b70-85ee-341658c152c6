import crypto from 'crypto';

/**
 * ✅ Enhanced Rate Limiting System
 */

interface RateLimitRecord {
	count: number;
	resetTime: number;
	blocked: boolean;
	blockUntil?: number;
}

// Rate limit storage (ใน production ควรใช้ Redis)
const rateLimitStore = new Map<string, RateLimitRecord>();

// Rate limit configurations
export const RATE_LIMIT_CONFIG = {
	// Authentication endpoints
	AUTH_SIGNIN: {
		maxAttempts: 5,
		windowMs: 15 * 60 * 1000,
		blockDurationMs: 30 * 60 * 1000,
	}, // 5 attempts per 15min, block 30min
	AUTH_SIGNUP: {
		maxAttempts: 3,
		windowMs: 60 * 60 * 1000,
		blockDurationMs: 60 * 60 * 1000,
	}, // 3 attempts per hour, block 1hr
	AUTH_FORGOT_PASSWORD: {
		maxAttempts: 3,
		windowMs: 60 * 60 * 1000,
		blockDurationMs: 60 * 60 * 1000,
	},
	AUTH_REFRESH_TOKEN: {
		maxAttempts: 10,
		windowMs: 60 * 60 * 1000,
		blockDurationMs: 15 * 60 * 1000,
	},

	// API endpoints
	API_GENERAL: {
		maxAttempts: 100,
		windowMs: 60 * 60 * 1000,
		blockDurationMs: 5 * 60 * 1000,
	}, // 100 per hour, block 5min
	API_SENSITIVE: {
		maxAttempts: 20,
		windowMs: 60 * 60 * 1000,
		blockDurationMs: 15 * 60 * 1000,
	}, // 20 per hour, block 15min
} as const;

export type RateLimitType = keyof typeof RATE_LIMIT_CONFIG;

/**
 * Create rate limit identifier
 */
export function createRateLimitId(type: RateLimitType, identifier: string): string {
	// Hash the identifier for privacy
	const hashedId = crypto.createHash('sha256').update(identifier).digest('hex').substring(0, 16);
	return `${type}:${hashedId}`;
}

/**
 * Check rate limit
 */
export function checkRateLimit(
	type: RateLimitType,
	identifier: string
): {
	allowed: boolean;
	remaining: number;
	resetTime: number;
	blocked: boolean;
	blockUntil?: number;
} {
	const config = RATE_LIMIT_CONFIG[type];
	const key = createRateLimitId(type, identifier);
	const now = Date.now();

	let record = rateLimitStore.get(key);

	// Check if currently blocked
	if (record?.blocked && record.blockUntil && now < record.blockUntil) {
		return {
			allowed: false,
			remaining: 0,
			resetTime: record.resetTime,
			blocked: true,
			blockUntil: record.blockUntil,
		};
	}

	// Initialize or reset if window expired
	if (!record || now > record.resetTime) {
		record = {
			count: 1,
			resetTime: now + config.windowMs,
			blocked: false,
		};
		rateLimitStore.set(key, record);

		return {
			allowed: true,
			remaining: config.maxAttempts - 1,
			resetTime: record.resetTime,
			blocked: false,
		};
	}

	// Check if limit exceeded
	if (record.count >= config.maxAttempts) {
		// Block the identifier
		record.blocked = true;
		record.blockUntil = now + config.blockDurationMs;

		return {
			allowed: false,
			remaining: 0,
			resetTime: record.resetTime,
			blocked: true,
			blockUntil: record.blockUntil,
		};
	}

	// Increment counter
	record.count++;

	return {
		allowed: true,
		remaining: config.maxAttempts - record.count,
		resetTime: record.resetTime,
		blocked: false,
	};
}

/**
 * Clear rate limit (for successful operations)
 */
export function clearRateLimit(type: RateLimitType, identifier: string): void {
	const key = createRateLimitId(type, identifier);
	rateLimitStore.delete(key);
}

/**
 * Get rate limit info
 */
export function getRateLimitInfo(
	type: RateLimitType,
	identifier: string
): {
	remaining: number;
	resetTime: number;
	blocked: boolean;
	blockUntil?: number;
} {
	const key = createRateLimitId(type, identifier);
	const record = rateLimitStore.get(key);
	const config = RATE_LIMIT_CONFIG[type];
	const now = Date.now();

	if (!record || now > record.resetTime) {
		return {
			remaining: config.maxAttempts,
			resetTime: 0,
			blocked: false,
		};
	}

	return {
		remaining: Math.max(0, config.maxAttempts - record.count),
		resetTime: record.resetTime,
		blocked: record.blocked || false,
		blockUntil: record.blockUntil,
	};
}

/**
 * Cleanup expired records
 */
export function cleanupRateLimits(): void {
	const now = Date.now();

	for (const [key, record] of rateLimitStore.entries()) {
		// Remove if both window and block period expired
		if (now > record.resetTime && (!record.blockUntil || now > record.blockUntil)) {
			rateLimitStore.delete(key);
		}
	}
}

// Cleanup every 5 minutes
setInterval(cleanupRateLimits, 5 * 60 * 1000);
