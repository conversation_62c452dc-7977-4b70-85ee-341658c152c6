import { redirect } from '@sveltejs/kit';

export interface Locals {
	user?: any;
	token?: string;
}

export function requireAuth(locals: Locals, redirectUrl?: string) {
	if (!locals.user || !locals.token) {
		console.log('Server Auth: Authentication required but not found');
		console.log('Server Auth: locals.user:', locals.user ? 'exists' : 'null');
		console.log('Server Auth: locals.token:', locals.token ? 'exists' : 'null');

		// ✅ Throw redirect instead of generic error
		const returnUrl = redirectUrl ? `?returnUrl=${encodeURIComponent(redirectUrl)}` : '';
		throw redirect(302, `/signin${returnUrl}`);
	}

	console.log('Server Auth: Authentication successful');
	return {
		user: locals.user,
		token: locals.token,
	};
}

export function getAuthData(locals: Locals) {
	return {
		user: locals.user,
		token: locals.token,
	};
}

export function requireSiteAccess(locals: Locals, siteId: string, redirectUrl?: string) {
	// ✅ Use updated requireAuth
	const authData = requireAuth(locals, redirectUrl);

	// เพิ่มการตรวจสอบสิทธิ์เข้าถึง site ถ้าจำเป็น
	// เช่น ตรวจสอบว่า user เป็น owner หรือ member ของ site นี้

	return {
		...authData,
		siteId,
	};
}
