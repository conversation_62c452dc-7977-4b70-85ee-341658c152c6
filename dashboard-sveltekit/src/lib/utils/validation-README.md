# 🚀 Validation System สำหรับ Svelte 5 + Zod

ระบบ validation ที่ยืดหยุ่นและใช้งานง่าย คล้าย **vuelidate** แต่ใช้ **zod** และรองรับ **Svelte 5 runes**

## ✨ Features

- ✅ **Real-time validation** - ตรวจสอบขณะพิมพ์และคลิกจุดอื่น
- ✅ **Debounced validation** - ลดการ validate ที่ไม่จำเป็น
- ✅ **Svelte 5 runes support** - ใช้ $state และ $derived
- ✅ **vuelidate-like API** - API ที่คุ้นเคย
- ✅ **Zod integration** - ใช้ zod สำหรับ schema validation
- ✅ **TypeScript support** - Type safety เต็มรูปแบบ
- ✅ **Flexible configuration** - ปรับแต่งได้ตามต้องการ

## 🎯 การใช้งานพื้นฐาน

### 1. สร้าง Schema

```typescript
import { z } from 'zod';

const userSchema = z.object({
  email: z.string().email('รูปแบบอีเมลไม่ถูกต้อง'),
  password: z.string().min(6, 'รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร'),
  rememberMe: z.boolean().optional().default(false)
});

type UserData = z.infer<typeof userSchema>;
```

### 2. สร้าง Validator

```typescript
import { createReactiveValidator } from '$lib/utils/validation';

const initialData: UserData = {
  email: '',
  password: '',
  rememberMe: false
};

const v = createReactiveValidator(userSchema, initialData, {
  validateOnInput: true,
  validateOnBlur: true,
  debounceMs: 500,
  showErrorsOnlyAfterTouch: true
});
```

### 3. ใช้ใน Component

```svelte
<script lang="ts">
  import { createReactiveValidator } from '$lib/utils/validation';
  import { onMount } from 'svelte';

  // ... สร้าง validator

  onMount(() => {
    // Cleanup เมื่อ component ถูก destroy
    return () => {
      v.$cleanup();
    };
  });

  function handleSubmit() {
    if (v.$validate()) {
      console.log('Form is valid!', v.$data);
      // ส่งข้อมูล
    }
  }
</script>

<form on:submit|preventDefault={handleSubmit}>
  <!-- Email Input -->
  <input
    type="email"
    bind:value={v.fields.email.value}
    on:blur={v.fields.email.onBlur}
    class:error={v.fields.email.hasError}
  />
  {#if v.fields.email.error}
    <span class="error">{v.fields.email.error}</span>
  {/if}

  <!-- Password Input -->
  <input
    type="password"
    bind:value={v.fields.password.value}
    on:blur={v.fields.password.onBlur}
    class:error={v.fields.password.hasError}
  />
  {#if v.fields.password.error}
    <span class="error">{v.fields.password.error}</span>
  {/if}

  <!-- Submit Button -->
  <button type="submit" disabled={!v.$valid}>
    Submit
  </button>
</form>

<!-- Debug Info -->
<div class="debug">
  <p>Valid: {v.$valid}</p>
  <p>Dirty: {v.$dirty}</p>
  <p>Errors: {JSON.stringify(v.$errors)}</p>
</div>
```

## 🔧 Configuration Options

```typescript
interface ValidationOptions {
  validateOnInput?: boolean;        // validate ขณะพิมพ์ (default: true)
  validateOnBlur?: boolean;         // validate เมื่อคลิกจุดอื่น (default: true)
  validateOnMount?: boolean;        // validate เมื่อ mount (default: false)
  debounceMs?: number;             // เวลา debounce (default: 300ms)
  showErrorsOnlyAfterTouch?: boolean; // แสดง error หลัง touch (default: true)
}
```

## 📚 API Reference

### Field Properties

```typescript
v.fields.fieldName = {
  value: any;           // ค่าของ field
  error: string;        // error message
  hasError: boolean;    // มี error หรือไม่
  touched: boolean;     // ถูก touch แล้วหรือไม่
  dirty: boolean;       // มีการเปลี่ยนแปลงหรือไม่
  valid: boolean;       // valid หรือไม่
  invalid: boolean;     // invalid หรือไม่
  onInput: (value) => void;  // handler สำหรับ input
  onBlur: () => void;        // handler สำหรับ blur
  validate: () => string | null; // validate manual
  clearError: () => void;    // ลบ error
}
```

### Form Properties

```typescript
v = {
  $valid: boolean;         // form valid หรือไม่
  $invalid: boolean;       // form invalid หรือไม่
  $dirty: boolean;         // form มีการเปลี่ยนแปลงหรือไม่
  $pristine: boolean;      // form ยังไม่มีการเปลี่ยนแปลง
  $errors: Record<string, string>; // errors ทั้งหมด
  $hasErrors: boolean;     // มี errors หรือไม่
  $data: T;               // ข้อมูลปัจจุบัน
  
  // Methods
  $validate: () => boolean;     // validate ทั้ง form
  $reset: () => void;          // reset form
  $setErrors: (errors) => void; // set errors จากภายนอก
  $cleanup: () => void;        // cleanup timers
}
```

## 🎨 ตัวอย่างการใช้งานขั้นสูง

### 1. Custom Validation Messages

```typescript
const schema = z.object({
  email: z.string().email('กรุณากรอกอีเมลให้ถูกต้อง'),
  password: z.string()
    .min(8, 'รหัสผ่านต้องมีอย่างน้อย 8 ตัวอักษร')
    .regex(/[A-Z]/, 'ต้องมีตัวพิมพ์ใหญ่อย่างน้อย 1 ตัว')
    .regex(/[0-9]/, 'ต้องมีตัวเลขอย่างน้อย 1 ตัว')
});
```

### 2. Cross-field Validation

```typescript
const schema = z.object({
  password: z.string().min(6),
  confirmPassword: z.string()
}).refine(data => data.password === data.confirmPassword, {
  message: 'รหัสผ่านไม่ตรงกัน',
  path: ['confirmPassword']
});
```

### 3. Conditional Validation

```typescript
const schema = z.object({
  hasAddress: z.boolean(),
  address: z.string().optional()
}).refine(data => {
  if (data.hasAddress && !data.address) {
    return false;
  }
  return true;
}, {
  message: 'กรุณากรอกที่อยู่',
  path: ['address']
});
```

## 🔄 Migration จาก vuelidate

```javascript
// vuelidate (Vue)
const v$ = useVuelidate(rules, formData);

// เปลี่ยนเป็น (Svelte 5)
const v = createReactiveValidator(zodSchema, initialData);

// การใช้งานคล้ายกัน
v.fields.email.value     // แทน v$.email.$model
v.fields.email.error     // แทน v$.email.$error
v.fields.email.touched   // แทน v$.email.$touched
v.$valid                 // แทน v$.$invalid
```

## 🚨 Best Practices

1. **ใช้ debouncing**: ตั้งค่า `debounceMs` ให้เหมาะสม (300-500ms)
2. **Cleanup**: เรียก `$cleanup()` เมื่อ component destroy
3. **Show errors after touch**: ใช้ `showErrorsOnlyAfterTouch: true`
4. **Type safety**: ใช้ TypeScript เพื่อความปลอดภัย
5. **Reusable schemas**: แยก schema ออกเป็นไฟล์แยก

## 🐛 Troubleshooting

### Type Errors
```typescript
// ถ้ามี type error ใช้ type assertion
bind:value={v.fields.email.value as string}
bind:checked={v.fields.rememberMe.value as boolean}
```

### Performance Issues
```typescript
// เพิ่ม debounce time
const v = createReactiveValidator(schema, data, {
  debounceMs: 500 // เพิ่มจาก 300ms
});
```

### Memory Leaks
```typescript
// อย่าลืม cleanup
onMount(() => {
  return () => {
    v.$cleanup();
  };
});
```
