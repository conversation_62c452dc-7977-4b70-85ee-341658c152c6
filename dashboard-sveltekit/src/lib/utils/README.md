# Cloudinary Utilities

ไฟล์นี้มี utility functions สำหรับจัดการรูปภาพจาก Cloudinary

## การติดตั้ง

1. เปลี่ยน `cloudName` ใน `cloudinary.ts` เป็น Cloud Name ของคุณ
2. ตั้งค่า environment variables (ถ้าจำเป็น)

## วิธีการใช้งาน

### 1. สร้าง URL พื้นฐาน

```typescript
import { getCloudinaryUrl } from '$lib/utils/cloudinary';

const url = getCloudinaryUrl('sample/image.jpg', 'f_auto,q_auto');
// ผลลัพธ์: https://res.cloudinary.com/your-cloud-name/image/upload/f_auto,q_auto/sample/image.jpg
```

### 2. สร้าง Thumbnail

```typescript
import { getThumbnailUrl } from '$lib/utils/cloudinary';

const thumbnail = getThumbnailUrl('sample/image.jpg', 300, 300, 'fill');
// ผลลัพธ์: https://res.cloudinary.com/your-cloud-name/image/upload/c_fill,w_300,h_300,f_auto,q_auto/sample/image.jpg
```

### 3. สร้าง Avatar

```typescript
import { getAvatarUrl } from '$lib/utils/cloudinary';

const avatar = getAvatarUrl('sample/image.jpg', 100);
// ผลลัพธ์: https://res.cloudinary.com/your-cloud-name/image/upload/c_fill,w_100,h_100,f_auto,q_auto/sample/image.jpg
```

### 4. สร้าง Hero Image

```typescript
import { getHeroUrl } from '$lib/utils/cloudinary';

const hero = getHeroUrl('sample/image.jpg', 1200, 600);
// ผลลัพธ์: https://res.cloudinary.com/your-cloud-name/image/upload/c_fill,w_1200,h_600,f_auto,q_auto/sample/image.jpg
```

### 5. สร้าง Responsive Images

```typescript
import { getSrcSet } from '$lib/utils/cloudinary';

const sizes = [
	{ width: 300, height: 200 },
	{ width: 600, height: 400 },
	{ width: 900, height: 600 },
];

const srcset = getSrcSet('sample/image.jpg', sizes);
// ผลลัพธ์: https://res.cloudinary.com/your-cloud-name/image/upload/c_fill,w_300,h_200,f_auto,q_auto/sample/image.jpg 300w, https://res.cloudinary.com/your-cloud-name/image/upload/c_fill,w_600,h_400,f_auto,q_auto/sample/image.jpg 600w, https://res.cloudinary.com/your-cloud-name/image/upload/c_fill,w_900,h_600,f_auto,q_auto/sample/image.jpg 900w
```

### 6. สร้าง Placeholder

```typescript
import { getPlaceholderUrl } from '$lib/utils/cloudinary';

const placeholder = getPlaceholderUrl('sample/image.jpg');
// ผลลัพธ์: https://res.cloudinary.com/your-cloud-name/image/upload/c_fill,w_50,h_50,f_auto,q_auto,blur_1000/sample/image.jpg
```

## การใช้งานใน Svelte Component

```svelte
<script lang="ts">
	import { getThumbnailUrl } from '$lib/utils/cloudinary';

	const imageUrl = getThumbnailUrl('sample/image.jpg', 300, 300);
</script>

<img src={imageUrl} alt="Sample Image" />
```

## การใช้งานกับ Public ID หรือ URL

```typescript
// ใช้ Public ID
const url1 = getThumbnailUrl('folder/image.jpg', 300, 300);

// ใช้ Cloudinary URL
const url2 = getThumbnailUrl(
	'https://res.cloudinary.com/your-cloud-name/image/upload/v1234567890/folder/image.jpg',
	300,
	300
);
```

## Configuration

```typescript
const config = {
	cloudName: 'your-cloud-name',
	defaultTransformation: 'f_auto,q_auto',
	secure: true,
};

const url = getThumbnailUrl('sample/image.jpg', 300, 300, 'fill', config);
```

## Transformation Parameters

- `crop`: `fill`, `scale`, `fit`, `thumb`, `limit`
- `quality`: `auto`, `10`, `20`, `30`, `40`, `50`, `60`, `70`, `80`, `90`, `100`
- `format`: `auto`, `webp`, `jpg`, `png`, `gif`

## ตัวอย่างการใช้งานใน Dashboard

```svelte
<script lang="ts">
	import { getAvatarUrl } from '$lib/utils/cloudinary';

	let { site } = $props();

	const logoUrl = site?.seoSettings?.logo ? getAvatarUrl(site.seoSettings.logo, 100) : null;
</script>

{#if logoUrl}
	<img src={logoUrl} alt="Logo" class="w-24 h-24 rounded-full" />
{:else}
	<div class="w-24 h-24 rounded-full bg-primary/10 flex items-center justify-center">
		<Icon icon="solar:global-line-duotone" class="w-12 h-12 text-primary" />
	</div>
{/if}
```

## ข้อควรระวัง

1. เปลี่ยน `your-cloud-name` เป็น Cloud Name ของคุณ
2. ตรวจสอบว่า Public ID ถูกต้อง
3. ใช้ transformation ที่เหมาะสมกับ use case
4. ระวังเรื่อง performance เมื่อใช้ responsive images
