import { browser } from '$app/environment';
import { SECURITY_CONFIG } from './security';

// Cache entry interface
interface CacheEntry<T> {
	data: T;
	timestamp: number;
	ttl: number;
	accessCount: number;
	lastAccessed: number;
}

// Cache configuration
const CACHE_CONFIG = {
	maxSize: 100, // Maximum number of entries
	defaultTTL: SECURITY_CONFIG.TOKEN_CACHE_TIME,
	cleanupInterval: 5 * 60 * 1000, // 5 minutes
	maxAccessCount: 1000,
} as const;

class MemoryCache {
	private cache = new Map<string, CacheEntry<any>>();
	private cleanupTimer?: NodeJS.Timeout;

	constructor() {
		if (browser) {
			this.startCleanupTimer();
		}
	}

	private startCleanupTimer() {
		this.cleanupTimer = setInterval(() => {
			this.cleanup();
		}, CACHE_CONFIG.cleanupInterval);
	}

	private isExpired(entry: CacheEntry<any>): boolean {
		return Date.now() - entry.timestamp > entry.ttl;
	}

	private cleanup() {
		const now = Date.now();
		const entries = Array.from(this.cache.entries());

		// Remove expired entries
		for (const [key, entry] of entries) {
			if (this.isExpired(entry)) {
				this.cache.delete(key);
			}
		}

		// If still over limit, remove least recently used
		if (this.cache.size > CACHE_CONFIG.maxSize) {
			const sortedEntries = entries
				.filter(([key]) => this.cache.has(key)) // Still exists after cleanup
				.sort(([, a], [, b]) => a.lastAccessed - b.lastAccessed);

			const toRemove = sortedEntries.slice(0, this.cache.size - CACHE_CONFIG.maxSize);
			for (const [key] of toRemove) {
				this.cache.delete(key);
			}
		}
	}

	set<T>(key: string, data: T, ttl: number = CACHE_CONFIG.defaultTTL): void {
		const now = Date.now();

		this.cache.set(key, {
			data,
			timestamp: now,
			ttl,
			accessCount: 0,
			lastAccessed: now,
		});

		// Trigger cleanup if over size limit
		if (this.cache.size > CACHE_CONFIG.maxSize) {
			this.cleanup();
		}
	}

	get<T>(key: string): T | null {
		const entry = this.cache.get(key);

		if (!entry) {
			return null;
		}

		if (this.isExpired(entry)) {
			this.cache.delete(key);
			return null;
		}

		// Update access statistics
		entry.accessCount++;
		entry.lastAccessed = Date.now();

		return entry.data as T;
	}

	has(key: string): boolean {
		const entry = this.cache.get(key);

		if (!entry) {
			return false;
		}

		if (this.isExpired(entry)) {
			this.cache.delete(key);
			return false;
		}

		return true;
	}

	delete(key: string): boolean {
		return this.cache.delete(key);
	}

	clear(): void {
		this.cache.clear();
	}

	size(): number {
		return this.cache.size;
	}

	keys(): string[] {
		return Array.from(this.cache.keys());
	}

	// Get cache statistics
	getStats(): {
		size: number;
		maxSize: number;
		hitRate: number;
		entries: Array<{
			key: string;
			size: number;
			accessCount: number;
			lastAccessed: Date;
			ttl: number;
			isExpired: boolean;
		}>;
	} {
		const entries = Array.from(this.cache.entries()).map(([key, entry]) => ({
			key,
			size: JSON.stringify(entry.data).length,
			accessCount: entry.accessCount,
			lastAccessed: new Date(entry.lastAccessed),
			ttl: entry.ttl,
			isExpired: this.isExpired(entry),
		}));

		const totalAccess = entries.reduce((sum, entry) => sum + entry.accessCount, 0);
		const hitRate = totalAccess > 0 ? (totalAccess / (totalAccess + 1)) * 100 : 0;

		return {
			size: this.cache.size,
			maxSize: CACHE_CONFIG.maxSize,
			hitRate: Math.round(hitRate * 100) / 100,
			entries,
		};
	}

	destroy(): void {
		if (this.cleanupTimer) {
			clearInterval(this.cleanupTimer);
		}
		this.cache.clear();
	}
}

// Token cache for authentication
class TokenCache {
	private cache = new MemoryCache();
	private readonly TOKEN_PREFIX = 'token:';
	private readonly USER_PREFIX = 'user:';

	cacheToken(userId: string, token: string, ttl: number = CACHE_CONFIG.defaultTTL): void {
		this.cache.set(`${this.TOKEN_PREFIX}${userId}`, token, ttl);
	}

	getToken(userId: string): string | null {
		return this.cache.get(`${this.TOKEN_PREFIX}${userId}`);
	}

	cacheUser(userId: string, userData: any, ttl: number = CACHE_CONFIG.defaultTTL): void {
		this.cache.set(`${this.USER_PREFIX}${userId}`, userData, ttl);
	}

	getUser(userId: string): any | null {
		return this.cache.get(`${this.USER_PREFIX}${userId}`);
	}

	invalidateUser(userId: string): void {
		this.cache.delete(`${this.TOKEN_PREFIX}${userId}`);
		this.cache.delete(`${this.USER_PREFIX}${userId}`);
	}

	clear(): void {
		this.cache.clear();
	}

	getStats() {
		return this.cache.getStats();
	}
}

// API response cache
class APICache {
	private cache = new MemoryCache();

	private generateKey(url: string, method: string = 'GET', params?: Record<string, any>): string {
		const paramString = params ? JSON.stringify(params) : '';
		return `${method}:${url}:${paramString}`;
	}

	cacheResponse(
		url: string,
		method: string,
		response: any,
		ttl: number = 60000,
		params?: Record<string, any>
	): void {
		const key = this.generateKey(url, method, params);
		this.cache.set(key, response, ttl);
	}

	getResponse(url: string, method: string = 'GET', params?: Record<string, any>): any | null {
		const key = this.generateKey(url, method, params);
		return this.cache.get(key);
	}

	invalidate(url: string, method?: string): void {
		if (method) {
			const key = this.generateKey(url, method);
			this.cache.delete(key);
		} else {
			// Invalidate all methods for this URL
			const keys = this.cache.keys().filter(key => key.includes(url));
			keys.forEach(key => this.cache.delete(key));
		}
	}

	clear(): void {
		this.cache.clear();
	}

	getStats() {
		return this.cache.getStats();
	}
}

// Export cache instances
export const tokenCache = new TokenCache();
export const apiCache = new APICache();
export const memoryCache = new MemoryCache();

// Cache utilities
export const cacheUtils = {
	// Memoization decorator
	memoize<T extends (...args: any[]) => any>(
		fn: T,
		keyGenerator?: (...args: Parameters<T>) => string,
		ttl: number = CACHE_CONFIG.defaultTTL
	): T {
		const cache = new Map<string, { result: ReturnType<T>; timestamp: number }>();

		return ((...args: Parameters<T>): ReturnType<T> => {
			const key = keyGenerator ? keyGenerator(...args) : JSON.stringify(args);
			const cached = cache.get(key);
			const now = Date.now();

			if (cached && now - cached.timestamp < ttl) {
				return cached.result;
			}

			const result = fn(...args);
			cache.set(key, { result, timestamp: now });

			return result;
		}) as T;
	},

	// Debounced cache setter
	debouncedSet: (() => {
		const timers = new Map<string, NodeJS.Timeout>();

		return <T>(
			cache: MemoryCache,
			key: string,
			data: T,
			delay: number = 1000,
			ttl?: number
		): void => {
			const existingTimer = timers.get(key);
			if (existingTimer) {
				clearTimeout(existingTimer);
			}

			const timer = setTimeout(() => {
				cache.set(key, data, ttl);
				timers.delete(key);
			}, delay);

			timers.set(key, timer);
		};
	})(),

	// Get all cache statistics
	getAllStats() {
		return {
			tokenCache: tokenCache.getStats(),
			apiCache: apiCache.getStats(),
			memoryCache: memoryCache.getStats(),
		};
	},

	// Clear all caches
	clearAll() {
		tokenCache.clear();
		apiCache.clear();
		memoryCache.clear();
	},
};

// Cleanup on page unload (browser only)
if (browser) {
	window.addEventListener('beforeunload', () => {
		memoryCache.destroy();
	});
}
