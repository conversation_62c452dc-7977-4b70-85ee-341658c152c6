import { goto } from '$app/navigation';
import { page } from '$app/state';

export interface PaginationData {
	page: number;
	limit: number;
	total: number;
	totalPages: number;
}

export interface PaginationParams {
	page?: number;
	limit?: number;
	search?: string;
	status?: string;
	sort?: string;
	order?: 'asc' | 'desc';
}

/**
 * สร้าง URL สำหรับ pagination
 */
export function createPaginationUrl(params: PaginationParams, baseUrl?: string): string {
	const url = new URL(baseUrl || window.location.href);

	// อัปเดต query parameters
	if (params.page !== undefined) url.searchParams.set('page', params.page.toString());
	if (params.limit !== undefined) url.searchParams.set('limit', params.limit.toString());
	if (params.search !== undefined) url.searchParams.set('search', params.search);
	if (params.status !== undefined) url.searchParams.set('status', params.status);
	if (params.sort !== undefined) url.searchParams.set('sort', params.sort);
	if (params.order !== undefined) url.searchParams.set('order', params.order);

	return url.toString();
}

/**
 * เปลี่ยนหน้า pagination
 */
export function goToPage(page: number, params?: Omit<PaginationParams, 'page'>): void {
	const currentParams = new URLSearchParams(window.location.search);

	// อัปเดต page
	currentParams.set('page', page.toString());

	// อัปเดต parameters อื่นๆ ถ้ามี
	if (params) {
		if (params.limit !== undefined) currentParams.set('limit', params.limit.toString());
		if (params.search !== undefined) currentParams.set('search', params.search);
		if (params.status !== undefined) currentParams.set('status', params.status);
		if (params.sort !== undefined) currentParams.set('sort', params.sort);
		if (params.order !== undefined) currentParams.set('order', params.order);
	}

	const newUrl = `${window.location.pathname}?${currentParams.toString()}`;
	goto(newUrl);
}

/**
 * สร้าง pagination controls
 */
export function createPaginationControls(pagination: PaginationData, baseUrl?: string) {
	const { page, limit, total, totalPages } = pagination;

	const controls = {
		currentPage: page,
		totalPages,
		total,
		limit,
		hasNext: page < totalPages,
		hasPrev: page > 1,
		pages: [] as number[],
	};

	// สร้างรายการหน้า
	const startPage = Math.max(1, page - 2);
	const endPage = Math.min(totalPages, page + 2);

	for (let i = startPage; i <= endPage; i++) {
		controls.pages.push(i);
	}

	return controls;
}

/**
 * คำนวณ pagination info
 */
export function calculatePaginationInfo(pagination: PaginationData) {
	const { page, limit, total } = pagination;
	const start = (page - 1) * limit + 1;
	const end = Math.min(page * limit, total);

	return {
		start,
		end,
		total,
		showing: `${start}-${end} จาก ${total} รายการ`,
	};
}
