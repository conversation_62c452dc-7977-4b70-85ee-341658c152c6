import { browser } from '$app/environment';

// Rate limiting storage
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

// Security constants
export const SECURITY_CONFIG = {
	MAX_LOGIN_ATTEMPTS: 5,
	LOCKOUT_DURATION: 15 * 60 * 1000, // 15 minutes
	TOKEN_CACHE_TIME: 5 * 60 * 1000, // 5 minutes
	CSRF_TOKEN_LENGTH: 32,
	SESSION_TIMEOUT: 30 * 60 * 1000, // 30 minutes
	PASSWORD_MIN_LENGTH: 8,
	MAX_REQUEST_SIZE: 1024 * 1024, // 1MB
} as const;

// Generate CSRF token
export function generateCSRFToken(): string {
	if (browser) {
		// Client-side: use Web Crypto API
		const array = new Uint8Array(SECURITY_CONFIG.CSRF_TOKEN_LENGTH);
		if (typeof window !== 'undefined' && window.crypto && window.crypto.getRandomValues) {
			window.crypto.getRandomValues(array);
			return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
		} else {
			// Fallback for older browsers
			return (
				Math.random().toString(36).substring(2) +
				Math.random().toString(36).substring(2) +
				Date.now().toString(36)
			);
		}
	} else {
		// Server-side: use fallback random generation
		// For production, use security.server.ts functions instead
		return (
			Math.random().toString(36).substring(2) +
			Math.random().toString(36).substring(2) +
			Date.now().toString(36) +
			Math.random().toString(36).substring(2)
		);
	}
}

// Validate CSRF token
export function validateCSRFToken(token: string, expectedToken: string): boolean {
	if (!token || !expectedToken) return false;
	return token === expectedToken;
}

// Rate limiting
export function checkRateLimit(
	identifier: string,
	maxAttempts: number = SECURITY_CONFIG.MAX_LOGIN_ATTEMPTS
): boolean {
	const now = Date.now();
	const record = rateLimitStore.get(identifier);

	if (!record) {
		rateLimitStore.set(identifier, {
			count: 1,
			resetTime: now + SECURITY_CONFIG.LOCKOUT_DURATION,
		});
		return true;
	}

	if (now > record.resetTime) {
		// Reset the counter
		rateLimitStore.set(identifier, {
			count: 1,
			resetTime: now + SECURITY_CONFIG.LOCKOUT_DURATION,
		});
		return true;
	}

	if (record.count >= maxAttempts) {
		return false; // Rate limited
	}

	record.count++;
	return true;
}

// Get rate limit info
export function getRateLimitInfo(identifier: string): {
	remaining: number;
	resetTime: number;
} {
	const record = rateLimitStore.get(identifier);
	const now = Date.now();

	if (!record || now > record.resetTime) {
		return { remaining: SECURITY_CONFIG.MAX_LOGIN_ATTEMPTS, resetTime: 0 };
	}

	return {
		remaining: Math.max(0, SECURITY_CONFIG.MAX_LOGIN_ATTEMPTS - record.count),
		resetTime: record.resetTime,
	};
}

// Clear rate limit for identifier
export function clearRateLimit(identifier: string): void {
	rateLimitStore.delete(identifier);
}

// Sanitize input
export function sanitizeInput(input: string): string {
	return input
		.replace(/[<>]/g, '') // Remove potential HTML tags
		.replace(/javascript:/gi, '') // Remove javascript: protocol
		.replace(/on\w+=/gi, '') // Remove event handlers
		.trim();
}

// Validate email format
export function isValidEmail(email: string): boolean {
	const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
	return emailRegex.test(email) && email.length <= 254;
}

// Validate password strength
export function validatePasswordStrength(password: string): {
	isValid: boolean;
	errors: string[];
} {
	const errors: string[] = [];

	if (password.length < SECURITY_CONFIG.PASSWORD_MIN_LENGTH) {
		errors.push(`รหัสผ่านต้องมีอย่างน้อย ${SECURITY_CONFIG.PASSWORD_MIN_LENGTH} ตัวอักษร`);
	}

	if (!/[A-Z]/.test(password)) {
		errors.push('รหัสผ่านต้องมีตัวอักษรพิมพ์ใหญ่อย่างน้อย 1 ตัว');
	}

	if (!/[a-z]/.test(password)) {
		errors.push('รหัสผ่านต้องมีตัวอักษรพิมพ์เล็กอย่างน้อย 1 ตัว');
	}

	if (!/\d/.test(password)) {
		errors.push('รหัสผ่านต้องมีตัวเลขอย่างน้อย 1 ตัว');
	}

	if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
		errors.push('รหัสผ่านต้องมีอักขระพิเศษอย่างน้อย 1 ตัว');
	}

	return {
		isValid: errors.length === 0,
		errors,
	};
}

// Get client IP address
export function getClientIP(request: Request): string {
	return (
		request.headers.get('x-forwarded-for') ||
		request.headers.get('x-real-ip') ||
		request.headers.get('cf-connecting-ip') ||
		'unknown'
	);
}

// Get user agent
export function getUserAgent(request: Request): string {
	return request.headers.get('user-agent') || 'unknown';
}

// Generate session ID
export function generateSessionId(): string {
	return generateCSRFToken();
}

// Hash sensitive data for logging
export function hashForLogging(data: string): string {
	// Use simple hash for both client and server (consistent behavior)
	let hash = 0;
	for (let i = 0; i < data.length; i++) {
		const char = data.charCodeAt(i);
		hash = (hash << 5) - hash + char;
		hash = hash & hash; // Convert to 32-bit integer
	}
	return Math.abs(hash).toString(16);
}

// Clean up old rate limit entries
export function cleanupRateLimit(): void {
	const now = Date.now();
	for (const [key, record] of rateLimitStore.entries()) {
		if (now > record.resetTime) {
			rateLimitStore.delete(key);
		}
	}
}

// Setup periodic cleanup
if (browser) {
	setInterval(cleanupRateLimit, 5 * 60 * 1000); // Clean up every 5 minutes
}
