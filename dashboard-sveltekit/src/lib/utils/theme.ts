// DaisyUI Theme Utilities

export const DAISYUI_THEMES = [
	'light',
	'dark',
	'cupcake',
	'bumblebee',
	'emerald',
	'corporate',
	'synthwave',
	'retro',
	'cyberpunk',
	'valentine',
	'halloween',
	'garden',
	'forest',
	'aqua',
	'lofi',
	'pastel',
	'fantasy',
	'wireframe',
	'black',
	'luxury',
	'dracula',
	'cmyk',
	'autumn',
	'business',
	'acid',
	'lemonade',
	'night',
	'coffee',
	'winter',
	'dim',
	'nord',
	'sunset',
] as const;

export type DaisyUITheme = (typeof DAISYUI_THEMES)[number];

export interface ThemeConfig {
	name: string;
	displayName: string;
	isDark: boolean;
	colors: {
		primary: string;
		secondary: string;
		accent: string;
		neutral: string;
		base: string;
		info: string;
		success: string;
		warning: string;
		error: string;
	};
}

export const THEME_CONFIGS: Record<DaisyUITheme, ThemeConfig> = {
	light: {
		name: 'light',
		displayName: 'Light',
		isDark: false,
		colors: {
			primary: '#570df8',
			secondary: '#f000b8',
			accent: '#37cdbe',
			neutral: '#3d4451',
			base: '#ffffff',
			info: '#3abff8',
			success: '#36d399',
			warning: '#fbbd23',
			error: '#f87272',
		},
	},
	dark: {
		name: 'dark',
		displayName: 'Dark',
		isDark: true,
		colors: {
			primary: '#661ae6',
			secondary: '#d926aa',
			accent: '#1fb2a5',
			neutral: '#191d24',
			base: '#2a303c',
			info: '#3abff8',
			success: '#36d399',
			warning: '#fbbd23',
			error: '#f87272',
		},
	},
	cupcake: {
		name: 'cupcake',
		displayName: 'Cupcake',
		isDark: false,
		colors: {
			primary: '#65c3c8',
			secondary: '#ef9fbc',
			accent: '#eeaf3a',
			neutral: '#291334',
			base: '#faf7f5',
			info: '#3abff8',
			success: '#36d399',
			warning: '#fbbd23',
			error: '#f87272',
		},
	},
	// Add more theme configs as needed...
	business: {
		name: 'business',
		displayName: 'Business',
		isDark: true,
		colors: {
			primary: '#1c4ed8',
			secondary: '#7c3aed',
			accent: '#0891b2',
			neutral: '#1e293b',
			base: '#ffffff',
			info: '#0ea5e9',
			success: '#10b981',
			warning: '#f59e0b',
			error: '#ef4444',
		},
	},
};

export function getThemeConfig(theme: DaisyUITheme): ThemeConfig {
	return THEME_CONFIGS[theme] || THEME_CONFIGS.light;
}

export function setTheme(theme: DaisyUITheme): void {
	if (typeof document !== 'undefined') {
		document.documentElement.setAttribute('data-theme', theme);
		localStorage.setItem('theme', theme);
	}
}

export function getTheme(): DaisyUITheme {
	if (typeof window !== 'undefined') {
		const stored = localStorage.getItem('theme') as DaisyUITheme;
		if (stored && DAISYUI_THEMES.includes(stored)) {
			return stored;
		}

		// Auto-detect based on system preference
		const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
		return prefersDark ? 'dark' : 'light';
	}
	return 'light';
}

export function initTheme(): void {
	if (typeof document !== 'undefined') {
		const theme = getTheme();
		setTheme(theme);
	}
}

export function toggleTheme(): void {
	const current = getTheme();
	const newTheme = current === 'light' ? 'dark' : 'light';
	setTheme(newTheme);
}

export function isValidTheme(theme: string): theme is DaisyUITheme {
	return DAISYUI_THEMES.includes(theme as DaisyUITheme);
}
