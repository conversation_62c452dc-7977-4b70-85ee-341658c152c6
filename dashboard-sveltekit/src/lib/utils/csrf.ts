import type { RequestEvent } from '@sveltejs/kit';
import crypto from 'crypto';

/**
 * ✅ CSRF Protection Utilities
 */

export function generateCSRFToken(): string {
	return crypto.randomBytes(32).toString('hex');
}

export function validateCSRFToken(event: RequestEvent): boolean {
	const cookieToken = event.cookies.get('csrf_token');
	const headerToken = event.request.headers.get('x-csrf-token');
	const formToken = event.url.searchParams.get('csrf_token');

	if (!cookieToken) {
		return false;
	}

	// Check header first, then form data
	const submittedToken = headerToken || formToken;

	if (!submittedToken) {
		return false;
	}

	// Use timing-safe comparison
	return crypto.timingSafeEqual(
		Buffer.from(cookieToken, 'hex'),
		Buffer.from(submittedToken, 'hex')
	);
}

export function requireCSRF(event: RequestEvent): void {
	// Skip CSRF for GET requests
	if (event.request.method === 'GET') {
		return;
	}

	if (!validateCSRFToken(event)) {
		throw new Error('CSRF token validation failed');
	}
}
