export interface Subscription {
	_id: string;
	siteId: string;
	userId: string;
	packageType: 'daily' | 'weekly' | 'monthly' | 'yearly' | 'permanent';
	status: 'active' | 'paused' | 'cancelled' | 'expired';
	autoRenew: boolean;
	startDate: string;
	endDate?: string;
	nextRenewalDate?: string;
	lastRenewalDate?: string;
	pricing: {
		amount: number;
		currency: string;
		originalPrice: number;
		discount?: number;
		discountType?: 'percentage' | 'fixed';
	};
	billing: {
		paymentMethod: string;
		paymentReference?: string;
		nextBillingDate?: string;
		lastBillingDate?: string;
		billingHistory: Array<{
			date: string;
			amount: number;
			status: 'success' | 'failed' | 'pending';
			reference?: string;
			failureReason?: string;
		}>;
	};
	renewalHistory: Array<{
		renewedAt: string;
		packageType: string;
		daysAdded: number;
		amount: number;
		method: 'auto' | 'manual';
		previousExpiry: string;
		newExpiry: string;
	}>;
	notifications: {
		expiryWarning: boolean;
		renewalSuccess: boolean;
		renewalFailure: boolean;
		daysBeforeExpiry: number;
	};
	stats: {
		totalRenewals: number;
		totalSpent: number;
		averageRenewalAmount: number;
		longestActiveStreak: number;
		currentActiveStreak: number;
	};
	createdAt: string;
	updatedAt: string;
}

export interface PackageInfo {
	type: string;
	name: string;
	description: string;
	days: number;
	moneyPoint: number;
	features: string[];
}

export interface SubscriptionNotification {
	_id: string;
	siteId: string;
	userId: string;
	subscriptionId?: string;
	type:
		| 'expiry_warning'
		| 'renewal_success'
		| 'renewal_failure'
		| 'auto_renewal_disabled'
		| 'payment_failed';
	title: string;
	message: string;
	isRead: boolean;
	data?: any;
	createdAt: string;
}

export interface CreateSubscriptionData {
	packageType: 'monthly' | 'yearly' | 'permanent';
	autoRenew?: boolean;
	paymentMethod?: string;
	discountCode?: string;
}

export interface DiscountValidation {
	valid: boolean;
	message: string;
	discount?: number;
}

export interface SubscriptionStats {
	totalActive: number;
	totalExpired: number;
	totalCancelled: number;
	totalSpent: number;
	averageRenewalRate: number;
}

export interface BillingHistory {
	date: string;
	amount: number;
	status: 'success' | 'failed' | 'pending';
	reference?: string;
	failureReason?: string;
}

export interface RenewalHistory {
	renewedAt: string;
	packageType: string;
	daysAdded: number;
	amount: number;
	method: 'auto' | 'manual';
	previousExpiry: string;
	newExpiry: string;
}
