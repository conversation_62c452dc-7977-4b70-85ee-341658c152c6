export interface Customer {
	_id: string;
	firstName?: string;
	lastName?: string;
	email: string;
	phone?: string;
	avatar: string;
	cover: string;
	isEmailVerified: boolean;
	profilePicture?: string;
	moneyPoint: number;
	goldPoint: number;
	siteId: string;
	createdAt: string;
	updatedAt: string;
}

export interface CustomerListResponse {
	success: boolean;
	data?: {
		customers: Customer[];
		pagination: {
			page: number;
			limit: number;
			total: number;
			totalPages: number;
		};
	};
	error?: string;
}

export interface CustomerResponse {
	success: boolean;
	data?: Customer | null;
	error?: string;
}
