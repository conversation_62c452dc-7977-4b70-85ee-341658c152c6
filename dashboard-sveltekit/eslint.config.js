import js from '@eslint/js';
import ts from '@typescript-eslint/eslint-plugin';
import tsParser from '@typescript-eslint/parser';
import svelte from 'eslint-plugin-svelte';
import prettier from 'eslint-config-prettier';

/** @type {import('eslint').Linter.FlatConfig[]} */
export default [
	js.configs.recommended,
	{
		files: ['**/*.{js,ts,jsx,tsx}'],
		plugins: {
			'@typescript-eslint': ts
		},
		languageOptions: {
			parser: tsParser,
			parserOptions: {
				ecmaVersion: 2022,
				sourceType: 'module'
			},
			globals: {
				console: 'readonly',
				window: 'readonly',
				document: 'readonly',
				Response: 'readonly',
				Request: 'readonly',
				fetch: 'readonly',
				URL: 'readonly',
				URLSearchParams: 'readonly'
			}
		},
		rules: {
			'@typescript-eslint/no-unused-vars': 'warn',
			'@typescript-eslint/no-explicit-any': 'off',
			'@typescript-eslint/no-non-null-assertion': 'warn',
			'prefer-const': 'warn',
			'no-const-assign': 'warn',
			'no-undef': 'warn',
			'no-unused-vars': 'off'
		}
	},
	...svelte.configs['flat/recommended'],
	{
		files: ['**/*.svelte'],
		languageOptions: {
			parserOptions: {
				parser: tsParser
			},
			globals: {
				console: 'readonly',
				window: 'readonly',
				document: 'readonly',
				$$Generic: 'readonly',
				$$Props: 'readonly',
				$$Events: 'readonly',
				$$Slots: 'readonly'
			}
		},
		rules: {
			'@typescript-eslint/no-unused-vars': 'off',
			'@typescript-eslint/no-explicit-any': 'off',
			'svelte/no-unused-svelte-ignore': 'off',
			'svelte/valid-prop-names-in-kit-pages': 'off',
			'svelte/require-each-key': 'warn',
			'no-const-assign': 'off',
			'no-undef': 'off',
			'no-unused-vars': 'off'
		}
	},
	prettier,
	{
		ignores: [
			'build/',
			'.svelte-kit/',
			'dist/',
			'node_modules/',
			'static/',
			'coverage/',
			'**/*.config.js',
			'**/*.config.ts'
		]
	}
];
