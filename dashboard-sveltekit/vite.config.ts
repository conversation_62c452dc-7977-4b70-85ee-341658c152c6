import { sveltekit } from '@sveltejs/kit/vite';
import tailwindcss from '@tailwindcss/vite';
import { defineConfig } from 'vite';
import devtoolsJson from 'vite-plugin-devtools-json';

export default defineConfig({
	plugins: [tailwindcss(), sveltekit(), devtoolsJson()],
	server: {
		port: process.env.VITE_PORT ? parseInt(process.env.VITE_PORT) : 8000,
		host: true,
	},
	optimizeDeps: {
		include: ['intl-messageformat', 'deepmerge'],
	},
	// css: {
	// 	preprocessorOptions: {
	// 		scss: {
	// 			additionalData: '@import "tailwindcss";'
	// 		}
	// 	}
	// }
});
