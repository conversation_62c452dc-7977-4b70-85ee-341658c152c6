# การตั้งค่า Google reCAPTCHA

## ปัญหาที่พบ
```
recaptcha__th.js:418 Uncaught Error: Missing required parameters: sitekey
```

## วิธีแก้ไข

### 1. สร้างไฟล์ .env
สร้างไฟล์ `.env` ในโฟลเดอร์ `dashboard-sveltekit/`:

```bash
# dashboard-sveltekit/.env
VITE_RECAPTCHA_SITE_KEY=your_recaptcha_site_key_here
```

### 2. สำหรับ Development
หากยังไม่มี reCAPTCHA site key จริง สามารถใช้ test key ได้:

```bash
# dashboard-sveltekit/.env
VITE_RECAPTCHA_SITE_KEY=6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI
```

### 3. สำหรับ Production
1. ไปที่ [Google reCAPTCHA Console](https://www.google.com/recaptcha/admin)
2. สร้าง reCAPTCHA site key ใหม่
3. เลือก reCAPTCHA type (v2 หรือ v3)
4. ตั้งค่า domain ที่อนุญาต
5. คัดลอก site key มาใส่ในไฟล์ .env

### 4. รีสตาร์ท Development Server
หลังจากสร้างไฟล์ .env แล้ว ให้รีสตาร์ท development server:

```bash
npm run dev
# หรือ
bun dev
```

## การตรวจสอบ

### ตรวจสอบ Environment Variable
เปิด Developer Tools (F12) และดูใน Console:
- หากไม่มี siteKey จะแสดงข้อความแจ้งเตือน
- หากมี siteKey จะโหลด reCAPTCHA ปกติ

### ตรวจสอบใน Component
Recaptcha component จะแสดงข้อความแจ้งเตือนใน UI หากไม่มี siteKey

## หมายเหตุ

- Test key จะทำงานเฉพาะใน localhost เท่านั้น
- สำหรับ production ต้องใช้ site key จริงจาก Google
- ต้องตั้งค่า domain ใน Google reCAPTCHA Console ให้ตรงกับ domain ที่ใช้งาน 