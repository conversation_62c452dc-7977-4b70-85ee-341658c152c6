import { Elysia } from 'elysia';
import { config } from '@/core/config/environment';

// Import all route modules
import { userRoutes } from '@/modules/user/user.routes';
import { monitoringRoutes } from '@/modules/user/monitoring.routes';
import { customerRoutes } from '@/modules/customer/customer.routes';
import { customerAdminRoutes } from '@/modules/customer/customer.routes';
import { siteRoutes } from '@/modules/site/site.routes';
import { productRoutes } from '@/modules/product/product.routes';
import { orderRoutes, preOrderRoutes } from '@/modules/order';
import { reviewRoutes } from '@/modules/product/review.routes';
import { commentRoutes } from '@/modules/product/comment.routes';
import { inventoryRoutes } from '@/modules/inventory';
import { shippingRoutes } from '@/modules/shipping';
import { discountRoutes } from '@/modules/discount';
import { brandRoutes } from '@/modules/brand';
import { analyticsRoutes } from '@/modules/analytics/analytics.routes';
import { affiliateRoutes } from '@/modules/affiliate';
import { chatRoutes } from '@/modules/chat';
import { roleRoutes } from '@/modules/role/role.routes';
import { topupRoutes } from '@/modules/topup/topup.routes';
import { menuRoutes } from '@/modules/menu/menu.routes';
import { adsRoutes } from '@/modules/ads/ads.routes';
import { mediaRoutes } from '@/modules/media/media.routes';
import { notificationRoutes } from '@/modules/notification/notification.routes';
import { subscriptionRoutes } from '@/modules/subscription/subscription.routes';
import { searchRoutes } from '@/modules/search/search.routes';
import { dashboardRoutes } from '@/modules/dashboard/dashboard.routes';

/**
 * Health check และ basic routes
 */
export function setupBasicRoutes(app: Elysia) {
	return app
		.get('/', () => ({
			message: 'Auth Template API is running',
			version: '1.0.0',
			environment: config.NODE_ENV,
			timestamp: new Date().toISOString(),
		}))
		.get('/favicon.ico', (c: Context) => {
			c.set.status = 204;
			return new Response(null, { status: 204 });
		})
		.get('/health', () => ({
			status: 'ok',
			timestamp: new Date().toISOString(),
			environment: config.NODE_ENV,
			version: '1.0.0',
			uptime: process.uptime(),
			memory: process.memoryUsage(),
			pid: process.pid,
		}))
		.get('/ping', () => ({ message: 'pong', timestamp: new Date().toISOString() }));
}

/**
 * User management routes
 */
export function setupUserRoutes(app: Elysia) {
	return app.use(userRoutes).use(monitoringRoutes).use(customerRoutes).use(roleRoutes);
}

/**
 * Site management routes
 */
export function setupSiteRoutes(app: Elysia) {
	return app.use(siteRoutes).use(menuRoutes).use(mediaRoutes);
}

/**
 * E-commerce routes
 */
export function setupEcommerceRoutes(app: Elysia) {
	return app
		.use(productRoutes)
		.use(reviewRoutes)
		.use(commentRoutes)
		.use(orderRoutes)
		.use(preOrderRoutes)
		.use(inventoryRoutes)
		.use(shippingRoutes)
		.use(discountRoutes)
		.use(brandRoutes)
		.use(searchRoutes);
}

/**
 * Business features routes
 */
export function setupBusinessRoutes(app: Elysia) {
	return app
		.use(analyticsRoutes)
		.use(affiliateRoutes)
		.use(subscriptionRoutes)
		.use(topupRoutes)
		.use(adsRoutes)
		.use(customerRoutes)
		.use(customerAdminRoutes)
		.use(siteRoutes)
		.use(dashboardRoutes);
}

/**
 * Communication routes
 */
export function setupCommunicationRoutes(app: Elysia) {
	return app.use(chatRoutes).use(notificationRoutes);
}

/**
 * Setup all routes in logical groups
 */
export function setupAllRoutes(app: Elysia) {
	return setupCommunicationRoutes(
		setupBusinessRoutes(setupEcommerceRoutes(setupSiteRoutes(setupUserRoutes(setupBasicRoutes(app)))))
	);
}
