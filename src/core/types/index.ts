// ✅ Core Types - Re-export all types for easy importing

// Error types
export * from './error.types';

// Common utility types
export type ID = string;
export type Timestamp = Date | string;
export type Optional<T> = T | undefined;
export type Nullable<T> = T | null;

// Database document base interface
export interface BaseDocument {
	_id: string;
	createdAt: Date;
	updatedAt: Date;
}

// API Response types
export interface ApiResponse<T = unknown> {
	success: boolean;
	data?: T;
	error?: string;
	message?: string;
	timestamp: string;
	statusCode?: number;
}

export interface PaginationMeta {
	page: number;
	limit: number;
	total: number;
	totalPages: number;
	hasNext: boolean;
	hasPrev: boolean;
}

export interface PaginatedResponse<T = unknown> extends ApiResponse<T[]> {
	pagination: PaginationMeta;
}

// Query parameters
export interface PaginationQuery {
	page?: string | number;
	limit?: string | number;
	search?: string;
	sort?: string;
	order?: 'asc' | 'desc';
}

export interface FilterQuery extends PaginationQuery {
	status?: string;
	category?: string;
	startDate?: string;
	endDate?: string;
}

// User roles and permissions
export type UserRole = 'admin' | 'user' | 'moderator';
export type CustomerRole = 'customer';
export type Status = 'active' | 'inactive' | 'pending' | 'suspended';

// File upload types
export interface FileUpload {
	filename: string;
	mimetype: string;
	size: number;
	buffer: Buffer;
}

export interface UploadedFile {
	url: string;
	publicId: string;
	filename: string;
	size: number;
	mimetype: string;
}

// Validation types
export interface ValidationError {
	field: string;
	message: string;
	value?: unknown;
}

// Context types for middleware
export interface RequestContext {
	userId?: string;
	siteId?: string;
	customerId?: string;
	ip: string;
	userAgent: string;
	timestamp: Date;
}

// Generic utility types
export type DeepPartial<T> = {
	[P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

export type OmitFields<T, K extends keyof T> = Omit<T, K>;

export type PickFields<T, K extends keyof T> = Pick<T, K>;

// Environment types
export type Environment = 'development' | 'production' | 'test';

// Database connection status
export type ConnectionStatus = 'connected' | 'disconnected' | 'connecting' | 'error';

// Generic CRUD operations
export interface CrudOperations<T> {
	create(data: Partial<T>): Promise<T>;
	findById(id: string): Promise<T | null>;
	findMany(query?: FilterQuery): Promise<PaginatedResponse<T>>;
	update(id: string, data: Partial<T>): Promise<T | null>;
	delete(id: string): Promise<boolean>;
}

// Event types
export interface BaseEvent {
	type: string;
	timestamp: Date;
	userId?: string;
	siteId?: string;
	data: Record<string, unknown>;
}

// Cache types
export interface CacheOptions {
	ttl?: number; // Time to live in seconds
	key: string;
	tags?: string[];
}

// Rate limiting types
export interface RateLimitOptions {
	windowMs: number;
	maxRequests: number;
	keyGenerator?: (request: Request) => string;
}

// Monitoring types
export interface HealthCheck {
	service: string;
	status: 'healthy' | 'unhealthy' | 'degraded';
	timestamp: Date;
	responseTime?: number;
	error?: string;
}

// Search types
export interface SearchQuery {
	query: string;
	filters?: Record<string, unknown>;
	sort?: Record<string, 'asc' | 'desc'>;
	limit?: number;
	offset?: number;
}

export interface SearchResult<T> {
	items: T[];
	total: number;
	took: number; // Search time in milliseconds
	facets?: Record<string, unknown>;
}

// Configuration types
export interface DatabaseConfig {
	uri: string;
	options?: Record<string, unknown>;
}

export interface RedisConfig {
	host: string;
	port: number;
	password?: string;
	db?: number;
}

export interface EmailConfig {
	provider: 'smtp' | 'sendgrid' | 'mailgun';
	apiKey?: string;
	from: string;
	templates?: Record<string, string>;
}

// Type guards
export function isString(value: unknown): value is string {
	return typeof value === 'string';
}

export function isNumber(value: unknown): value is number {
	return typeof value === 'number' && !isNaN(value);
}

export function isObject(value: unknown): value is Record<string, unknown> {
	return typeof value === 'object' && value !== null && !Array.isArray(value);
}

export function isArray(value: unknown): value is unknown[] {
	return Array.isArray(value);
}

export function isDefined<T>(value: T | undefined | null): value is T {
	return value !== undefined && value !== null;
}

// Utility functions
export function omit<T extends Record<string, unknown>, K extends keyof T>(
	obj: T,
	keys: K[]
): Omit<T, K> {
	const result = { ...obj };
	keys.forEach(key => delete result[key]);
	return result;
}

export function pick<T extends Record<string, unknown>, K extends keyof T>(
	obj: T,
	keys: K[]
): Pick<T, K> {
	const result = {} as Pick<T, K>;
	keys.forEach(key => {
		if (key in obj) {
			result[key] = obj[key];
		}
	});
	return result;
}
