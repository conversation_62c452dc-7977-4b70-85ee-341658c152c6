// ✅ Error Types สำหรับ Backend Elysia

/**
 * Base Error Interface
 */
export interface BaseError {
	message: string;
	status?: number;
	statusCode?: number;
}

/**
 * Validation Error Detail
 */
export interface ValidationErrorDetail {
	field?: string;
	message: string;
	summary?: string;
	code?: string;
	value?: unknown;
}

/**
 * Validation Error
 */
export interface ValidationError extends BaseError {
	type: 'VALIDATION';
	message: string;
	all?: ValidationErrorDetail[];
	errors?: ValidationErrorDetail[];
}

/**
 * HTTP Error
 */
export interface HttpError extends BaseError {
	status: number;
	statusCode?: number;
	message: string;
}

/**
 * Generic Error with message
 */
export interface GenericError extends BaseError {
	message: string;
}

/**
 * Unknown Error (any type)
 */
export type UnknownError = unknown;

/**
 * Union type for all possible errors
 */
export type AppError = ValidationError | HttpError | GenericError | UnknownError;

/**
 * Error Response Interface
 */
export interface ErrorResponse {
	success: false;
	message: string;
	statusMessage: string;
	timestamp: string;
	detail?: ValidationErrorDetail[] | ValidationErrorDetail | Record<string, unknown>;
	stack?: string;
}

/**
 * Error Context Interface
 */
export interface ErrorContext {
	ip: string;
	userAgent: string;
	url: string;
	method: string;
}

/**
 * Error Handler Result
 */
export interface ErrorHandlerResult {
	status: number;
	message: string;
	detail?: ValidationErrorDetail[] | ValidationErrorDetail | Record<string, unknown>;
}

/**
 * Type guards for error types
 */
export function isValidationError(error: unknown): error is ValidationError {
	return (
		typeof error === 'object' &&
		error !== null &&
		'type' in error &&
		error.type === 'VALIDATION'
	);
}

export function isHttpError(error: unknown): error is HttpError {
	return (
		typeof error === 'object' &&
		error !== null &&
		('status' in error || 'statusCode' in error) &&
		'message' in error
	);
}

export function isGenericError(error: unknown): error is GenericError {
	return (
		typeof error === 'object' &&
		error !== null &&
		'message' in error &&
		typeof (error as GenericError).message === 'string'
	);
}

export function hasMessage(error: unknown): error is { message: string } {
	return (
		typeof error === 'object' &&
		error !== null &&
		'message' in error &&
		typeof (error as { message: string }).message === 'string'
	);
}
