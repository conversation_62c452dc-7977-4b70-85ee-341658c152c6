import { config } from './environment';
import { logger } from '@/core/utils/logger';

/**
 * Server configuration interface
 */
export interface ServerConfig {
	port: number;
	hostname: string;
	idleTimeout: number;
	maxRequestBodySize: number;
}

/**
 * Get server configuration based on environment
 */
export function getServerConfig(): ServerConfig {
	return {
		port: config.port || 5000,
		hostname: config.hostname || 'localhost',
		idleTimeout: 160, // 160 seconds idle timeout
		maxRequestBodySize: 10 * 1024 * 1024, // 10MB max request body size
	};
}

/**
 * Log server startup information
 */
export function logServerStartup() {
	logger.info('Starting Elysia server', {
		environment: config.NODE_ENV,
		port: config.port,
		hostname: config.hostname,
		isDev: config.isDev,
	});
}

/**
 * Log server started information
 */
export function logServerStarted(server: any) {
	logger.info('🦊 Elysia server started successfully', {
		hostname: server?.hostname,
		port: server?.port,
		environment: config.NODE_ENV,
		url: `http://${server?.hostname}:${server?.port}`,
		features: {
			cors: true,
			rateLimit: true,
			helmet: true,
			staticFiles: true,
			serverTiming: true,
			authentication: true,
			logging: true,
			errorHandling: true,
		},
	});
}
