import { env } from 'bun';

// Validate critical environment variables
function validateEnvironment() {
	const requiredVars = ['MONGODB_URI', 'JWT_SECRET', 'EMAIL_USER', 'EMAIL_PASSWORD'];

	const missing = requiredVars.filter(varName => !env[varName]);

	if (missing.length > 0) {
		console.error(`❌ Missing required environment variables: ${missing.join(', ')}`);
		throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
	}

	// Validate JWT_SECRET length
	if (env.JWT_SECRET && env.JWT_SECRET.length < 32) {
		console.warn('⚠️  JWT_SECRET should be at least 32 characters long for security');
	}

	// ✅ Security: บังคับให้มี JWT_SECRET ใน production
	if (env.NODE_ENV === 'production') {
		if (!env.JWT_SECRET || env.JWT_SECRET === 'your-secret-key-change-this-in-production') {
			throw new Error('JWT_SECRET must be set to a secure value in production');
		}
		if (
			!env.REFRESH_TOKEN_SECRET ||
			env.REFRESH_TOKEN_SECRET === 'your-refresh-token-secret-change-this-in-production'
		) {
			throw new Error('REFRESH_TOKEN_SECRET must be set to a secure value in production');
		}
	}

	// Validate email configuration
	if (env.NODE_ENV === 'production' && (!env.EMAIL_USER || !env.EMAIL_PASSWORD)) {
		console.warn('⚠️  Email configuration incomplete in production environment');
	}

	console.log('✅ Environment validation completed successfully');
}

// Run validation
validateEnvironment();

export const config = {
	// Environment
	NODE_ENV: env.NODE_ENV || 'development',
	isDev: env.NODE_ENV !== 'production',
	isProd: env.NODE_ENV === 'production',
	isTest: env.NODE_ENV === 'test',

	// Server
	port: Number(env.PORT) || 3000,
	hostname: env.HOSTNAME || 'localhost',

	// Database
	mongodbUri: env.MONGODB_URI || 'mongodb://localhost:27017/auth',

	// JWT
	jwtSecret: env.JWT_SECRET || 'your-secret-key-change-this-in-production',
	refreshTokenSecret: env.REFRESH_TOKEN_SECRET || 'your-refresh-token-secret-change-this-in-production',
	jwtExpiresIn: env.JWT_EXPIRES_IN || '1h',
	refreshTokenExpiresIn: env.REFRESH_TOKEN_EXPIRES_IN || '7d',

	// Frontend
	frontendUrl: env.FRONTEND_URL || 'http://localhost:8000',

	// Email configuration
	email: {
		host: env.EMAIL_HOST || 'smtp.gmail.com',
		port: Number(env.EMAIL_PORT) || 587,
		secure: env.EMAIL_SECURE === 'true',
		user: env.EMAIL_USER || '',
		password: env.EMAIL_PASSWORD || '',
		from: env.EMAIL_FROM || '<EMAIL>',
	},

	// Cloudinary configuration
	cloudinary: {
		cloudName: env.CLOUDINARY_CLOUD_NAME || '',
		apiKey: env.CLOUDINARY_API_KEY || '',
		apiSecret: env.CLOUDINARY_API_SECRET || '',
	},

	// Vercel configuration
	vercel: {
		apiToken: env.VERCEL_API_TOKEN || '',
		teamId: env.VERCEL_TEAM_ID || '',
		projectId: env.VERCEL_PROJECT_ID || '',
	},

	// Rate limiting
	rateLimit: {
		windowMs: Number(env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
		max: Number(env.RATE_LIMIT_MAX) || 100, // limit each IP to 100 requests per windowMs
	},

	// Legacy properties for backward compatibility
	emailHost: env.EMAIL_HOST || 'smtp.gmail.com',
	emailPort: Number(env.EMAIL_PORT) || 587,
	emailSecure: env.EMAIL_SECURE === 'true',
	emailUser: env.EMAIL_USER || '',
	emailPassword: env.EMAIL_PASSWORD || '',
	emailFrom: env.EMAIL_FROM || '<EMAIL>',
	cloudinaryCloudName: env.CLOUDINARY_CLOUD_NAME || '',
	cloudinaryApiKey: env.CLOUDINARY_API_KEY || '',
	cloudinaryApiSecret: env.CLOUDINARY_API_SECRET || '',
} as const;
