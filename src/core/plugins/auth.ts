import { Elysia } from 'elysia';
import { HttpError } from '../utils/error';
import { User } from '@/modules/user/user.model';
import { Customer } from '@/modules/customer/customer.model';
import { extractBearerToken, verifyUserToken, verifyCustomerToken } from '../services/jwt.service';
import { standardResponseSchema } from '../schemas/response.schema';

// Authentication plugin สำหรับ User
export const userAuthPlugin = new Elysia({ name: 'user-auth' })
	.derive(async ({ headers }) => {
		const bearer = extractBearerToken(headers['authorization']);

		if (!bearer) {
			return { user: null };
		}

		const userPayload = await verifyUserToken(bearer);
		if (!userPayload) {
			return { user: null };
		}

		const userData = await User.findById(userPayload.userId);
		return { user: userData };
	})
	.guard({ response: standardResponseSchema })
	.onBeforeHandle(({ user }) => {
		if (!user) {
			throw new HttpError(401, 'ไม่มีสิทธิ์เข้าถึง');
		}
	})
	.as('scoped');

// Authentication plugin สำหรับ Customer
export const customerAuthPlugin = new Elysia({ name: 'customer-auth' })
	.derive(async ({ headers }) => {
		const bearer = extractBearerToken(headers['authorization']);

		if (!bearer) {
			return { customer: null };
		}

		const customerPayload = await verifyCustomerToken(bearer);
		if (!customerPayload) {
			return { customer: null };
		}

		const customerData = await Customer.findById(customerPayload.customerId);
		return { customer: customerData };
	})
	.guard({ response: standardResponseSchema })
	.onBeforeHandle(({ customer }) => {
		if (!customer) {
			throw new HttpError(401, 'ไม่มีสิทธิ์เข้าถึง');
		}
	})
	.as('scoped');

// Authentication plugin ที่ใช้ guard pattern
export const authPlugin = new Elysia({ name: 'auth' })
	.derive(({ headers }) => {
		const bearer = extractBearerToken(headers['authorization']);
		return { bearer };
	})
	.onBeforeHandle(({ bearer, set }) => {
		if (!bearer) {
			set.status = 401;
			return 'Unauthorized';
		}
	})
	.get('/', ({ bearer }) => `Token: ${bearer}`)
	.as('scoped');

// Authentication plugin สำหรับ JWT ที่ใช้ guard pattern (legacy - ใช้สำหรับ user)
export const jwtAuthPlugin = new Elysia({ name: 'jwt-auth' })
	.derive(async ({ headers }) => {
		const bearer = extractBearerToken(headers['authorization']);

		if (!bearer) {
			return { user: null };
		}

		const userPayload = await verifyUserToken(bearer);
		return { user: userPayload };
	})
	.guard({ response: standardResponseSchema })
	.onBeforeHandle(({ user }) => {
		if (!user) {
			throw new HttpError(401, 'ไม่มีสิทธิ์เข้าถึง');
		}
	})
	.as('scoped');

// Optional authentication plugin (ไม่บังคับต้องมี token)
export const optionalAuthPlugin = new Elysia({ name: 'optional-auth' }).derive(async ({ headers }) => {
	const bearer = extractBearerToken(headers.authorization);

	if (!bearer) {
		return { user: null };
	}

	const userPayload = await verifyUserToken(bearer);
	return { user: userPayload };
});

// Role-based authentication plugin ที่ใช้ guard pattern
export const roleAuthPlugin = new Elysia({ name: 'role-auth' })
	.derive(async ({ headers }) => {
		const bearer = extractBearerToken(headers.authorization);

		if (!bearer) {
			return { user: null };
		}

		const userPayload = await verifyUserToken(bearer);

		if (!userPayload) {
			return { user: null };
		}

		return {
			user: userPayload,
			hasRole: (role: string) => userPayload.role === role,
			hasAnyRole: (roles: string[]) => roles.includes(userPayload.role || ''),
			isAdmin: userPayload.role === 'admin',
			isOwner: userPayload.role === 'owner',
		};
	})
	.guard({ response: standardResponseSchema })
	.onBeforeHandle(({ user }) => {
		if (!user) {
			throw new HttpError(401, 'ไม่มีสิทธิ์เข้าถึง');
		}
	})
	.as('scoped');

// Export all plugins
export const authPlugins = {
	auth: authPlugin,
	jwt: jwtAuthPlugin,
	user: userAuthPlugin,
	customer: customerAuthPlugin,
	optional: optionalAuthPlugin,
	role: roleAuthPlugin,
};

// Export types for backward compatibility
export type { UserJWTPayload, CustomerJWTPayload, JWTPayload };
