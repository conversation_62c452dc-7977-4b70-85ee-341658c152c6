import { logger } from '@/core/utils/logger';

/**
 * ✅ Login Attempt Tracking Service
 * ป้องกัน brute force attacks และ track suspicious login activities
 */

interface LoginAttempt {
	email: string;
	ip: string;
	userAgent: string;
	success: boolean;
	timestamp: Date;
	reason?: string; // เหตุผลที่ login ไม่สำเร็จ
}

interface AccountLockout {
	email: string;
	lockedUntil: Date;
	attemptCount: number;
	lockReason: 'brute_force' | 'suspicious_activity' | 'manual';
}

/**
 * Configuration สำหรับ login attempt tracking
 */
const LOGIN_ATTEMPT_CONFIG = {
	MAX_ATTEMPTS: 5, // จำนวนครั้งสูงสุดที่อนุญาต
	LOCKOUT_DURATION: 15 * 60 * 1000, // 15 นาที
	TRACKING_WINDOW: 60 * 60 * 1000, // 1 ชั่วโมง
	CLEANUP_INTERVAL: 60 * 60 * 1000, // ทำความสะอาดทุก 1 ชั่วโมง

	// Progressive lockout (เพิ่มเวลา lock ตามจำนวนครั้ง)
	PROGRESSIVE_LOCKOUT: {
		5: 15 * 60 * 1000, // 15 นาที
		10: 30 * 60 * 1000, // 30 นาที
		15: 60 * 60 * 1000, // 1 ชั่วโมง
		20: 24 * 60 * 60 * 1000, // 24 ชั่วโมง
	},
};

/**
 * In-memory storage (ในการใช้งานจริงควรใช้ Redis หรือ Database)
 */
class LoginAttemptService {
	private attempts = new Map<string, LoginAttempt[]>();
	private lockouts = new Map<string, AccountLockout>();
	private ipAttempts = new Map<string, LoginAttempt[]>();
	private cleanupInterval: NodeJS.Timeout;

	constructor() {
		// ทำความสะอาดข้อมูลเก่าทุก 1 ชั่วโมง
		this.cleanupInterval = setInterval(() => {
			this.cleanup();
		}, LOGIN_ATTEMPT_CONFIG.CLEANUP_INTERVAL);
	}

	/**
	 * บันทึก login attempt
	 */
	async recordAttempt(email: string, ip: string, userAgent: string, success: boolean, reason?: string): Promise<void> {
		const attempt: LoginAttempt = {
			email: email.toLowerCase(),
			ip,
			userAgent,
			success,
			timestamp: new Date(),
			reason,
		};

		// บันทึกตาม email
		const emailKey = email.toLowerCase();
		if (!this.attempts.has(emailKey)) {
			this.attempts.set(emailKey, []);
		}
		this.attempts.get(emailKey)?.push(attempt);

		// บันทึกตาม IP
		if (!this.ipAttempts.has(ip)) {
			this.ipAttempts.set(ip, []);
		}
		this.ipAttempts.get(ip)?.push(attempt);

		logger.info('Login attempt recorded', {
			email: email.substring(0, 3) + '***',
			ip,
			success,
			reason,
			userAgent: userAgent.substring(0, 50) + '...',
		});

		// ตรวจสอบและ lock account ถ้าจำเป็น
		if (!success) {
			await this.checkAndLockAccount(email, ip);
		} else {
			// ถ้า login สำเร็จ ให้ clear failed attempts
			await this.clearFailedAttempts(email);
		}
	}

	/**
	 * ตรวจสอบว่า account ถูก lock หรือไม่
	 */
	async isAccountLocked(email: string): Promise<{
		isLocked: boolean;
		lockedUntil?: Date;
		attemptCount?: number;
		reason?: string;
	}> {
		const emailKey = email.toLowerCase();
		const lockout = this.lockouts.get(emailKey);

		if (!lockout) {
			return { isLocked: false };
		}

		// ตรวจสอบว่าหมดเวลา lock แล้วหรือไม่
		if (lockout.lockedUntil < new Date()) {
			this.lockouts.delete(emailKey);
			logger.info('Account lockout expired', {
				email: email.substring(0, 3) + '***',
			});
			return { isLocked: false };
		}

		return {
			isLocked: true,
			lockedUntil: lockout.lockedUntil,
			attemptCount: lockout.attemptCount,
			reason: lockout.lockReason,
		};
	}

	/**
	 * ตรวจสอบ IP ที่มี suspicious activity
	 */
	async checkSuspiciousIP(ip: string): Promise<{
		isSuspicious: boolean;
		attemptCount: number;
		reason?: string;
	}> {
		const attempts = this.ipAttempts.get(ip) || [];
		const recentAttempts = this.getRecentAttempts(attempts);
		const failedAttempts = recentAttempts.filter(a => !a.success);

		// ตรวจสอบ pattern ที่น่าสงสัย
		const uniqueEmails = new Set(recentAttempts.map(a => a.email)).size;
		const failedCount = failedAttempts.length;

		// Suspicious patterns:
		// 1. มากกว่า 10 failed attempts ใน 1 ชั่วโมง
		// 2. พยายาม login หลาย email จาก IP เดียวกัน (> 5 emails)
		const isSuspicious = failedCount > 10 || uniqueEmails > 5;

		if (isSuspicious) {
			logger.warn('Suspicious IP activity detected', {
				ip,
				failedCount,
				uniqueEmails,
				recentAttempts: recentAttempts.length,
			});
		}

		return {
			isSuspicious,
			attemptCount: failedCount,
			reason: failedCount > 10 ? 'too_many_failures' : uniqueEmails > 5 ? 'multiple_accounts' : undefined,
		};
	}

	/**
	 * Manual lock account (สำหรับ admin)
	 */
	async lockAccount(
		email: string,
		duration: number = LOGIN_ATTEMPT_CONFIG.LOCKOUT_DURATION,
		reason: AccountLockout['lockReason'] = 'manual'
	): Promise<void> {
		const emailKey = email.toLowerCase();
		const lockedUntil = new Date(Date.now() + duration);

		this.lockouts.set(emailKey, {
			email: emailKey,
			lockedUntil,
			attemptCount: 0,
			lockReason: reason,
		});
 
	}

	/**
	 * Unlock account (สำหรับ admin)
	 */
	async unlockAccount(email: string): Promise<void> {
		const emailKey = email.toLowerCase();
		this.lockouts.delete(emailKey);
		await this.clearFailedAttempts(email);

		logger.info('Account manually unlocked', {
			email: email.substring(0, 3) + '***',
		});
	}

	/**
	 * ตรวจสอบและ lock account ถ้าจำเป็น
	 */
	private async checkAndLockAccount(email: string, ip: string): Promise<void> {
		const emailKey = email.toLowerCase();
		const attempts = this.attempts.get(emailKey) || [];
		const recentAttempts = this.getRecentAttempts(attempts);
		const failedAttempts = recentAttempts.filter(a => !a.success);

		if (failedAttempts.length >= LOGIN_ATTEMPT_CONFIG.MAX_ATTEMPTS) {
			// คำนวณ lockout duration แบบ progressive
			const totalFailedAttempts = attempts.filter(a => !a.success).length;
			const duration = this.calculateLockoutDuration(totalFailedAttempts);

			const lockedUntil = new Date(Date.now() + duration);

			this.lockouts.set(emailKey, {
				email: emailKey,
				lockedUntil,
				attemptCount: totalFailedAttempts,
				lockReason: 'brute_force',
			});

			logger.warn('Account locked due to failed attempts', {
				email: email.substring(0, 3) + '***',
				ip,
				failedAttempts: failedAttempts.length,
				totalFailedAttempts,
				lockedUntil,
				duration: (duration / 1000 / 60).toFixed(2) + ' minutes',
			});
		}
	}

	/**
	 * คำนวณ lockout duration แบบ progressive
	 */
	private calculateLockoutDuration(attemptCount: number): number {
		const config = LOGIN_ATTEMPT_CONFIG.PROGRESSIVE_LOCKOUT;

		for (const [threshold, duration] of Object.entries(config).reverse()) {
			if (attemptCount >= parseInt(threshold)) {
				return duration;
			}
		}

		return LOGIN_ATTEMPT_CONFIG.LOCKOUT_DURATION;
	}

	/**
	 * ดึง attempts ที่เกิดขึ้นใน tracking window
	 */
	private getRecentAttempts(attempts: LoginAttempt[]): LoginAttempt[] {
		const cutoff = new Date(Date.now() - LOGIN_ATTEMPT_CONFIG.TRACKING_WINDOW);
		return attempts.filter(a => a.timestamp > cutoff);
	}

	/**
	 * ลบ failed attempts เมื่อ login สำเร็จ
	 */
	private async clearFailedAttempts(email: string): Promise<void> {
		const emailKey = email.toLowerCase();
		const attempts = this.attempts.get(emailKey) || [];

		// เก็บเฉพาะ successful attempts
		const successfulAttempts = attempts.filter(a => a.success);
		this.attempts.set(emailKey, successfulAttempts);

		// ลบ lockout ถ้ามี
		this.lockouts.delete(emailKey);

		logger.info('Failed attempts cleared after successful login', {
			email: email.substring(0, 3) + '***',
		});
	}

	/**
	 * ทำความสะอาดข้อมูลเก่า
	 */
	private cleanup(): void {
		const cutoff = new Date(Date.now() - LOGIN_ATTEMPT_CONFIG.TRACKING_WINDOW * 2);
		let cleanedAttempts = 0;
		let cleanedLockouts = 0;

		// ทำความสะอาด attempts
		for (const [email, attempts] of this.attempts.entries()) {
			const recentAttempts = attempts.filter(a => a.timestamp > cutoff);
			if (recentAttempts.length === 0) {
				this.attempts.delete(email);
				cleanedAttempts++;
			} else {
				this.attempts.set(email, recentAttempts);
			}
		}

		// ทำความสะอาด IP attempts
		for (const [ip, attempts] of this.ipAttempts.entries()) {
			const recentAttempts = attempts.filter(a => a.timestamp > cutoff);
			if (recentAttempts.length === 0) {
				this.ipAttempts.delete(ip);
			} else {
				this.ipAttempts.set(ip, recentAttempts);
			}
		}

		// ทำความสะอาด expired lockouts
		const now = new Date();
		for (const [email, lockout] of this.lockouts.entries()) {
			if (lockout.lockedUntil < now) {
				this.lockouts.delete(email);
				cleanedLockouts++;
			}
		}

		if (cleanedAttempts > 0 || cleanedLockouts > 0) {
			logger.debug('Login attempt cleanup completed', {
				cleanedAttempts,
				cleanedLockouts,
				remainingAttempts: this.attempts.size,
				remainingLockouts: this.lockouts.size,
			});
		}
	}

	/**
	 * ดูสถิติ
	 */
	getStats(): {
		totalAttempts: number;
		activeLockouts: number;
		suspiciousIPs: number;
	} {
		const now = new Date();
		const activeLockouts = Array.from(this.lockouts.values()).filter(l => l.lockedUntil > now).length;

		return {
			totalAttempts: this.attempts.size,
			activeLockouts,
			suspiciousIPs: this.ipAttempts.size,
		};
	}

	/**
	 * ปิด service และ cleanup
	 */
	destroy(): void {
		if (this.cleanupInterval) {
			clearInterval(this.cleanupInterval);
		}
		this.attempts.clear();
		this.lockouts.clear();
		this.ipAttempts.clear();
	}
}

// Singleton instance
export const loginAttemptService = new LoginAttemptService();
