export interface TokenActivity {
	userId: string;
	action:
		| 'token_created'
		| 'token_refreshed'
		| 'token_rotated'
		| 'token_revoked'
		| 'token_expired'
		| 'token_validation_failed';
	tokenId?: string;
	deviceFingerprint?: string;
	ipAddress: string;
	userAgent: string;
	success: boolean;
	error?: string;
	metadata?: Record<string, any>;
	timestamp: Date;
}

export interface SecurityEvent {
	type:
		| 'suspicious_activity'
		| 'rate_limit_exceeded'
		| 'device_mismatch'
		| 'token_theft_attempt'
		| 'multiple_failed_attempts';
	severity: 'low' | 'medium' | 'high' | 'critical';
	userId?: string;
	ipAddress: string;
	userAgent: string;
	description: string;
	metadata?: Record<string, any>;
	timestamp: Date;
}

export interface MonitoringStats {
	totalTokenActivities: number;
	totalSecurityEvents: number;
	recentTokenActivities: TokenActivity[];
	recentSecurityEvents: SecurityEvent[];
	rateLimitStats: {
		totalRecords: number;
		blockedRecords: number;
		activeRecords: number;
	};
}

export class MonitoringService {
	private static instance: MonitoringService;
	private tokenActivities: TokenActivity[] = [];
	private securityEvents: SecurityEvent[] = [];
	private maxStoredEvents = 1000; // Keep last 1000 events in memory

	private constructor() {
		// Cleanup old events every 10 minutes
		setInterval(() => this.cleanupOldEvents(), 10 * 60 * 1000);
	}

	static getInstance(): MonitoringService {
		if (!MonitoringService.instance) {
			MonitoringService.instance = new MonitoringService();
		}
		return MonitoringService.instance;
	}

	/**
	 * Log token activity
	 */
	logTokenActivity(activity: Omit<TokenActivity, 'timestamp'>): void {
		const fullActivity: TokenActivity = {
			...activity,
			timestamp: new Date(),
		};

		this.tokenActivities.push(fullActivity);

		// Keep only recent activities
		if (this.tokenActivities.length > this.maxStoredEvents) {
			this.tokenActivities = this.tokenActivities.slice(-this.maxStoredEvents);
		}

		// Log to console for debugging
		console.log(`[TokenActivity] ${activity.action}:`, {
			userId: activity.userId,
			success: activity.success,
			ipAddress: activity.ipAddress,
			timestamp: fullActivity.timestamp.toISOString(),
		});

		// Alert on suspicious activities
		this.checkForSuspiciousActivity(fullActivity);
	}

	/**
	 * Log security event
	 */
	logSecurityEvent(event: Omit<SecurityEvent, 'timestamp'>): void {
		const fullEvent: SecurityEvent = {
			...event,
			timestamp: new Date(),
		};

		this.securityEvents.push(fullEvent);

		// Keep only recent events
		if (this.securityEvents.length > this.maxStoredEvents) {
			this.securityEvents = this.securityEvents.slice(-this.maxStoredEvents);
		}

		// Log to console with appropriate level
		const logLevel = this.getLogLevel(event.severity);
		console[logLevel](`[SecurityEvent] ${event.type}:`, {
			severity: event.severity,
			userId: event.userId,
			ipAddress: event.ipAddress,
			description: event.description,
			timestamp: fullEvent.timestamp.toISOString(),
		});

		// Send alert for high/critical events
		if (event.severity === 'high' || event.severity === 'critical') {
			this.sendSecurityAlert(fullEvent);
		}
	}

	/**
	 * Check for suspicious token activities
	 */
	private checkForSuspiciousActivity(activity: TokenActivity): void {
		const recentActivities = this.tokenActivities.filter(
			a => a.userId === activity.userId && a.timestamp > new Date(Date.now() - 5 * 60 * 1000) // Last 5 minutes
		);

		// Multiple failed attempts
		const failedAttempts = recentActivities.filter(a => !a.success);
		if (failedAttempts.length >= 3) {
			this.logSecurityEvent({
				type: 'multiple_failed_attempts',
				severity: 'medium',
				userId: activity.userId,
				ipAddress: activity.ipAddress,
				userAgent: activity.userAgent,
				description: `Multiple failed token activities detected for user ${activity.userId}`,
				metadata: {
					failedAttempts: failedAttempts.length,
					timeWindow: '5 minutes',
				},
			});
		}

		// Rapid token rotations
		const rotations = recentActivities.filter(a => a.action === 'token_rotated');
		if (rotations.length >= 5) {
			this.logSecurityEvent({
				type: 'suspicious_activity',
				severity: 'high',
				userId: activity.userId,
				ipAddress: activity.ipAddress,
				userAgent: activity.userAgent,
				description: `Excessive token rotations detected for user ${activity.userId}`,
				metadata: {
					rotationCount: rotations.length,
					timeWindow: '5 minutes',
				},
			});
		}
	}

	/**
	 * Send security alert
	 */
	private sendSecurityAlert(event: SecurityEvent): void {
		// In production, this would send to external monitoring service
		// For now, just log with special formatting
		console.error('🚨 SECURITY ALERT 🚨');
		console.error(`Type: ${event.type}`);
		console.error(`Severity: ${event.severity}`);
		console.error(`User: ${event.userId || 'Unknown'}`);
		console.error(`IP: ${event.ipAddress}`);
		console.error(`Description: ${event.description}`);
		console.error(`Time: ${event.timestamp.toISOString()}`);
		console.error('🚨 END ALERT 🚨');

		// TODO: Integrate with external monitoring services like:
		// - Sentry for error tracking
		// - DataDog for metrics
		// - PagerDuty for alerts
		// - Slack/Discord for notifications
	}

	/**
	 * Get log level based on severity
	 */
	private getLogLevel(severity: SecurityEvent['severity']): 'log' | 'warn' | 'error' {
		switch (severity) {
			case 'low':
				return 'log';
			case 'medium':
				return 'warn';
			case 'high':
			case 'critical':
				return 'error';
			default:
				return 'log';
		}
	}

	/**
	 * Get monitoring statistics
	 */
	getMonitoringStats(): MonitoringStats {
		const now = new Date();
		const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);

		const recentTokenActivities = this.tokenActivities.filter(a => a.timestamp > oneHourAgo);

		const recentSecurityEvents = this.securityEvents.filter(e => e.timestamp > oneHourAgo);

		return {
			totalTokenActivities: this.tokenActivities.length,
			totalSecurityEvents: this.securityEvents.length,
			recentTokenActivities,
			recentSecurityEvents,
			rateLimitStats: {
				totalRecords: 0, // Will be populated by rate limit service
				blockedRecords: 0,
				activeRecords: 0,
			},
		};
	}

	/**
	 * Get user activity summary
	 */
	getUserActivitySummary(
		userId: string,
		hours: number = 24
	): {
		totalActivities: number;
		successfulActivities: number;
		failedActivities: number;
		securityEvents: number;
		activityBreakdown: Record<string, number>;
	} {
		const cutoffTime = new Date(Date.now() - hours * 60 * 60 * 1000);

		const userActivities = this.tokenActivities.filter(a => a.userId === userId && a.timestamp > cutoffTime);

		const userSecurityEvents = this.securityEvents.filter(e => e.userId === userId && e.timestamp > cutoffTime);

		const activityBreakdown: Record<string, number> = {};
		userActivities.forEach(activity => {
			activityBreakdown[activity.action] = (activityBreakdown[activity.action] || 0) + 1;
		});

		return {
			totalActivities: userActivities.length,
			successfulActivities: userActivities.filter(a => a.success).length,
			failedActivities: userActivities.filter(a => !a.success).length,
			securityEvents: userSecurityEvents.length,
			activityBreakdown,
		};
	}

	/**
	 * Cleanup old events
	 */
	private cleanupOldEvents(): void {
		const cutoffTime = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours ago

		this.tokenActivities = this.tokenActivities.filter(a => a.timestamp > cutoffTime);

		this.securityEvents = this.securityEvents.filter(e => e.timestamp > cutoffTime);

		console.log(
			`[MonitoringService] Cleaned up old events. Remaining: ${this.tokenActivities.length} activities, ${this.securityEvents.length} security events`
		);
	}

	/**
	 * Export monitoring data for analysis
	 */
	exportMonitoringData(): {
		tokenActivities: TokenActivity[];
		securityEvents: SecurityEvent[];
		exportTime: Date;
	} {
		return {
			tokenActivities: [...this.tokenActivities],
			securityEvents: [...this.securityEvents],
			exportTime: new Date(),
		};
	}
}

export const monitoringService = MonitoringService.getInstance();
