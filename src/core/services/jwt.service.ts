import * as jose from 'jose';
import { config } from '../config/environment';
import { logger } from '../utils/logger';


// Base JWT Payload interface
export interface BaseJWTPayload {
	iat?: number;
	exp?: number;
}

// User JWT Payload interface
export interface UserJWTPayload extends BaseJWTPayload {
	userId: string;
	email: string;
	role?: string;
}

// Customer JWT Payload interface
export interface CustomerJWTPayload extends BaseJWTPayload {
	customerId: string;
	email: string;
	siteId?: string;
}

// Legacy interface สำหรับ backward compatibility
export interface JWTPayload extends UserJWTPayload {}

// ✅ แก้ไขจาก static class เป็น functions
const secret = new TextEncoder().encode(config.jwtSecret);
const refreshTokenSecret = new TextEncoder().encode(config.refreshTokenSecret);

/**
 * Verify JWT token และ return payload (รวมการตรวจสอบ blacklist)
 */
export async function verifyToken<T = unknown>(token: string): Promise<T | null> {
	try {
		// ✅ ตรวจสอบ blacklist ก่อน
		// const isBlacklisted = await checkTokenBlacklist(token);
		// if (isBlacklisted) {
		//     logger.debug('JWT verification failed: token is blacklisted');
		//     return null;
		// }

		const { payload } = await jose.jwtVerify(token, secret);
		return payload as unknown as T;
	} catch (error) {
		logger.debug('JWT verification failed', {
			error: error instanceof Error ? error.message : String(error),
		});
		return null;
	}
}

/**
 * Extract Bearer token from Authorization header
 */
export function extractBearerToken(authHeader?: string): string | null {
	if (!authHeader?.startsWith('Bearer ')) {
		return null;
	}
	return authHeader.slice(7);
}

/**
 * Verify User JWT token
 */
export async function verifyUserToken(token: string): Promise<UserJWTPayload | null> {
	const payload = await verifyToken<UserJWTPayload>(token);

	if (!payload || !payload.userId || !payload.email) {
		return null;
	}

	return payload;
}

/**
 * Verify Customer JWT token
 */
export async function verifyCustomerToken(token: string): Promise<CustomerJWTPayload | null> {
	const payload = await verifyToken<CustomerJWTPayload>(token);

	if (!payload || !payload.customerId || !payload.email) {
		return null;
	}

	return payload;
}

/**
 * Generate JWT token
 */
export async function generateToken(payload: Record<string, unknown>, expiresIn: string = '24h'): Promise<string> {
	return await new jose.SignJWT(payload)
		.setProtectedHeader({ alg: 'HS256' })
		.setIssuedAt()
		.setExpirationTime(expiresIn)
		.sign(secret);
}

/**
 * Generate refresh token
 */
export async function generateRefreshToken(
	payload: Record<string, unknown>,
	expiresIn: string = '7d'
): Promise<string> {
	return await new jose.SignJWT(payload)
		.setProtectedHeader({ alg: 'HS256' })
		.setIssuedAt()
		.setExpirationTime(expiresIn)
		.sign(refreshTokenSecret);
}

/**
 * Verify refresh token
 */
export async function verifyRefreshToken<T = unknown>(token: string): Promise<T | null> {
	try {
		// ✅ ตรวจสอบ blacklist ก่อน
		// const isBlacklisted = await checkTokenBlacklist(token);
		// if (isBlacklisted) {
		//     logger.debug('Refresh token verification failed: token is blacklisted');
		//     return null;
		// }

		const { payload } = await jose.jwtVerify(token, refreshTokenSecret);
		return payload as unknown as T;
	} catch (error) {
		logger.debug('Refresh token verification failed', {
			error: error instanceof Error ? error.message : String(error),
		});
		return null;
	}
}
