# Authentication & Authorization System

ระบบ Authentication และ Authorization ที่ได้รับการ refactor ใหม่เพื่อลดการซ้ำซ้อนและเพิ่มความยืดหยุ่น

## โครงสร้างใหม่

### Services

- `JWTService` - จัดการ JWT token verification และ generation
- `AccessControlService` - จัดการการตรวจสอบสิทธิ์และ permissions

### Schemas

- `response.schema.ts` - Standard response schemas

### Middleware

- `unified-auth.ts` - Unified authentication middleware ที่รองรับทั้ง user และ customer
- `checkUser.ts` - Legacy user middleware (backward compatibility)
- `checkMember.ts` - Legacy customer middleware (backward compatibility)

## การใช้งาน

### 1. Unified Auth Middleware (แนะนำ)

```typescript
import { requireUserAuth, requireSiteOwner, requireSiteAdmin, createUnifiedAuth } from '@/core/middleware';

// User authentication (required)
app.use(requireUserAuth).get('/profile', ({ store }: any) => {
	return { user: store.user };
});

// Site owner only
app.use(requireSiteOwner).post('/sites/:siteId/settings', ({ store }: any) => {
	return { site: store.site, user: store.user };
});

// Custom auth configuration
const customAuth = createUnifiedAuth({
	type: 'user',
	requireAuth: true,
	requireSiteAccess: true,
	allowRoles: ['owner', 'admin', 'editor'],
	requiredPermissions: ['write'],
	loadUserData: true,
	loadSiteData: true,
});

app.use(customAuth).put('/sites/:siteId/content', ({ store }: any) => {
	// store.user, store.site, store.permissions available
});
```

### 2. Manual Access Checks

```typescript
import { checkUserAccess, checkPermission, isSiteOwner } from '@/core/middleware';

// ตรวจสอบสิทธิ์ user ในไซต์
const accessResult = await checkUserAccess(siteId, userId);
if (accessResult.hasAccess) {
	console.log('User role:', accessResult.role);
	console.log('Permissions:', accessResult.permissions);
}

// ตรวจสอบ permission เฉพาะ
const canWrite = await checkPermission(siteId, userId, 'write', 'user');

// ตรวจสอบว่าเป็น owner หรือไม่
const isOwner = await isSiteOwner(siteId, userId);
```

### 3. JWT Operations

```typescript
import { verifyUserToken, verifyCustomerToken, generateToken } from '@/core/middleware';

// Verify tokens
const userPayload = await verifyUserToken(token);
const customerPayload = await verifyCustomerToken(token);

// Generate token
const newToken = await generateToken({ userId: '123', email: '<EMAIL>' });
```

## Pre-configured Middlewares

### User Authentication

- `requireUserAuth` - User authentication (required)
- `optionalUserAuth` - User authentication (optional)

### Customer Authentication

- `requireCustomerAuth` - Customer authentication (required)
- `optionalCustomerAuth` - Customer authentication (optional)

### Site Access Control

- `requireSiteOwner` - Site owner only
- `requireSiteAdmin` - Site owner or admin
- `requireSiteMember` - Any site member
- `requireCustomerSiteAccess` - Customer site access

## Migration Guide

### จาก Legacy Auth Plugins

**เดิม:**

```typescript
import { userAuthPlugin } from '@/core/plugins/auth';

app.use(userAuthPlugin).get('/profile', ({ user }) => ({ user }));
```

**ใหม่:**

```typescript
import { requireUserAuth } from '@/core/middleware';

app.use(requireUserAuth).get('/profile', ({ store }: any) => ({ user: store.user }));
```

### จาก checkUser/checkMember

**เดิม:**

```typescript
import { checkSiteAccess } from '@/core/middleware/checkUser';

app.onBeforeHandle(async c => {
	await checkSiteAccess(c, { requiredRole: 'owner' });
});
```

**ใหม่:**

```typescript
import { requireSiteOwner } from '@/core/middleware';

app.use(requireSiteOwner).get('/endpoint', ({ store }: any) => {
	// store.user, store.site, store.userRole available
});
```

## ข้อดีของระบบใหม่

1. **ลดการซ้ำซ้อน** - JWT verification และ access control logic ถูกรวมเข้าด้วยกัน
2. **ยืดหยุ่น** - สามารถ configure middleware ตามต้องการ
3. **Type Safety** - TypeScript interfaces ที่ชัดเจน
4. **Performance** - ลด database queries ที่ไม่จำเป็น
5. **Maintainable** - โค้ดที่ง่ายต่อการดูแลรักษา
6. **Backward Compatible** - รองรับ legacy code

## Store Properties

เมื่อใช้ unified auth middleware จะมี properties เหล่านี้ใน `store`:

```typescript
interface AuthStore {
	user: User | UserJWTPayload | null;
	customer: Customer | CustomerJWTPayload | null;
	site: Site | null;
	hasAccess: boolean;
	userRole: string | null;
	customerRole: string | null;
	permissions: string[];
}
```
