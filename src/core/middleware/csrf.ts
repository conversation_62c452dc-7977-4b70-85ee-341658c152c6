import { Elysia } from 'elysia';
import { config } from '@/core/config/environment';


/**
 * ✅ CSRF Protection Middleware
 * ป้องกัน Cross-Site Request Forgery attacks
 */

interface CSRFOptions {
	/**
	 * Origins ที่อนุญาต
	 */
	allowedOrigins: string[];

	/**
	 * HTTP methods ที่ต้องตรวจสอบ CSRF
	 */
	protectedMethods: string[];

	/**
	 * Header name สำหรับ CSRF token
	 */
	headerName: string;

	/**
	 * <PERSON>ie name สำหรับ CSRF token
	 */
	cookieName: string;

	/**
	 * ข้ามการตรวจสอบสำหรับ paths เหล่านี้
	 */
	skipPaths: string[];
}

const defaultOptions: CSRFOptions = {
	allowedOrigins: [config.frontendUrl, 'http://localhost:3000', 'http://localhost:8000', 'http://localhost:5173'],
	protectedMethods: ['POST', 'PUT', 'DELETE', 'PATCH'],
	headerName: 'x-csrf-token',
	cookieName: 'csrf-token',
	skipPaths: ['/health', '/metrics', '/user/signin', '/user/signup', '/customer/signin', '/customer/signup'],
};

/**
 * สร้าง CSRF token
 */
function generateCSRFToken(): string {
	return crypto.randomUUID();
}



/**
 * ตรวจสอบว่าเป็น path ที่ข้ามการตรวจสอบหรือไม่
 */
function shouldSkipPath(path: string, skipPaths: string[]): boolean {
	return skipPaths.some(skipPath => {
		if (skipPath.endsWith('*')) {
			return path.startsWith(skipPath.slice(0, -1));
		}
		return path === skipPath;
	});
}

/**
 * CSRF Protection Plugin
 */
export function createCSRFProtection(options: Partial<CSRFOptions> = {}) {
	const opts = { ...defaultOptions, ...options };

	return new Elysia({ name: 'csrf-protection' })
		.onBeforeHandle(({ request }) => {
			const method = request.method.toUpperCase();
			const url = new URL(request.url);
			const path = url.pathname;

			// ข้ามการตรวจสอบสำหรับ methods ที่ปลอดภัย
			if (!opts.protectedMethods.includes(method)) {
				return;
			}

			// ข้ามการตรวจสอบสำหรับ paths ที่กำหนด
			if (shouldSkipPath(path, opts.skipPaths)) {
				return;
			}

			// ตรวจสอบ Origin header
			// if (!isValidOrigin(origin, opts.allowedOrigins)) {
			//     // ถ้าไม่มี origin ให้ตรวจสอบ referer
			//     if (!referer || !isValidOrigin(new URL(referer).origin, opts.allowedOrigins)) {
			//         logger.warn('CSRF protection triggered', {
			//             method,
			//             path,
			//             origin,
			//             referer,
			//             ip: headers['x-forwarded-for'] || 'unknown'
			//         });

			//         set.status = 403;
			//         throw new HttpError(403, 'CSRF protection: Invalid origin');
			//     }
			// }

			// สำหรับ authenticated requests ให้ตรวจสอบ CSRF token
			// const authHeader = headers.authorization;
			// if (authHeader && authHeader.startsWith('Bearer ')) {
			//     if (!csrfToken) {
			//         logger.warn('CSRF protection: Missing CSRF token', {
			//             method,
			//             path,
			//             origin,
			//             ip: headers['x-forwarded-for'] || 'unknown'
			//         });

			//         set.status = 403;
			//         throw new HttpError(403, 'CSRF protection: Missing CSRF token');
			//     }

			//     // ในการใช้งานจริง ควรเก็บ CSRF tokens ใน database หรือ cache
			//     // และตรวจสอบว่า token ที่ส่งมาถูกต้องหรือไม่
			//     // ตอนนี้ข้ามการตรวจสอบนี้ไปก่อน
			// }
		})
		.get('/csrf-token', ({ set }) => {
			const token = generateCSRFToken();

			// Set CSRF token ใน cookie
			set.headers['Set-Cookie'] = `${opts.cookieName}=${token}; Path=/; HttpOnly; SameSite=Lax${
				config.NODE_ENV === 'production' ? '; Secure' : ''
			}`;

			return {
				success: true,
				data: { csrfToken: token },
				message: 'CSRF token generated',
			};
		})
		.as('scoped');
}

/**
 * Pre-configured CSRF protection
 */
export const csrfProtection = createCSRFProtection();

/**
 * CSRF protection สำหรับ API routes
 */
export const apiCSRFProtection = createCSRFProtection({
	skipPaths: ['/health', '/metrics'],
});

/**
 * CSRF protection สำหรับ public routes (ผ่อนปรนกว่า)
 */
export const publicCSRFProtection = createCSRFProtection({
	skipPaths: [
		'/health',
		'/metrics',
		'/user/signin',
		'/user/signup',
		'/user/forgot-password',
		'/user/reset-password',
		'/user/verify-email',
		'/customer/signin',
		'/customer/signup',
		'/customer/forgot-password',
		'/customer/reset-password',
		'/customer/verify-email',
	],
});
