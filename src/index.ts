import { Elysia } from 'elysia';
import { connectDB } from '@/core/config/database';
import { getServerConfig, logServerStarted, logServerStartup } from '@/core/config/server';
import { createError<PERSON>and<PERSON> } from '@/core/handlers/error.handler';
import { setupAllPlugins } from '@/core/plugins';
import { setupAllRoutes } from '@/core/routes';

/**
 * Initialize database connection
 */
async function initializeDatabase() {
	try {
		await connectDB();
	} catch (error) {
		console.error('Failed to connect to database:', error);
		process.exit(1);
	}
}

/**
 * Create and configure the Elysia application
 */
function createApp() {
	const app: any = new Elysia({
		aot: true,
		prefix: '/v1',
		name: 'is1-api',
	});

	// Setup plugins (security, utilities, logging)
	setupAllPlugins(app);

	// Setup routes (organized by feature groups)
	setupAllRoutes(app);

	// Setup error handling
	app.onError(createErrorHandler());

	return app;
}

/**
 * Start the server
 */
async function startServer() {
	// Log server startup
	logServerStartup();

	// Initialize database
	await initializeDatabase();

	// Create app
	const app = createApp();

	// Get server configuration
	const serverConfig = getServerConfig();

	// Start listening
	app.listen(serverConfig);

	// Log successful startup
	logServerStarted(app.server);

	return app;
}

/**
 * Handle graceful shutdown
 */
function setupGracefulShutdown(app: Elysia) {
	const shutdown = async (signal: string) => {
		console.log(`\n🛑 Received ${signal}. Starting graceful shutdown...`);

		try {
			// Close server
			if (app.server) {
				app.server.stop();
				console.log('✅ Server closed');
			}

			// Close database connection
			// Note: mongoose will close automatically when process exits
			console.log('✅ Database connections closed');

			console.log('✅ Graceful shutdown completed');
			process.exit(0);
		} catch (error) {
			console.error('❌ Error during shutdown:', error);
			process.exit(1);
		}
	};

	// Handle different shutdown signals
	process.on('SIGTERM', () => shutdown('SIGTERM'));
	process.on('SIGINT', () => shutdown('SIGINT'));
	process.on('SIGUSR2', () => shutdown('SIGUSR2')); // nodemon restart

	// Handle uncaught exceptions
	process.on('uncaughtException', error => {
		console.error('❌ Uncaught Exception:', error);
		shutdown('uncaughtException');
	});

	// Handle unhandled promise rejections
	process.on('unhandledRejection', (reason, promise) => {
		console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
		shutdown('unhandledRejection');
	});
}

/**
 * Main application entry point
 */
async function main() {
	try {
		const app = await startServer();
		setupGracefulShutdown(app);
	} catch (error) {
		console.error('❌ Failed to start server:', error);
		process.exit(1);
	}
}

// Start the application
main();
