import { t } from 'elysia';

// ประเภทการแจ้งเตือนทั้งหมด
const NotificationTypes = [
	t.Literal('order'),
	t.Literal('product'),
	t.Literal('promotion'),
	t.Literal('system'),
	t.Literal('chat'),
	t.Literal('affiliate'),
	t.Literal('topup'),
	t.<PERSON>teral('membership'),
	t.Literal('expiry'),
	t.Literal('inventory'),
	t.Literal('payment'),
	t.<PERSON>teral('security'),
	t.Literal('marketing'),
];

// Notification Schemas
export const NotificationCreateSchema = t.Object({
	recipientId: t.String({ error: 'recipientId ต้องเป็นข้อความ' }),
	recipientType: t.<PERSON>([t.Literal('user'), t.Literal('customer'), t.Literal('admin')], {
		error: 'recipientType ไม่ถูกต้อง',
	}),
	type: t.Union(NotificationTypes, { error: 'type ไม่ถูกต้อง' }),
	title: t.String({ error: 'title ต้องเป็นข้อความ' }),
	message: t.String({ error: 'message ต้องเป็นข้อความ' }),
	data: t.Optional(
		t.Object({
			orderId: t.Optional(t.String()),
			productId: t.Optional(t.String()),
			chatRoomId: t.Optional(t.String()),
			affiliateId: t.Optional(t.String()),
			customerId: t.Optional(t.String()),
			userId: t.Optional(t.String()),
			amount: t.Optional(t.Number()),
			balance: t.Optional(t.Number()),
			expiryDate: t.Optional(t.String()),
			daysLeft: t.Optional(t.Number()),
			stockLevel: t.Optional(t.Number()),
			threshold: t.Optional(t.Number()),
			url: t.Optional(t.String()),
			image: t.Optional(t.String()),
			metadata: t.Optional(t.Record(t.String(), t.Any())),
		})
	),
	priority: t.Optional(t.Union([t.Literal('low'), t.Literal('medium'), t.Literal('high'), t.Literal('urgent')])),
	channels: t.Optional(
		t.Object({
			inApp: t.Optional(t.Boolean()),
			email: t.Optional(t.Boolean()),
			push: t.Optional(t.Boolean()),
			sms: t.Optional(t.Boolean()),
		})
	),
	scheduledAt: t.Optional(t.String()),
});

export const NotificationQuerySchema = t.Object({
	page: t.Optional(t.String()),
	limit: t.Optional(t.String()),
	type: t.Optional(t.Union(NotificationTypes)),
	status: t.Optional(t.Union([t.Literal('unread'), t.Literal('read'), t.Literal('archived')])),
});

export const NotificationResponseSchema = t.Object({
	success: t.Boolean(),
	data: t.Object({
		_id: t.String(),
		siteId: t.String(),
		recipientId: t.String(),
		recipientType: t.String(),
		type: t.String(),
		title: t.String(),
		message: t.String(),
		data: t.Optional(
			t.Object({
				orderId: t.Optional(t.String()),
				productId: t.Optional(t.String()),
				chatRoomId: t.Optional(t.String()),
				affiliateId: t.Optional(t.String()),
				customerId: t.Optional(t.String()),
				userId: t.Optional(t.String()),
				amount: t.Optional(t.Number()),
				balance: t.Optional(t.Number()),
				expiryDate: t.Optional(t.String()),
				daysLeft: t.Optional(t.Number()),
				stockLevel: t.Optional(t.Number()),
				threshold: t.Optional(t.Number()),
				url: t.Optional(t.String()),
				image: t.Optional(t.String()),
				metadata: t.Optional(t.Record(t.String(), t.Any())),
			})
		),
		priority: t.String(),
		status: t.String(),
		channels: t.Object({
			inApp: t.Boolean(),
			email: t.Boolean(),
			push: t.Boolean(),
			sms: t.Boolean(),
		}),
		deliveryStatus: t.Object({
			inApp: t.Object({
				sent: t.Boolean(),
				delivered: t.Boolean(),
				read: t.Boolean(),
			}),
			email: t.Object({
				sent: t.Boolean(),
				delivered: t.Boolean(),
				opened: t.Boolean(),
			}),
			push: t.Object({
				sent: t.Boolean(),
				delivered: t.Boolean(),
				clicked: t.Boolean(),
			}),
			sms: t.Object({
				sent: t.Boolean(),
				delivered: t.Boolean(),
			}),
		}),
		scheduledAt: t.Optional(t.String()),
		sentAt: t.Optional(t.String()),
		readAt: t.Optional(t.String()),
		createdAt: t.String(),
		updatedAt: t.String(),
	}),
});

export const NotificationListResponseSchema = t.Object({
	success: t.Boolean(),
	data: t.Object({
		notifications: t.Array(
			t.Object({
				_id: t.String(),
				siteId: t.String(),
				recipientId: t.String(),
				recipientType: t.String(),
				type: t.String(),
				title: t.String(),
				message: t.String(),
				data: t.Optional(
					t.Object({
						orderId: t.Optional(t.String()),
						productId: t.Optional(t.String()),
						chatRoomId: t.Optional(t.String()),
						affiliateId: t.Optional(t.String()),
						customerId: t.Optional(t.String()),
						userId: t.Optional(t.String()),
						amount: t.Optional(t.Number()),
						balance: t.Optional(t.Number()),
						expiryDate: t.Optional(t.String()),
						daysLeft: t.Optional(t.Number()),
						stockLevel: t.Optional(t.Number()),
						threshold: t.Optional(t.Number()),
						url: t.Optional(t.String()),
						image: t.Optional(t.String()),
						metadata: t.Optional(t.Record(t.String(), t.Any())),
					})
				),
				priority: t.String(),
				status: t.String(),
				channels: t.Object({
					inApp: t.Boolean(),
					email: t.Boolean(),
					push: t.Boolean(),
					sms: t.Boolean(),
				}),
				deliveryStatus: t.Object({
					inApp: t.Object({
						sent: t.Boolean(),
						delivered: t.Boolean(),
						read: t.Boolean(),
					}),
					email: t.Object({
						sent: t.Boolean(),
						delivered: t.Boolean(),
						opened: t.Boolean(),
					}),
					push: t.Object({
						sent: t.Boolean(),
						delivered: t.Boolean(),
						clicked: t.Boolean(),
					}),
					sms: t.Object({
						sent: t.Boolean(),
						delivered: t.Boolean(),
					}),
				}),
				scheduledAt: t.Optional(t.String()),
				sentAt: t.Optional(t.String()),
				readAt: t.Optional(t.String()),
				createdAt: t.String(),
				updatedAt: t.String(),
			})
		),
		pagination: t.Object({
			page: t.Number(),
			limit: t.Number(),
			total: t.Number(),
			pages: t.Number(),
		}),
		unreadCount: t.Number(),
	}),
});

export const NotificationTemplateSchema = t.Object({
	name: t.String(),
	type: t.Union(NotificationTypes),
	title: t.String(),
	message: t.String(),
	variables: t.Array(t.String()),
	channels: t.Object({
		inApp: t.Boolean(),
		email: t.Boolean(),
		push: t.Boolean(),
		sms: t.Boolean(),
	}),
	conditions: t.Optional(
		t.Object({
			userType: t.Optional(t.Array(t.String())),
			minAmount: t.Optional(t.Number()),
			maxAmount: t.Optional(t.Number()),
			daysBeforeExpiry: t.Optional(t.Number()),
			stockThreshold: t.Optional(t.Number()),
		})
	),
});

export const NotificationSettingsSchema = t.Object({
	preferences: t.Object({
		orderUpdates: t.Boolean(),
		productAlerts: t.Boolean(),
		promotions: t.Boolean(),
		systemMessages: t.Boolean(),
		chatMessages: t.Boolean(),
		affiliateUpdates: t.Boolean(),
		topupAlerts: t.Boolean(),
		membershipUpdates: t.Boolean(),
		expiryWarnings: t.Boolean(),
		inventoryAlerts: t.Boolean(),
		paymentNotifications: t.Boolean(),
		securityAlerts: t.Boolean(),
		marketingMessages: t.Boolean(),
	}),
	channels: t.Object({
		inApp: t.Boolean(),
		email: t.Boolean(),
		push: t.Boolean(),
		sms: t.Boolean(),
	}),
	quietHours: t.Object({
		enabled: t.Boolean(),
		startTime: t.String(),
		endTime: t.String(),
		timezone: t.String(),
	}),
});

export const NotificationStatsResponseSchema = t.Object({
	success: t.Boolean(),
	data: t.Array(
		t.Object({
			_id: t.String(),
			count: t.Number(),
			unread: t.Number(),
		})
	),
});

export const UnreadCountResponseSchema = t.Object({
	success: t.Boolean(),
	data: t.Number(),
});

export const MarkAsReadSchema = t.Object({
	notificationIds: t.Array(t.String()),
});

export const BulkNotificationSchema = t.Object({
	recipients: t.Array(t.String()),
	notificationData: t.Object({
		recipientType: t.Union([t.Literal('user'), t.Literal('customer'), t.Literal('admin')]),
		type: t.Union(NotificationTypes),
		title: t.String(),
		message: t.String(),
		data: t.Optional(
			t.Object({
				orderId: t.Optional(t.String()),
				productId: t.Optional(t.String()),
				chatRoomId: t.Optional(t.String()),
				affiliateId: t.Optional(t.String()),
				customerId: t.Optional(t.String()),
				userId: t.Optional(t.String()),
				amount: t.Optional(t.Number()),
				balance: t.Optional(t.Number()),
				expiryDate: t.Optional(t.String()),
				daysLeft: t.Optional(t.Number()),
				stockLevel: t.Optional(t.Number()),
				threshold: t.Optional(t.Number()),
				url: t.Optional(t.String()),
				image: t.Optional(t.String()),
				metadata: t.Optional(t.Record(t.String(), t.Any())),
			})
		),
		priority: t.Optional(t.Union([t.Literal('low'), t.Literal('medium'), t.Literal('high'), t.Literal('urgent')])),
		channels: t.Optional(
			t.Object({
				inApp: t.Optional(t.Boolean()),
				email: t.Optional(t.Boolean()),
				push: t.Optional(t.Boolean()),
				sms: t.Optional(t.Boolean()),
			})
		),
	}),
});

// Schema สำหรับการแจ้งเตือนเฉพาะ
export const TopupNotificationSchema = t.Object({
	userId: t.String(),
	amount: t.Number(),
	balance: t.Number(),
});

export const MembershipNotificationSchema = t.Object({
	adminUsers: t.Array(t.String()),
	customerName: t.String(),
	customerId: t.String(),
});

export const ExpiryNotificationSchema = t.Object({
	userId: t.String(),
	expiryDate: t.String(),
	daysLeft: t.Number(),
});

export const LowStockNotificationSchema = t.Object({
	adminUsers: t.Array(t.String()),
	productName: t.String(),
	productId: t.String(),
	stockLevel: t.Number(),
	threshold: t.Number(),
});

export const NewProductNotificationSchema = t.Object({
	customerIds: t.Array(t.String()),
	productName: t.String(),
	productId: t.String(),
	productImage: t.Optional(t.String()),
});

export const OrderPurchasedNotificationSchema = t.Object({
	adminUsers: t.Array(t.String()),
	customerName: t.String(),
	orderId: t.String(),
	amount: t.Number(),
});
