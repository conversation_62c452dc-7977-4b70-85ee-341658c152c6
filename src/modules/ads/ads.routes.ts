import { Elysia } from 'elysia';
import { AdsService } from './ads.service';
import { createAdsSchema, updateAdsSchema, adsQuerySchema, trackAdsSchema, getAdsForPageSchema } from './ads.schema';
import { checkUser } from '@/core/middleware/checkUser';
import { checkSite } from '@/core/middleware/checkSite';
import { logger } from '@/core/utils/logger';

const adsService = new AdsService();

export const adsRoutes = new Elysia({ prefix: '/ads' })
	.use(checkUser)
	.use(checkSite)

	// สร้างโฆษณา
	.post(
		'/',
		async ({ body, user, site }) => {
			try {
				if (user.role !== 'admin') {
					throw new Error('ไม่มีสิทธิ์ในการดำเนินการ');
				}

				const ads = await adsService.createAds({
					...body,
					siteId: site._id,
					createdBy: user._id,
				});

				return {
					success: true,
					message: 'สร้างโฆษณาสำเร็จ',
					data: ads,
				};
			} catch (error: any) {
				logger.error('เกิดข้อผิดพลาดในการสร้างโฆษณา:', error);
				return {
					success: false,
					message: error.message || 'เกิดข้อผิดพลาดในการสร้างโฆษณา',
				};
			}
		},
		{
			body: createAdsSchema,
		}
	)

	// ดึงรายการโฆษณา
	.get(
		'/',
		async ({ query, site }) => {
			try {
				const result = await adsService.getAds({
					...query,
					siteId: site._id,
				});

				return {
					success: true,
					message: 'ดึงรายการโฆษณาสำเร็จ',
					data: result,
				};
			} catch (error: any) {
				logger.error('เกิดข้อผิดพลาดในการดึงรายการโฆษณา:', error);
				return {
					success: false,
					message: error.message || 'เกิดข้อผิดพลาดในการดึงรายการโฆษณา',
				};
			}
		},
		{
			query: adsQuerySchema,
		}
	)

	// ดึงโฆษณาตาม ID
	.get('/:id', async ({ params, site }) => {
		try {
			const ads = await adsService.getAdsById(params.id, site._id);

			if (!ads) {
				return {
					success: false,
					message: 'ไม่พบโฆษณาที่ระบุ',
				};
			}

			return {
				success: true,
				message: 'ดึงข้อมูลโฆษณาสำเร็จ',
				data: ads,
			};
		} catch (error: any) {
			logger.error('เกิดข้อผิดพลาดในการดึงข้อมูลโฆษณา:', error);
			return {
				success: false,
				message: error.message || 'เกิดข้อผิดพลาดในการดึงข้อมูลโฆษณา',
			};
		}
	})

	// อัพเดทโฆษณา
	.patch(
		'/:id',
		async ({ params, body, user, site }) => {
			try {
				if (user.role !== 'admin') {
					throw new Error('ไม่มีสิทธิ์ในการดำเนินการ');
				}

				const ads = await adsService.updateAds(params.id, site._id, body);

				if (!ads) {
					return {
						success: false,
						message: 'ไม่พบโฆษณาที่ระบุ',
					};
				}

				return {
					success: true,
					message: 'อัพเดทโฆษณาสำเร็จ',
					data: ads,
				};
			} catch (error: any) {
				logger.error('เกิดข้อผิดพลาดในการอัพเดทโฆษณา:', error);
				return {
					success: false,
					message: error.message || 'เกิดข้อผิดพลาดในการอัพเดทโฆษณา',
				};
			}
		},
		{
			body: updateAdsSchema,
		}
	)

	// ลบโฆษณา
	.delete('/:id', async ({ params, user, site }) => {
		try {
			if (user.role !== 'admin') {
				throw new Error('ไม่มีสิทธิ์ในการดำเนินการ');
			}

			await adsService.deleteAds(params.id, site._id);

			return {
				success: true,
				message: 'ลบโฆษณาสำเร็จ',
			};
		} catch (error: any) {
			logger.error('เกิดข้อผิดพลาดในการลบโฆษณา:', error);
			return {
				success: false,
				message: error.message || 'เกิดข้อผิดพลาดในการลบโฆษณา',
			};
		}
	})

	// ดึงโฆษณาสำหรับแสดงในหน้าเว็บ (Public API)
	.post(
		'/page',
		async ({ body, site, headers }) => {
			try {
				const userAgent = headers['user-agent'] || '';
				const deviceType = this.detectDeviceType(userAgent);

				const ads = await adsService.getAdsForPage(
					site._id,
					body.pageSlug,
					body.userType || 'visitor',
					body.deviceType || deviceType
				);

				return {
					success: true,
					message: 'ดึงโฆษณาสำหรับหน้าเว็บสำเร็จ',
					data: ads,
				};
			} catch (error: any) {
				logger.error('เกิดข้อผิดพลาดในการดึงโฆษณาสำหรับหน้าเว็บ:', error);
				return {
					success: false,
					message: error.message || 'เกิดข้อผิดพลาดในการดึงโฆษณาสำหรับหน้าเว็บ',
				};
			}
		},
		{
			body: getAdsForPageSchema,
		}
	)

	// ติดตามการดูและคลิก (Public API)
	.post(
		'/:id/track',
		async ({ params, body, headers }) => {
			try {
				const userAgent = headers['user-agent'] || '';
				const ipAddress = headers['x-forwarded-for'] || headers['x-real-ip'] || 'unknown';

				const result = await adsService.trackAds(params.id, body.action, {
					userAgent,
					ipAddress,
				});

				return {
					success: true,
					message: 'ติดตามโฆษณาสำเร็จ',
					data: { tracked: result },
				};
			} catch (error: any) {
				logger.error('เกิดข้อผิดพลาดในการติดตามโฆษณา:', error);
				return {
					success: false,
					message: error.message || 'เกิดข้อผิดพลาดในการติดตามโฆษณา',
				};
			}
		},
		{
			body: trackAdsSchema,
		}
	)

	// ดึงสถิติโฆษณา
	.get('/stats', async ({ user, site }) => {
		try {
			if (user.role !== 'admin') {
				throw new Error('ไม่มีสิทธิ์ในการดำเนินการ');
			}

			const stats = await adsService.getAdsStats(site._id);

			return {
				success: true,
				message: 'ดึงสถิติโฆษณาสำเร็จ',
				data: stats,
			};
		} catch (error: any) {
			logger.error('เกิดข้อผิดพลาดในการดึงสถิติโฆษณา:', error);
			return {
				success: false,
				message: error.message || 'เกิดข้อผิดพลาดในการดึงสถิติโฆษณา',
			};
		}
	})

	// คัดลอกโฆษณา
	.post('/:id/duplicate', async ({ params, body, user, site }) => {
		try {
			if (user.role !== 'admin') {
				throw new Error('ไม่มีสิทธิ์ในการดำเนินการ');
			}

			const newTitle = body?.title as string;
			const duplicatedAds = await adsService.duplicateAds(params.id, site._id, newTitle);

			return {
				success: true,
				message: 'คัดลอกโฆษณาสำเร็จ',
				data: duplicatedAds,
			};
		} catch (error: any) {
			logger.error('เกิดข้อผิดพลาดในการคัดลอกโฆษณา:', error);
			return {
				success: false,
				message: error.message || 'เกิดข้อผิดพลาดในการคัดลอกโฆษณา',
			};
		}
	})

	// เปิด/ปิดโฆษณา
	.patch('/:id/toggle', async ({ params, user, site }) => {
		try {
			if (user.role !== 'admin') {
				throw new Error('ไม่มีสิทธิ์ในการดำเนินการ');
			}

			const ads = await adsService.toggleAdsStatus(params.id, site._id);

			if (!ads) {
				return {
					success: false,
					message: 'ไม่พบโฆษณาที่ระบุ',
				};
			}

			return {
				success: true,
				message: `${ads.isActive ? 'เปิด' : 'ปิด'}โฆษณาสำเร็จ`,
				data: ads,
			};
		} catch (error: any) {
			logger.error('เกิดข้อผิดพลาดในการเปิด/ปิดโฆษณา:', error);
			return {
				success: false,
				message: error.message || 'เกิดข้อผิดพลาดในการเปิด/ปิดโฆษณา',
			};
		}
	})

	// Helper method สำหรับตรวจจับประเภทอุปกรณ์
	.derive(() => ({
		detectDeviceType: (userAgent: string): 'mobile' | 'tablet' | 'desktop' => {
			const ua = userAgent.toLowerCase();

			if (ua.includes('mobile') || ua.includes('android') || ua.includes('iphone')) {
				return 'mobile';
			}

			if (ua.includes('tablet') || ua.includes('ipad')) {
				return 'tablet';
			}

			return 'desktop';
		},
	}));
