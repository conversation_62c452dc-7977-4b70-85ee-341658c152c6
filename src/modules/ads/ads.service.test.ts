import { describe, it, expect, beforeEach, afterEach } from 'bun:test';
import { AdsService } from './ads.service';
import { Ads } from './ads.model';

describe('AdsService', () => {
	let adsService: AdsService;
	const testSiteId = 'test-site-123';
	const testUserId = 'test-user-123';

	beforeEach(async () => {
		adsService = new AdsService();
	});

	afterEach(async () => {
		// ลบข้อมูลทดสอบ
		await Ads.deleteMany({});
	});

	describe('createAds', () => {
		it('ควรสร้างโฆษณาแบนเนอร์ได้', async () => {
			const adsData = {
				siteId: testSiteId,
				title: 'แบนเนอร์โปรโมชั่น',
				type: 'banner' as const,
				position: 'header' as const,
				content: {
					imageUrl: '/images/banner.jpg',
					linkUrl: '/promotion',
					linkTarget: '_self' as const,
				},
				displaySettings: {
					showOnPages: 'all' as const,
					showOnMobile: true,
					showOnTablet: true,
					showOnDesktop: true,
					showToVisitors: true,
					showToCustomers: true,
					showToAdmins: true,
				},
				behaviorSettings: {
					showDelay: 0,
					showFrequency: 'always' as const,
					animationType: 'fade' as const,
					closeButton: true,
				},
				isActive: true,
				priority: 1,
				createdBy: testUserId,
			};

			const ads = await adsService.createAds(adsData);

			expect(ads.title).toBe('แบนเนอร์โปรโมชั่น');
			expect(ads.type).toBe('banner');
			expect(ads.position).toBe('header');
			expect(ads.siteId).toBe(testSiteId);
			expect(ads.isActive).toBe(true);
		});

		it('ควรสร้างโฆษณา slide ได้', async () => {
			const adsData = {
				siteId: testSiteId,
				title: 'สไลด์โชว์สินค้า',
				type: 'slide' as const,
				position: 'content-top' as const,
				content: {
					backgroundColor: '#ffffff',
				},
				displaySettings: {
					showOnPages: 'all' as const,
					showOnMobile: true,
					showOnTablet: true,
					showOnDesktop: true,
					showToVisitors: true,
					showToCustomers: true,
					showToAdmins: true,
				},
				behaviorSettings: {
					showFrequency: 'always' as const,
					animationType: 'slide' as const,
				},
				slideSettings: {
					slides: [
						{
							id: 'slide1',
							imageUrl: '/images/slide1.jpg',
							title: 'สินค้าใหม่',
							description: 'สินค้าใหม่ล่าสุด',
							linkUrl: '/products/new',
							linkTarget: '_self' as const,
							order: 1,
						},
						{
							id: 'slide2',
							imageUrl: '/images/slide2.jpg',
							title: 'ลดราคา',
							description: 'ลดราคาพิเศษ',
							linkUrl: '/sale',
							linkTarget: '_self' as const,
							order: 2,
						},
					],
					autoPlay: true,
					autoPlayInterval: 5,
					showDots: true,
					showArrows: true,
					infinite: true,
					pauseOnHover: true,
				},
				isActive: true,
				createdBy: testUserId,
			};

			const ads = await adsService.createAds(adsData);

			expect(ads.type).toBe('slide');
			expect(ads.slideSettings?.slides).toHaveLength(2);
			expect(ads.slideSettings?.autoPlay).toBe(true);
		});

		it('ควรสร้างโฆษณา marquee ได้', async () => {
			const adsData = {
				siteId: testSiteId,
				title: 'ข้อความเลื่อน',
				type: 'marquee' as const,
				position: 'header' as const,
				content: {},
				displaySettings: {
					showOnPages: 'all' as const,
					showOnMobile: true,
					showOnTablet: true,
					showOnDesktop: true,
					showToVisitors: true,
					showToCustomers: true,
					showToAdmins: true,
				},
				behaviorSettings: {
					showFrequency: 'always' as const,
				},
				marqueeSettings: {
					text: 'ข่าวสารล่าสุด: ลดราคาพิเศษ 50% ทุกสินค้า!',
					speed: 50,
					direction: 'left' as const,
					pauseOnHover: true,
					backgroundColor: '#ff0000',
					textColor: '#ffffff',
					fontSize: '16px',
					height: '40px',
				},
				isActive: true,
				createdBy: testUserId,
			};

			const ads = await adsService.createAds(adsData);

			expect(ads.type).toBe('marquee');
			expect(ads.marqueeSettings?.text).toBe('ข่าวสารล่าสุด: ลดราคาพิเศษ 50% ทุกสินค้า!');
			expect(ads.marqueeSettings?.direction).toBe('left');
		});

		it('ควร throw error เมื่อสร้าง slide โดยไม่มี slides', async () => {
			const adsData = {
				siteId: testSiteId,
				title: 'สไลด์ไม่สมบูรณ์',
				type: 'slide' as const,
				position: 'content-top' as const,
				content: {},
				displaySettings: {
					showOnPages: 'all' as const,
					showOnMobile: true,
					showOnTablet: true,
					showOnDesktop: true,
					showToVisitors: true,
					showToCustomers: true,
					showToAdmins: true,
				},
				behaviorSettings: {
					showFrequency: 'always' as const,
				},
				isActive: true,
				createdBy: testUserId,
			};

			await expect(adsService.createAds(adsData)).rejects.toThrow('โฆษณาประเภท slide ต้องมีข้อมูล slides');
		});

		it('ควร throw error เมื่อสร้าง marquee โดยไม่มีข้อความ', async () => {
			const adsData = {
				siteId: testSiteId,
				title: 'Marquee ไม่สมบูรณ์',
				type: 'marquee' as const,
				position: 'header' as const,
				content: {},
				displaySettings: {
					showOnPages: 'all' as const,
					showOnMobile: true,
					showOnTablet: true,
					showOnDesktop: true,
					showToVisitors: true,
					showToCustomers: true,
					showToAdmins: true,
				},
				behaviorSettings: {
					showFrequency: 'always' as const,
				},
				isActive: true,
				createdBy: testUserId,
			};

			await expect(adsService.createAds(adsData)).rejects.toThrow('โฆษณาประเภท marquee ต้องมีข้อความ');
		});
	});

	describe('getAdsForPage', () => {
		beforeEach(async () => {
			// สร้างโฆษณาทดสอบ
			await adsService.createAds({
				siteId: testSiteId,
				title: 'แบนเนอร์หน้าแรก',
				type: 'banner',
				position: 'header',
				content: { imageUrl: '/banner1.jpg' },
				displaySettings: {
					showOnPages: 'specific',
					specificPages: ['home'],
					showOnMobile: true,
					showOnTablet: true,
					showOnDesktop: true,
					showToVisitors: true,
					showToCustomers: true,
					showToAdmins: true,
				},
				behaviorSettings: {
					showFrequency: 'always',
				},
				isActive: true,
				createdBy: testUserId,
			});

			await adsService.createAds({
				siteId: testSiteId,
				title: 'Modal ทุกหน้า',
				type: 'modal',
				position: 'custom',
				content: { html: '<div>Modal Content</div>' },
				displaySettings: {
					showOnPages: 'all',
					showOnMobile: true,
					showOnTablet: true,
					showOnDesktop: true,
					showToVisitors: true,
					showToCustomers: true,
					showToAdmins: true,
				},
				behaviorSettings: {
					showFrequency: 'always',
					autoClose: 10,
				},
				isActive: true,
				createdBy: testUserId,
			});
		});

		it('ควรดึงโฆษณาสำหรับหน้าแรกได้', async () => {
			const ads = await adsService.getAdsForPage(testSiteId, 'home', 'visitor', 'desktop');

			expect(ads.banners).toHaveLength(1);
			expect(ads.modals).toHaveLength(1);
			expect(ads.banners[0].title).toBe('แบนเนอร์หน้าแรก');
			expect(ads.modals[0].title).toBe('Modal ทุกหน้า');
		});

		it('ควรดึงเฉพาะ modal สำหรับหน้าอื่น', async () => {
			const ads = await adsService.getAdsForPage(testSiteId, 'about', 'visitor', 'desktop');

			expect(ads.banners).toHaveLength(0);
			expect(ads.modals).toHaveLength(1);
			expect(ads.modals[0].title).toBe('Modal ทุกหน้า');
		});
	});

	describe('trackAds', () => {
		it('ควรติดตามการดูโฆษณาได้', async () => {
			const ads = await adsService.createAds({
				siteId: testSiteId,
				title: 'โฆษณาทดสอบ',
				type: 'banner',
				position: 'header',
				content: { imageUrl: '/test.jpg' },
				displaySettings: {
					showOnPages: 'all',
					showOnMobile: true,
					showOnTablet: true,
					showOnDesktop: true,
					showToVisitors: true,
					showToCustomers: true,
					showToAdmins: true,
				},
				behaviorSettings: {
					showFrequency: 'always',
				},
				isActive: true,
				createdBy: testUserId,
			});

			const result = await adsService.trackAds(ads._id, 'view');
			expect(result).toBe(true);

			// ตรวจสอบว่าสถิติถูกอัพเดท
			const updatedAds = await Ads.findById(ads._id);
			expect(updatedAds?.displaySettings.currentViews).toBe(1);
		});

		it('ควรติดตามการคลิกโฆษณาได้', async () => {
			const ads = await adsService.createAds({
				siteId: testSiteId,
				title: 'โฆษณาทดสอบ',
				type: 'banner',
				position: 'header',
				content: { imageUrl: '/test.jpg' },
				displaySettings: {
					showOnPages: 'all',
					showOnMobile: true,
					showOnTablet: true,
					showOnDesktop: true,
					showToVisitors: true,
					showToCustomers: true,
					showToAdmins: true,
				},
				behaviorSettings: {
					showFrequency: 'always',
				},
				isActive: true,
				createdBy: testUserId,
			});

			const result = await adsService.trackAds(ads._id, 'click');
			expect(result).toBe(true);

			// ตรวจสอบว่าสถิติถูกอัพเดท
			const updatedAds = await Ads.findById(ads._id);
			expect(updatedAds?.displaySettings.currentClicks).toBe(1);
		});

		it('ควรปิดโฆษณาอัตโนมัติเมื่อถึงขีดจำกัดการดู', async () => {
			const ads = await adsService.createAds({
				siteId: testSiteId,
				title: 'โฆษณาจำกัดการดู',
				type: 'banner',
				position: 'header',
				content: { imageUrl: '/test.jpg' },
				displaySettings: {
					showOnPages: 'all',
					maxViews: 2,
					showOnMobile: true,
					showOnTablet: true,
					showOnDesktop: true,
					showToVisitors: true,
					showToCustomers: true,
					showToAdmins: true,
				},
				behaviorSettings: {
					showFrequency: 'always',
				},
				isActive: true,
				createdBy: testUserId,
			});

			// ดู 2 ครั้ง
			await adsService.trackAds(ads._id, 'view');
			await adsService.trackAds(ads._id, 'view');

			// ครั้งที่ 3 ควรปิดโฆษณา
			const result = await adsService.trackAds(ads._id, 'view');
			expect(result).toBe(false);

			// ตรวจสอบว่าโฆษณาถูกปิด
			const updatedAds = await Ads.findById(ads._id);
			expect(updatedAds?.isActive).toBe(false);
		});
	});

	describe('getAdsStats', () => {
		it('ควรคำนวณสถิติโฆษณาได้ถูกต้อง', async () => {
			// สร้างโฆษณาหลายประเภท
			const banner = await adsService.createAds({
				siteId: testSiteId,
				title: 'แบนเนอร์',
				type: 'banner',
				position: 'header',
				content: { imageUrl: '/banner.jpg' },
				displaySettings: {
					showOnPages: 'all',
					currentViews: 100,
					currentClicks: 10,
					showOnMobile: true,
					showOnTablet: true,
					showOnDesktop: true,
					showToVisitors: true,
					showToCustomers: true,
					showToAdmins: true,
				},
				behaviorSettings: {
					showFrequency: 'always',
				},
				isActive: true,
				createdBy: testUserId,
			});

			const modal = await adsService.createAds({
				siteId: testSiteId,
				title: 'Modal',
				type: 'modal',
				position: 'custom',
				content: { html: '<div>Modal</div>' },
				displaySettings: {
					showOnPages: 'all',
					currentViews: 50,
					currentClicks: 5,
					showOnMobile: true,
					showOnTablet: true,
					showOnDesktop: true,
					showToVisitors: true,
					showToCustomers: true,
					showToAdmins: true,
				},
				behaviorSettings: {
					showFrequency: 'always',
				},
				isActive: false,
				createdBy: testUserId,
			});

			const stats = await adsService.getAdsStats(testSiteId);

			expect(stats.totalAds).toBe(2);
			expect(stats.activeAds).toBe(1);
			expect(stats.totalViews).toBe(150);
			expect(stats.totalClicks).toBe(15);
			expect(stats.statsByType).toHaveLength(2);

			const bannerStats = stats.statsByType.find(s => s.type === 'banner');
			expect(bannerStats?.count).toBe(1);
			expect(bannerStats?.views).toBe(100);
			expect(bannerStats?.clicks).toBe(10);
		});
	});

	describe('duplicateAds', () => {
		it('ควรคัดลอกโฆษณาได้', async () => {
			const originalAds = await adsService.createAds({
				siteId: testSiteId,
				title: 'โฆษณาต้นฉบับ',
				type: 'banner',
				position: 'header',
				content: { imageUrl: '/original.jpg' },
				displaySettings: {
					showOnPages: 'all',
					currentViews: 100,
					currentClicks: 10,
					showOnMobile: true,
					showOnTablet: true,
					showOnDesktop: true,
					showToVisitors: true,
					showToCustomers: true,
					showToAdmins: true,
				},
				behaviorSettings: {
					showFrequency: 'always',
				},
				isActive: true,
				createdBy: testUserId,
			});

			const duplicatedAds = await adsService.duplicateAds(originalAds._id, testSiteId, 'โฆษณาคัดลอก');

			expect(duplicatedAds.title).toBe('โฆษณาคัดลอก');
			expect(duplicatedAds.type).toBe(originalAds.type);
			expect(duplicatedAds.content.imageUrl).toBe(originalAds.content.imageUrl);
			expect(duplicatedAds.displaySettings.currentViews).toBe(0);
			expect(duplicatedAds.displaySettings.currentClicks).toBe(0);
			expect(duplicatedAds.isActive).toBe(false);
		});
	});
});
