import { Ads } from './ads.model';
import { logger } from '@/core/utils/logger';

export class AdsService {
	// สร้างโฆษณา
	async createAds(data: Partial<IAds>): Promise<IAds> {
		try {
			// ตรวจสอบข้อมูลตามประเภทโฆษณา
			this.validateAdsData(data);

			const ads = new Ads(data);
			await ads.save();

			logger.info(`สร้างโฆษณา: ${ads.title} (${ads.type}) สำหรับเว็บไซต์ ${data.siteId}`);

			return ads;
		} catch (error) {
			logger.error('เกิดข้อผิดพลาดในการสร้างโฆษณา:', error);
			throw error;
		}
	}

	// อัพเดทโฆษณา
	async updateAds(adsId: string, siteId: string, data: Partial<IAds>): Promise<IAds | null> {
		try {
			if (data.type || data.content || data.slideSettings || data.marqueeSettings) {
				this.validateAdsData(data);
			}

			const ads = await Ads.findOneAndUpdate({ _id: adsId, siteId }, data, { new: true });

			if (!ads) {
				throw new Error('ไม่พบโฆษณาที่ระบุ');
			}

			logger.info(`อัพเดทโฆษณา: ${ads.title}`);

			return ads;
		} catch (error) {
			logger.error('เกิดข้อผิดพลาดในการอัพเดทโฆษณา:', error);
			throw error;
		}
	}

	// ลบโฆษณา
	async deleteAds(adsId: string, siteId: string): Promise<boolean> {
		try {
			const result = await Ads.findOneAndDelete({ _id: adsId, siteId });

			if (!result) {
				throw new Error('ไม่พบโฆษณาที่ระบุ');
			}

			logger.info(`ลบโฆษณา: ${result.title}`);

			return true;
		} catch (error) {
			logger.error('เกิดข้อผิดพลาดในการลบโฆษณา:', error);
			throw error;
		}
	}

	// ดึงรายการโฆษณา
	async getAds(query: {
		siteId?: string;
		type?: string;
		position?: string;
		isActive?: boolean;
		page?: number;
		limit?: number;
	}): Promise<{ ads: IAds[]; total: number; page: number; totalPages: number }> {
		try {
			const page = query.page || 1;
			const limit = query.limit || 20;
			const skip = (page - 1) * limit;

			const filter: any = {};

			if (query.siteId) filter.siteId = query.siteId;
			if (query.type) filter.type = query.type;
			if (query.position) filter.position = query.position;
			if (query.isActive !== undefined) filter.isActive = query.isActive;

			const [ads, total] = await Promise.all([
				Ads.find(filter).sort({ priority: -1, createdAt: -1 }).skip(skip).limit(limit).lean(),
				Ads.countDocuments(filter),
			]);

			return {
				ads: ads as IAds[],
				total,
				page,
				totalPages: Math.ceil(total / limit),
			};
		} catch (error) {
			logger.error('เกิดข้อผิดพลาดในการดึงรายการโฆษณา:', error);
			throw error;
		}
	}

	// ดึงโฆษณาตาม ID
	async getAdsById(adsId: string, siteId: string): Promise<IAds | null> {
		try {
			return (await Ads.findOne({ _id: adsId, siteId }).lean()) as IAds | null;
		} catch (error) {
			logger.error('เกิดข้อผิดพลาดในการดึงโฆษณา:', error);
			throw error;
		}
	}

	// ดึงโฆษณาสำหรับแสดงในหน้าเว็บ
	async getAdsForPage(
		siteId: string,
		pageSlug: string,
		userType: 'visitor' | 'customer' | 'admin' = 'visitor',
		deviceType: 'mobile' | 'tablet' | 'desktop' = 'desktop'
	): Promise<{
		banners: IAds[];
		modals: IAds[];
		slides: IAds[];
		marquees: IAds[];
		popups: IAds[];
		floating: IAds[];
	}> {
		try {
			const now = new Date();

			// สร้าง filter พื้นฐาน
			const baseFilter: any = {
				siteId,
				isActive: true,
				$or: [{ 'displaySettings.startDate': { $exists: false } }, { 'displaySettings.startDate': { $lte: now } }],
				$and: [
					{
						$or: [{ 'displaySettings.endDate': { $exists: false } }, { 'displaySettings.endDate': { $gte: now } }],
					},
				],
			};

			// กรองตามประเภทผู้ใช้
			const userTypeFilter: any = {};
			if (userType === 'visitor') userTypeFilter['displaySettings.showToVisitors'] = true;
			if (userType === 'customer') userTypeFilter['displaySettings.showToCustomers'] = true;
			if (userType === 'admin') userTypeFilter['displaySettings.showToAdmins'] = true;

			// กรองตามอุปกรณ์
			const deviceFilter: any = {};
			if (deviceType === 'mobile') deviceFilter['displaySettings.showOnMobile'] = true;
			if (deviceType === 'tablet') deviceFilter['displaySettings.showOnTablet'] = true;
			if (deviceType === 'desktop') deviceFilter['displaySettings.showOnDesktop'] = true;

			// รวม filter
			const finalFilter = {
				...baseFilter,
				...userTypeFilter,
				...deviceFilter,
			};

			// ดึงโฆษณาทั้งหมดที่ตรงเงื่อนไข
			const allAds = (await Ads.find(finalFilter).sort({ priority: -1, createdAt: -1 }).lean()) as IAds[];

			// กรองตามหน้าที่แสดง
			const filteredAds = allAds.filter(ad => this.shouldShowOnPage(ad, pageSlug));

			// แยกตามประเภท
			const result = {
				banners: filteredAds.filter(ad => ad.type === 'banner'),
				modals: filteredAds.filter(ad => ad.type === 'modal'),
				slides: filteredAds.filter(ad => ad.type === 'slide'),
				marquees: filteredAds.filter(ad => ad.type === 'marquee'),
				popups: filteredAds.filter(ad => ad.type === 'popup'),
				floating: filteredAds.filter(ad => ad.type === 'floating'),
			};

			return result;
		} catch (error) {
			logger.error('เกิดข้อผิดพลาดในการดึงโฆษณาสำหรับหน้าเว็บ:', error);
			throw error;
		}
	}

	// ติดตามการดูและคลิก
	async trackAds(
		adsId: string,
		action: 'view' | 'click',
		metadata?: {
			userAgent?: string;
			ipAddress?: string;
		}
	): Promise<boolean> {
		try {
			const ads = await Ads.findById(adsId);
			if (!ads) {
				throw new Error('ไม่พบโฆษณาที่ระบุ');
			}

			// ตรวจสอบขีดจำกัด
			if (action === 'view' && ads.displaySettings.maxViews) {
				if (ads.displaySettings.currentViews >= ads.displaySettings.maxViews) {
					// ปิดโฆษณาอัตโนมัติเมื่อถึงขีดจำกัด
					await Ads.findByIdAndUpdate(adsId, { isActive: false });
					return false;
				}
			}

			if (action === 'click' && ads.displaySettings.maxClicks) {
				if (ads.displaySettings.currentClicks >= ads.displaySettings.maxClicks) {
					await Ads.findByIdAndUpdate(adsId, { isActive: false });
					return false;
				}
			}

			// อัพเดทสถิติ
			const updateField = action === 'view' ? 'displaySettings.currentViews' : 'displaySettings.currentClicks';
			await Ads.findByIdAndUpdate(adsId, { $inc: { [updateField]: 1 } });

			logger.info(`ติดตาม ${action} โฆษณา: ${ads.title}`, metadata);

			return true;
		} catch (error) {
			logger.error('เกิดข้อผิดพลาดในการติดตามโฆษณา:', error);
			throw error;
		}
	}

	// ดึงสถิติโฆษณา
	async getAdsStats(siteId: string): Promise<{
		totalAds: number;
		activeAds: number;
		totalViews: number;
		totalClicks: number;
		statsByType: Array<{
			type: string;
			count: number;
			views: number;
			clicks: number;
		}>;
	}> {
		try {
			const [totalStats, typeStats] = await Promise.all([
				Ads.aggregate([
					{ $match: { siteId } },
					{
						$group: {
							_id: null,
							totalAds: { $sum: 1 },
							activeAds: { $sum: { $cond: ['$isActive', 1, 0] } },
							totalViews: { $sum: '$displaySettings.currentViews' },
							totalClicks: { $sum: '$displaySettings.currentClicks' },
						},
					},
				]),
				Ads.aggregate([
					{ $match: { siteId } },
					{
						$group: {
							_id: '$type',
							count: { $sum: 1 },
							views: { $sum: '$displaySettings.currentViews' },
							clicks: { $sum: '$displaySettings.currentClicks' },
						},
					},
					{ $sort: { count: -1 } },
				]),
			]);

			const stats = totalStats[0] || {
				totalAds: 0,
				activeAds: 0,
				totalViews: 0,
				totalClicks: 0,
			};

			const statsByType = typeStats.map(stat => ({
				type: stat._id,
				count: stat.count,
				views: stat.views,
				clicks: stat.clicks,
			}));

			return {
				...stats,
				statsByType,
			};
		} catch (error) {
			logger.error('เกิดข้อผิดพลาดในการดึงสถิติโฆษณา:', error);
			throw error;
		}
	}

	// ตรวจสอบว่าควรแสดงโฆษณาในหน้านี้หรือไม่
	private shouldShowOnPage(ads: IAds, pageSlug: string): boolean {
		const { showOnPages, specificPages = [], excludePages = [] } = ads.displaySettings;

		switch (showOnPages) {
			case 'all':
				return !excludePages.includes(pageSlug);
			case 'specific':
				return specificPages.includes(pageSlug);
			case 'exclude':
				return !excludePages.includes(pageSlug);
			default:
				return true;
		}
	}

	// ตรวจสอบความถูกต้องของข้อมูลโฆษณา
	private validateAdsData(data: Partial<IAds>): void {
		if (data.type === 'slide' && (!data.slideSettings || !data.slideSettings.slides?.length)) {
			throw new Error('โฆษณาประเภท slide ต้องมีข้อมูล slides');
		}

		if (data.type === 'marquee' && (!data.marqueeSettings || !data.marqueeSettings.text)) {
			throw new Error('โฆษณาประเภท marquee ต้องมีข้อความ');
		}

		if (data.displaySettings?.startDate && data.displaySettings?.endDate) {
			if (data.displaySettings.startDate >= data.displaySettings.endDate) {
				throw new Error('วันที่เริ่มต้องน้อยกว่าวันที่สิ้นสุด');
			}
		}

		if (data.displaySettings?.maxViews && data.displaySettings.maxViews < 1) {
			throw new Error('จำนวนการดูสูงสุดต้องมากกว่า 0');
		}

		if (data.displaySettings?.maxClicks && data.displaySettings.maxClicks < 1) {
			throw new Error('จำนวนการคลิกสูงสุดต้องมากกว่า 0');
		}
	}

	// คัดลอกโฆษณา
	async duplicateAds(adsId: string, siteId: string, newTitle?: string): Promise<IAds> {
		try {
			const originalAds = await Ads.findOne({ _id: adsId, siteId });
			if (!originalAds) {
				throw new Error('ไม่พบโฆษณาที่ระบุ');
			}

			const duplicatedData = {
				...originalAds.toObject(),
				_id: undefined,
				title: newTitle || `${originalAds.title} (Copy)`,
				displaySettings: {
					...originalAds.displaySettings,
					currentViews: 0,
					currentClicks: 0,
				},
				isActive: false, // เริ่มต้นเป็น inactive
				createdAt: undefined,
				updatedAt: undefined,
			};

			const duplicatedAds = new Ads(duplicatedData);
			await duplicatedAds.save();

			logger.info(`คัดลอกโฆษณา: ${originalAds.title} -> ${duplicatedAds.title}`);

			return duplicatedAds;
		} catch (error) {
			logger.error('เกิดข้อผิดพลาดในการคัดลอกโฆษณา:', error);
			throw error;
		}
	}

	// เปิด/ปิดโฆษณา
	async toggleAdsStatus(adsId: string, siteId: string): Promise<IAds | null> {
		try {
			const ads = await Ads.findOne({ _id: adsId, siteId });
			if (!ads) {
				throw new Error('ไม่พบโฆษณาที่ระบุ');
			}

			ads.isActive = !ads.isActive;
			await ads.save();

			logger.info(`${ads.isActive ? 'เปิด' : 'ปิด'}โฆษณา: ${ads.title}`);

			return ads;
		} catch (error) {
			logger.error('เกิดข้อผิดพลาดในการเปิด/ปิดโฆษณา:', error);
			throw error;
		}
	}
}
