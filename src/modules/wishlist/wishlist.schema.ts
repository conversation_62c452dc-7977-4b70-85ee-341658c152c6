import { t } from 'elysia';

// Wishlist Schemas
export const WishlistCreateSchema = t.Object({
	name: t.String({ minLength: 1, error: 'name ต้องไม่ว่าง' }),
	description: t.Optional(t.String({ error: 'description ต้องเป็นข้อความ' })),
	isPublic: t.Optional(t.<PERSON>({ error: 'isPublic ต้องเป็น true หรือ false เท่านั้น' })),
	isDefault: t.Optional(t.<PERSON>({ error: 'isDefault ต้องเป็น true หรือ false เท่านั้น' })),
	settings: t.Optional(
		t.Object({
			allowNotifications: t.<PERSON>tional(t.<PERSON>({ error: 'allowNotifications ต้องเป็น true หรือ false เท่านั้น' })),
			notifyOnSale: t.<PERSON>tional(t.<PERSON>({ error: 'notifyOnSale ต้องเป็น true หรือ false เท่านั้น' })),
			notifyOnStock: t.<PERSON>(t.<PERSON>({ error: 'notifyOnStock ต้องเป็น true หรือ false เท่านั้น' })),
			notifyOnPriceDrop: t.Optional(t.<PERSON>({ error: 'notifyOnPriceDrop ต้องเป็น true หรือ false เท่านั้น' })),
		})
	),
});

export const WishlistUpdateSchema = t.Object({
	name: t.Optional(t.String({ minLength: 1, error: 'name ต้องไม่ว่าง' })),
	description: t.Optional(t.String({ error: 'description ต้องเป็นข้อความ' })),
	isPublic: t.Optional(t.Boolean({ error: 'isPublic ต้องเป็น true หรือ false เท่านั้น' })),
	settings: t.Optional(
		t.Object({
			allowNotifications: t.Optional(t.Boolean({ error: 'allowNotifications ต้องเป็น true หรือ false เท่านั้น' })),
			notifyOnSale: t.Optional(t.Boolean({ error: 'notifyOnSale ต้องเป็น true หรือ false เท่านั้น' })),
			notifyOnStock: t.Optional(t.Boolean({ error: 'notifyOnStock ต้องเป็น true หรือ false เท่านั้น' })),
			notifyOnPriceDrop: t.Optional(t.Boolean({ error: 'notifyOnPriceDrop ต้องเป็น true หรือ false เท่านั้น' })),
		})
	),
});

export const WishlistItemSchema = t.Object({
	productId: t.String({ error: 'productId ต้องเป็นข้อความ' }),
	notes: t.Optional(t.String({ error: 'notes ต้องเป็นข้อความ' })),
	priority: t.Optional(
		t.Union(
			[
				t.Literal('low', { error: 'ต้องเป็น low เท่านั้น' }),
				t.Literal('medium', { error: 'ต้องเป็น medium เท่านั้น' }),
				t.Literal('high', { error: 'ต้องเป็น high เท่านั้น' }),
			],
			{ error: 'priority ไม่ถูกต้อง' }
		)
	),
});

export const WishlistShareSchema = t.Object({
	sharedWith: t.String({ error: 'sharedWith ต้องเป็นข้อความ' }),
	permissions: t.Union(
		[
			t.Literal('view', { error: 'ต้องเป็น view เท่านั้น' }),
			t.Literal('edit', { error: 'ต้องเป็น edit เท่านั้น' }),
			t.Literal('admin', { error: 'ต้องเป็น admin เท่านั้น' }),
		],
		{ error: 'permissions ไม่ถูกต้อง' }
	),
	expiresAt: t.Optional(t.String({ error: 'expiresAt ต้องเป็นข้อความ' })),
});

export const WishlistResponseSchema = t.Object({
	success: t.Boolean(),
	data: t.Object({
		_id: t.String(),
		siteId: t.String(),
		userId: t.String(),
		userType: t.String(),
		name: t.String(),
		description: t.Optional(t.String()),
		isPublic: t.Boolean(),
		isDefault: t.Boolean(),
		items: t.Array(
			t.Object({
				productId: t.String(),
				addedAt: t.String(),
				notes: t.Optional(t.String()),
				priority: t.String(),
				priceWhenAdded: t.Optional(t.Number()),
			})
		),
		settings: t.Object({
			allowNotifications: t.Boolean(),
			notifyOnSale: t.Boolean(),
			notifyOnStock: t.Boolean(),
			notifyOnPriceDrop: t.Boolean(),
		}),
		stats: t.Object({
			totalItems: t.Number(),
			totalValue: t.Number(),
			lastUpdated: t.String(),
		}),
		createdAt: t.String(),
		updatedAt: t.String(),
	}),
});

export const WishlistListResponseSchema = t.Object({
	success: t.Boolean(),
	data: t.Array(
		t.Object({
			_id: t.String(),
			siteId: t.String(),
			userId: t.String(),
			userType: t.String(),
			name: t.String(),
			description: t.Optional(t.String()),
			isPublic: t.Boolean(),
			isDefault: t.Boolean(),
			items: t.Array(
				t.Object({
					productId: t.String(),
					addedAt: t.String(),
					notes: t.Optional(t.String()),
					priority: t.String(),
					priceWhenAdded: t.Optional(t.Number()),
				})
			),
			settings: t.Object({
				allowNotifications: t.Boolean(),
				notifyOnSale: t.Boolean(),
				notifyOnStock: t.Boolean(),
				notifyOnPriceDrop: t.Boolean(),
			}),
			stats: t.Object({
				totalItems: t.Number(),
				totalValue: t.Number(),
				lastUpdated: t.String(),
			}),
			createdAt: t.String(),
			updatedAt: t.String(),
		})
	),
});

export const WishlistShareResponseSchema = t.Object({
	success: t.Boolean(),
	data: t.Object({
		_id: t.String(),
		siteId: t.String(),
		wishlistId: t.String(),
		sharedBy: t.String(),
		sharedWith: t.String(),
		permissions: t.String(),
		expiresAt: t.Optional(t.String()),
		isActive: t.Boolean(),
		createdAt: t.String(),
		updatedAt: t.String(),
	}),
});

export const WishlistStatsResponseSchema = t.Object({
	success: t.Boolean(),
	data: t.Object({
		totalWishlists: t.Number(),
		totalItems: t.Number(),
		totalValue: t.Number(),
		averageItemsPerWishlist: t.Number(),
	}),
});

export const PriceAlertResponseSchema = t.Object({
	success: t.Boolean(),
	data: t.Array(
		t.Object({
			wishlistId: t.String(),
			productId: t.String(),
			productName: t.String(),
			oldPrice: t.Number(),
			newPrice: t.Number(),
			discount: t.Number(),
		})
	),
});

export const WishlistRecommendationResponseSchema = t.Object({
	success: t.Boolean(),
	data: t.Array(
		t.Object({
			_id: t.String(),
			name: t.String(),
			description: t.Optional(t.String()),
			price: t.Number(),
			image: t.Optional(t.String()),
			category: t.Optional(t.String()),
			brand: t.Optional(t.String()),
		})
	),
});
