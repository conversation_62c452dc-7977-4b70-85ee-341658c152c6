import { Elysia, t } from 'elysia';
import * as wishlistService from './wishlist.service';
import { userPlugin } from '@/core/middleware/checkUser';

export const wishlistRoutes = new Elysia({ prefix: '/wishlists' })
	.use(userPlugin)
	.get('/', async ({ store }: any) => {
		const wishlists = await wishlistService.getUserWishlists(store.siteId, store.userId);

		return {
			success: true,
			data: wishlists,
		};
	})
	.get('/default', async ({ store }: any) => {
		const wishlist = await wishlistService.getWishlistById(store.siteId, 'default', store.userId);

		return {
			success: true,
			data: wishlist,
		};
	})
	.post(
		'/',
		async ({ body, store }: any) => {
			const wishlist = await wishlistService.createWishlist(store.siteId, store.userId, body);

			return {
				success: true,
				data: wishlist,
			};
		},
		{
			body: t.Object({
				name: t.String({ minLength: 1 }),
				description: t.Optional(t.String()),
				isPublic: t.Optional(t.<PERSON>()),
				isDefault: t.Optional(t.<PERSON>()),
				settings: t.Optional(
					t.Object({
						allowNotifications: t.Optional(t.Boolean()),
						notifyOnSale: t.Optional(t.Boolean()),
						notifyOnStock: t.Optional(t.Boolean()),
						notifyOnPriceDrop: t.Optional(t.Boolean()),
					})
				),
			}),
		}
	)
	.get(
		'/:wishlistId',
		async ({ params, store }: any) => {
			const { wishlistId } = params;

			const wishlist = await wishlistService.getWishlistById(store.siteId, wishlistId, store.userId);

			return {
				success: true,
				data: wishlist,
			};
		},
		{
			params: t.Object({
				wishlistId: t.String(),
			}),
		}
	)
	.put(
		'/:wishlistId',
		async ({ params, body, store }: any) => {
			const { wishlistId } = params;

			const wishlist = await wishlistService.updateWishlist(store.siteId, wishlistId, store.userId, body);

			return {
				success: true,
				data: wishlist,
			};
		},
		{
			params: t.Object({
				wishlistId: t.String(),
			}),
			body: t.Object({
				name: t.Optional(t.String({ minLength: 1 })),
				description: t.Optional(t.String()),
				isPublic: t.Optional(t.Boolean()),
				settings: t.Optional(
					t.Object({
						allowNotifications: t.Optional(t.Boolean()),
						notifyOnSale: t.Optional(t.Boolean()),
						notifyOnStock: t.Optional(t.Boolean()),
						notifyOnPriceDrop: t.Optional(t.Boolean()),
					})
				),
			}),
		}
	)
	.delete(
		'/:wishlistId',
		async ({ params, store }: any) => {
			const { wishlistId } = params;

			const result = await wishlistService.deleteWishlist(store.siteId, wishlistId, store.userId);

			return {
				success: true,
				data: result,
			};
		},
		{
			params: t.Object({
				wishlistId: t.String(),
			}),
		}
	)
	.post(
		'/:wishlistId/items',
		async ({ params, body, store }: any) => {
			const { wishlistId } = params;

			const wishlist = await wishlistService.addItemToWishlist(store.siteId, wishlistId, body);

			return {
				success: true,
				data: wishlist,
			};
		},
		{
			params: t.Object({
				wishlistId: t.String(),
			}),
			body: t.Object({
				productId: t.String(),
				notes: t.Optional(t.String()),
				priority: t.Optional(t.Union([t.Literal('low'), t.Literal('medium'), t.Literal('high')])),
			}),
		}
	)
	.delete(
		'/:wishlistId/items/:productId',
		async ({ params, store }: any) => {
			const { wishlistId, productId } = params;

			const wishlist = await wishlistService.removeItemFromWishlist(store.siteId, wishlistId, productId);

			return {
				success: true,
				data: wishlist,
			};
		},
		{
			params: t.Object({
				wishlistId: t.String(),
				productId: t.String(),
			}),
		}
	)
	.post(
		'/:wishlistId/share',
		async ({ params, body, store }: any) => {
			const { wishlistId } = params;

			const share = await wishlistService.shareWishlist(store.siteId, wishlistId, {
				...body,
				sharedBy: store.userId,
			});

			return {
				success: true,
				data: share,
			};
		},
		{
			params: t.Object({
				wishlistId: t.String(),
			}),
			body: t.Object({
				sharedWith: t.String(),
				permissions: t.Union([t.Literal('view'), t.Literal('edit'), t.Literal('admin')]),
				expiresAt: t.Optional(t.String()),
			}),
		}
	)
	.get('/shared', async ({ store }: any) => {
		const shares = await wishlistService.getSharedWishlists(store.siteId, store.userId);

		return {
			success: true,
			data: shares,
		};
	})
	.get('/recommendations', async ({ store }: any) => {
		const recommendations = await wishlistService.getWishlistRecommendations(store.siteId, store.userId);

		return {
			success: true,
			data: recommendations,
		};
	})
	.get('/price-alerts', async ({ store }: any) => {
		const alerts = await wishlistService.checkPriceAlerts(store.siteId, store.userId);

		return {
			success: true,
			data: alerts,
		};
	})
	.get('/stats', async ({ store }: any) => {
		const stats = await wishlistService.getWishlistStats(store.siteId, store.userId);

		return {
			success: true,
			data: stats,
		};
	});
