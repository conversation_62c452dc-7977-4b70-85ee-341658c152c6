import { Elysia } from 'elysia';
import { TopupService } from './topup.service';
import { createTopupSchema, topupQuerySchema, processTopupSchema } from './topup.schema';
import { userAuthPlugin } from '@/core/plugins/auth';
import { HttpError } from '@/core/utils/error';

const topupService = new TopupService();

export const topupRoutes = new Elysia({ prefix: '/topup' })
	.use(userAuthPlugin)

	// สร้างคำขอเติมเงิน
	.post(
		'/',
		async ({ body, user }) => {
			if (user?.role !== 'admin') {
				throw new HttpError(403, 'ไม่มีสิทธิ์ในการดำเนินการ');
			}
			const topup = await topupService.createTopup(body);
			return {
				success: true,
				message: 'สร้างคำขอเติมเงินสำเร็จ',
				statusMessage: 'สำเร็จ!',
				timestamp: new Date().toISOString(),
				data: { topup },
			};
		},
		{
			body: createTopupSchema,
		}
	)

	// ดึงรายการคำขอเติมเงิน
	.get(
		'/',
		async ({ query, user }) => {
			if (user?.role !== 'admin') {
				throw new HttpError(403, 'ไม่มีสิทธิ์ในการดำเนินการ');
			}
			const result = await topupService.getTopups(query);
			return {
				success: true,
				message: 'ดึงรายการคำขอเติมเงินสำเร็จ',
				statusMessage: 'สำเร็จ!',
				timestamp: new Date().toISOString(),
				data: result,
			};
		},
		{
			query: topupQuerySchema,
		}
	)

	// ดึงคำขอเติมเงินตาม ID
	.get('/:id', async ({ params, user }) => {
		if (user?.role !== 'admin') {
			throw new HttpError(403, 'ไม่มีสิทธิ์ในการดำเนินการ');
		}
		const topup = await topupService.getTopupById(params.id);
		if (!topup) {
			throw new HttpError(404, 'ไม่พบคำขอเติมเงิน');
		}
		return {
			success: true,
			message: 'ดึงข้อมูลคำขอเติมเงินสำเร็จ',
			statusMessage: 'สำเร็จ!',
			timestamp: new Date().toISOString(),
			data: { topup },
		};
	})

	// อนุมัติ/ปฏิเสธคำขอเติมเงิน
	.patch(
		'/:id/process',
		async ({ params, body, user }) => {
			if (user?.role !== 'admin') {
				throw new HttpError(403, 'ไม่มีสิทธิ์ในการดำเนินการ');
			}
			const topup = await topupService.processTopup(params.id, body.action, user._id);
			const actionText = body.action === 'approve' ? 'อนุมัติ' : 'ปฏิเสธ';
			return {
				success: true,
				message: `${actionText}คำขอเติมเงินสำเร็จ`,
				statusMessage: 'สำเร็จ!',
				timestamp: new Date().toISOString(),
				data: { topup },
			};
		},
		{
			body: processTopupSchema,
		}
	)

	// ดึงประวัติการเติมเงินของ user
	.get('/history/user/:userId', async ({ params, user }) => {
		if (user?.role !== 'admin' && user?._id !== params.userId) {
			throw new HttpError(403, 'ไม่มีสิทธิ์ในการดำเนินการ');
		}
		const history = await topupService.getTopupHistory('user', params.userId);
		return {
			success: true,
			message: 'ดึงประวัติการเติมเงินสำเร็จ',
			statusMessage: 'สำเร็จ!',
			timestamp: new Date().toISOString(),
			data: { history },
		};
	})

	// ดึงประวัติการเติมเงินของ customer
	.get('/history/customer/:customerId', async ({ params, query, user }) => {
		if (user?.role !== 'admin') {
			throw new HttpError(403, 'ไม่มีสิทธิ์ในการดำเนินการ');
		}
		const siteId = query.siteId as string;
		if (!siteId) {
			throw new HttpError(400, 'กรุณาระบุ siteId');
		}
		const history = await topupService.getTopupHistory('customer', params.customerId, siteId);
		return {
			success: true,
			message: 'ดึงประวัติการเติมเงินสำเร็จ',
			statusMessage: 'สำเร็จ!',
			timestamp: new Date().toISOString(),
			data: { history },
		};
	})

	// ดึงสถิติการเติมเงิน
	.get('/stats', async ({ query, user }) => {
		if (user?.role !== 'admin') {
			throw new HttpError(403, 'ไม่มีสิทธิ์ในการดำเนินการ');
		}
		const siteId = query.siteId as string;
		const stats = await topupService.getTopupStats(siteId);
		return {
			success: true,
			message: 'ดึงสถิติการเติมเงินสำเร็จ',
			statusMessage: 'สำเร็จ!',
			timestamp: new Date().toISOString(),
			data: { stats },
		};
	});
