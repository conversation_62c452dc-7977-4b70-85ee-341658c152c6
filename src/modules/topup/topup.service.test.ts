import { describe, it, expect, beforeEach, afterEach } from 'bun:test';
import { TopupService } from './topup.service';
import { Topup } from './topup.model';
import { User } from '@/modules/user/user.model';
import { Customer } from '@/modules/customer/customer.model';

describe('TopupService', () => {
	let topupService: TopupService;
	let testUser: any;
	let testCustomer: any;

	beforeEach(async () => {
		topupService = new TopupService();

		// สร้าง test user
		testUser = new User({
			email: '<EMAIL>',
			password: 'password123',
			role: 'user',
			moneyPoint: 100,
			goldPoint: 50,
		});
		await testUser.save();

		// สร้าง test customer
		testCustomer = new Customer({
			firstName: 'Test',
			lastName: 'Customer',
			siteId: 'test-site',
			email: '<EMAIL>',
			password: 'password123',
			moneyPoint: 200,
			goldPoint: 100,
		});
		await testCustomer.save();
	});

	afterEach(async () => {
		// ลบข้อมูลทดสอบ
		await Topup.deleteMany({});
		await User.deleteMany({});
		await Customer.deleteMany({});
	});

	describe('createTopup', () => {
		it('ควรสร้างคำขอเติมเงินสำหรับ user ได้', async () => {
			const topupData = {
				targetType: 'user' as const,
				targetId: testUser._id,
				pointType: 'moneyPoint' as const,
				amount: 500,
				description: 'เติมเงินทดสอบ',
			};

			const topup = await topupService.createTopup(topupData);

			expect(topup.targetType).toBe('user');
			expect(topup.targetId).toBe(testUser._id);
			expect(topup.pointType).toBe('moneyPoint');
			expect(topup.amount).toBe(500);
			expect(topup.status).toBe('pending');
		});

		it('ควรสร้างคำขอเติมเงินสำหรับ customer ได้', async () => {
			const topupData = {
				targetType: 'customer' as const,
				targetId: testCustomer._id,
				pointType: 'goldPoint' as const,
				amount: 300,
				siteId: 'test-site',
			};

			const topup = await topupService.createTopup(topupData);

			expect(topup.targetType).toBe('customer');
			expect(topup.targetId).toBe(testCustomer._id);
			expect(topup.pointType).toBe('goldPoint');
			expect(topup.amount).toBe(300);
			expect(topup.siteId).toBe('test-site');
			expect(topup.status).toBe('pending');
		});

		it('ควร throw error เมื่อไม่พบ user', async () => {
			const topupData = {
				targetType: 'user' as const,
				targetId: 'invalid-user-id',
				pointType: 'moneyPoint' as const,
				amount: 500,
			};

			await expect(topupService.createTopup(topupData)).rejects.toThrow('ไม่พบผู้ใช้ที่ระบุ');
		});

		it('ควร throw error เมื่อไม่ระบุ siteId สำหรับ customer', async () => {
			const topupData = {
				targetType: 'customer' as const,
				targetId: testCustomer._id,
				pointType: 'goldPoint' as const,
				amount: 300,
			};

			await expect(topupService.createTopup(topupData)).rejects.toThrow(
				'กรุณาระบุ siteId สำหรับการเติมเงินให้ customer'
			);
		});
	});

	describe('processTopup', () => {
		it('ควรอนุมัติและเติมเงินให้ user ได้', async () => {
			// สร้างคำขอเติมเงิน
			const topup = await topupService.createTopup({
				targetType: 'user',
				targetId: testUser._id,
				pointType: 'moneyPoint',
				amount: 500,
			});

			// อนุมัติคำขอ
			const processedTopup = await topupService.processTopup(topup._id, 'approve', 'admin-id');

			expect(processedTopup.status).toBe('completed');
			expect(processedTopup.processedBy).toBe('admin-id');
			expect(processedTopup.processedAt).toBeDefined();

			// ตรวจสอบว่าเงินถูกเติมแล้ว
			const updatedUser = await User.findById(testUser._id);
			expect(updatedUser?.moneyPoint).toBe(600); // 100 + 500
		});

		it('ควรปฏิเสธคำขอเติมเงินได้', async () => {
			const topup = await topupService.createTopup({
				targetType: 'user',
				targetId: testUser._id,
				pointType: 'moneyPoint',
				amount: 500,
			});

			const processedTopup = await topupService.processTopup(topup._id, 'reject', 'admin-id');

			expect(processedTopup.status).toBe('cancelled');
			expect(processedTopup.processedBy).toBe('admin-id');

			// ตรวจสอบว่าเงินไม่ถูกเติม
			const updatedUser = await User.findById(testUser._id);
			expect(updatedUser?.moneyPoint).toBe(100); // ยังคงเท่าเดิม
		});

		it('ควร throw error เมื่อคำขอถูกดำเนินการแล้ว', async () => {
			const topup = await topupService.createTopup({
				targetType: 'user',
				targetId: testUser._id,
				pointType: 'moneyPoint',
				amount: 500,
			});

			// อนุมัติครั้งแรก
			await topupService.processTopup(topup._id, 'approve', 'admin-id');

			// พยายามอนุมัติอีกครั้ง
			await expect(topupService.processTopup(topup._id, 'approve', 'admin-id')).rejects.toThrow(
				'คำขอเติมเงินนี้ได้รับการดำเนินการแล้ว'
			);
		});
	});

	describe('getTopups', () => {
		it('ควรดึงรายการคำขอเติมเงินได้', async () => {
			// สร้างคำขอเติมเงินหลายรายการ
			await topupService.createTopup({
				targetType: 'user',
				targetId: testUser._id,
				pointType: 'moneyPoint',
				amount: 500,
			});

			await topupService.createTopup({
				targetType: 'customer',
				targetId: testCustomer._id,
				pointType: 'goldPoint',
				amount: 300,
				siteId: 'test-site',
			});

			const result = await topupService.getTopups({});

			expect(result.topups).toHaveLength(2);
			expect(result.total).toBe(2);
			expect(result.page).toBe(1);
		});

		it('ควรกรองรายการตาม targetType ได้', async () => {
			await topupService.createTopup({
				targetType: 'user',
				targetId: testUser._id,
				pointType: 'moneyPoint',
				amount: 500,
			});

			await topupService.createTopup({
				targetType: 'customer',
				targetId: testCustomer._id,
				pointType: 'goldPoint',
				amount: 300,
				siteId: 'test-site',
			});

			const result = await topupService.getTopups({ targetType: 'user' });

			expect(result.topups).toHaveLength(1);
			expect(result.topups[0].targetType).toBe('user');
		});
	});

	describe('getTopupStats', () => {
		it('ควรคำนวณสถิติได้ถูกต้อง', async () => {
			// สร้างและอนุมัติคำขอเติมเงิน
			const topup1 = await topupService.createTopup({
				targetType: 'user',
				targetId: testUser._id,
				pointType: 'moneyPoint',
				amount: 500,
			});

			const topup2 = await topupService.createTopup({
				targetType: 'user',
				targetId: testUser._id,
				pointType: 'goldPoint',
				amount: 200,
			});

			await topupService.processTopup(topup1._id, 'approve', 'admin-id');
			// topup2 ยังคง pending

			const stats = await topupService.getTopupStats();

			expect(stats.totalTopups).toBe(2);
			expect(stats.pendingTopups).toBe(1);
			expect(stats.completedTopups).toBe(1);
			expect(stats.totalMoneyPoints).toBe(500);
			expect(stats.totalGoldPoints).toBe(0); // เพราะยัง pending
		});
	});
});
