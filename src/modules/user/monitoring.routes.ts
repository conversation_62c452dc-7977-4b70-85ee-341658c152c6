import { Elysia } from 'elysia';
import { HttpError } from '@/core/utils/error';
import { userAuthPlugin } from '@/core/plugins/auth';
import { rateLimitService } from '@/core/services/rate-limit.service';
import { monitoringService } from '@/core/services/monitoring.service';
import { getTokenStoreDebugInfo, getUserTokenStats } from '@/core/utils/token-rotation';

export const monitoringRoutes = new Elysia({ prefix: '/monitoring' })
	// Routes ที่ต้องการ authentication
	.use(userAuthPlugin)

	// ✅ Get monitoring statistics (Admin only)
	.get('/stats', async ({ user }) => {
		try {
			// Check if user is admin
			if (user.role !== 'admin') {
				throw new HttpError(403, 'ไม่มีสิทธิ์เข้าถึง');
			}

			const monitoringStats = monitoringService.getMonitoringStats();
			const rateLimitStats = rateLimitService.getRateLimitStats();
			const tokenStoreInfo = await getTokenStoreDebugInfo();

			return {
				success: true,
				message: 'ดึงข้อมูล monitoring สำเร็จ',
				data: {
					monitoring: monitoringStats,
					rateLimit: rateLimitStats,
					tokenStore: tokenStoreInfo,
				},
			};
		} catch (error) {
			console.error('[monitoring/stats] Error:', error);
			if (error instanceof HttpError) throw error;
			throw new HttpError(500, 'เกิดข้อผิดพลาดในการดึงข้อมูล monitoring');
		}
	})

	// ✅ Get user activity summary
	.get('/user-activity/:userId', async ({ params, user }) => {
		try {
			const { userId } = params;

			// Check if user is admin or requesting their own data
			if (user.role !== 'admin' && user._id !== userId) {
				throw new HttpError(403, 'ไม่มีสิทธิ์เข้าถึง');
			}

			const activitySummary = monitoringService.getUserActivitySummary(userId, 24);
			const tokenStats = await getUserTokenStats(userId);

			return {
				success: true,
				message: 'ดึงข้อมูล user activity สำเร็จ',
				data: {
					activitySummary,
					tokenStats,
				},
			};
		} catch (error) {
			console.error('[monitoring/user-activity] Error:', error);
			if (error instanceof HttpError) throw error;
			throw new HttpError(500, 'เกิดข้อผิดพลาดในการดึงข้อมูล user activity');
		}
	})

	// ✅ Get rate limit statistics
	.get('/rate-limits', async ({ user }) => {
		try {
			// Check if user is admin
			if (user.role !== 'admin') {
				throw new HttpError(403, 'ไม่มีสิทธิ์เข้าถึง');
			}

			const rateLimitStats = rateLimitService.getRateLimitStats();

			return {
				success: true,
				message: 'ดึงข้อมูล rate limit สำเร็จ',
				data: rateLimitStats,
			};
		} catch (error) {
			console.error('[monitoring/rate-limits] Error:', error);
			if (error instanceof HttpError) throw error;
			throw new HttpError(500, 'เกิดข้อผิดพลาดในการดึงข้อมูล rate limit');
		}
	})

	// ✅ Reset rate limit for specific key (Admin only)
	.post('/reset-rate-limit', async ({ body, user }) => {
		try {
			// Check if user is admin
			if (user.role !== 'admin') {
				throw new HttpError(403, 'ไม่มีสิทธิ์เข้าถึง');
			}

			const { key } = body as { key: string };

			if (!key) {
				throw new HttpError(400, 'ต้องระบุ key สำหรับ reset rate limit');
			}

			rateLimitService.resetRateLimit(key);

			return {
				success: true,
				message: 'Reset rate limit สำเร็จ',
				data: { key },
			};
		} catch (error) {
			console.error('[monitoring/reset-rate-limit] Error:', error);
			if (error instanceof HttpError) throw error;
			throw new HttpError(500, 'เกิดข้อผิดพลาดในการ reset rate limit');
		}
	})

	// ✅ Export monitoring data (Admin only)
	.get('/export', async ({ user }) => {
		try {
			// Check if user is admin
			if (user.role !== 'admin') {
				throw new HttpError(403, 'ไม่มีสิทธิ์เข้าถึง');
			}

			const exportData = monitoringService.exportMonitoringData();

			return {
				success: true,
				message: 'Export monitoring data สำเร็จ',
				data: exportData,
			};
		} catch (error) {
			console.error('[monitoring/export] Error:', error);
			if (error instanceof HttpError) throw error;
			throw new HttpError(500, 'เกิดข้อผิดพลาดในการ export monitoring data');
		}
	})

	// ✅ Get security events (Admin only)
	.get('/security-events', async ({ user, query }) => {
		try {
			// Check if user is admin
			if (user.role !== 'admin') {
				throw new HttpError(403, 'ไม่มีสิทธิ์เข้าถึง');
			}

			const { severity, hours = '24' } = query as { severity?: string; hours?: string };
			const hoursNum = parseInt(hours);

			const monitoringStats = monitoringService.getMonitoringStats();
			let securityEvents = monitoringStats.recentSecurityEvents;

			// Filter by severity if provided
			if (severity) {
				securityEvents = securityEvents.filter(event => event.severity === severity);
			}

			return {
				success: true,
				message: 'ดึงข้อมูล security events สำเร็จ',
				data: {
					events: securityEvents,
					total: securityEvents.length,
					hours: hoursNum,
				},
			};
		} catch (error) {
			console.error('[monitoring/security-events] Error:', error);
			if (error instanceof HttpError) throw error;
			throw new HttpError(500, 'เกิดข้อผิดพลาดในการดึงข้อมูล security events');
		}
	});
