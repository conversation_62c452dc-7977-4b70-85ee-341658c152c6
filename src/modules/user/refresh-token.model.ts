import mongoose, { Schema, Document } from 'mongoose';

export interface IRefreshToken extends Document {
	tokenId: string;
	userId: string;
	hashedToken: string;
	createdAt: Date;
	lastUsed: Date;
	rotationCount: number;
	isRevoked: boolean;
	revokedAt?: Date;
	deviceFingerprint?: string;
	userAgent?: string;
	ipAddress?: string;
	expiresAt: Date;
}

const refreshTokenSchema = new Schema<IRefreshToken>(
	{
		tokenId: {
			type: String,
			required: true,
			unique: true,
			index: true,
		},
		userId: {
			type: String,
			ref: 'User',
			required: true,
			index: true,
		},
		hashedToken: {
			type: String,
			required: true,
			index: true,
		},
		createdAt: {
			type: Date,
			default: Date.now,
			index: true,
		},
		lastUsed: {
			type: Date,
			default: Date.now,
			index: true,
		},
		rotationCount: {
			type: Number,
			default: 0,
			min: 0,
		},
		isRevoked: {
			type: Boolean,
			default: false,
			index: true,
		},
		revokedAt: {
			type: Date,
			index: true,
		},
		deviceFingerprint: {
			type: String,
			index: true,
		},
		userAgent: String,
		ipAddress: String,
		expiresAt: {
			type: Date,
			required: true,
			index: true,
		},
	},
	{
		timestamps: true,
	}
);

// Indexes for performance
// refreshTokenSchema.index({ userId: 1, isRevoked: 1 });
// refreshTokenSchema.index({ hashedToken: 1, isRevoked: 1 });
// refreshTokenSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 }); // TTL index
// refreshTokenSchema.index({ createdAt: 1 });
// refreshTokenSchema.index({ lastUsed: 1 });

// Compound indexes for common queries
// refreshTokenSchema.index({ userId: 1, deviceFingerprint: 1 });
// refreshTokenSchema.index({ isRevoked: 1, revokedAt: 1 });

export const RefreshToken = mongoose.model<IRefreshToken>('RefreshToken', refreshTokenSchema);
