import mongoose, { Schema, type Document } from 'mongoose';
import { generateFileId } from '@/core/utils/idGenerator';

export type SiteRole = 'owner' | 'admin' | 'editor' | 'viewer';

export interface IRole extends Document {
	_id: string;
	siteId: string;
	userId: string;
	role: SiteRole;
	createdAt: Date;
	updatedAt: Date;
}

const roleSchema = new Schema<IRole>(
	{
		_id: { type: String, default: () => generateFileId(5) },
		siteId: { type: String, required: true, index: true },
		userId: { type: String, required: true, index: true },
		role: {
			type: String,
			enum: ['owner', 'admin', 'editor', 'viewer'],
			required: true,
		},
	},
	{
		timestamps: true,
		id: false,
		versionKey: false,
		toJSON: {
			virtuals: true,
		},
		toObject: {
			virtuals: true,
		},
	}
);

roleSchema.virtual('userInfo', {
	ref: 'User',
	localField: 'userId',
	foreignField: '_id',
	justOne: true,
});

roleSchema.index({ siteId: 1, userId: 1 }, { unique: true });

export const Role = mongoose.model<IRole>('Role', roleSchema);
