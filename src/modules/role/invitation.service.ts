import { Invitation } from './invitation.model';
import { Role } from './role.model';
import { HttpError } from './role.service';
import { User } from '@/modules/user/user.model';

// สร้างคำเชิญ
export async function createInvitation(
	siteId: string,
	fromUserId: string,
	toUserId: string,
	role: SiteRole,
	message?: string,
	toEmail?: string
): Promise<any> {
	// ตรวจสอบว่าผู้ส่งมีสิทธิ์เชิญหรือไม่
	const senderRole = await Role.findOne({ siteId, userId: fromUserId });
	if (!senderRole || (senderRole.role !== 'owner' && senderRole.role !== 'admin')) {
		throw new HttpError(403, 'คุณไม่มีสิทธิ์ส่งคำเชิญ');
	}

	// ตรวจสอบว่าผู้รับมี role ในเว็บไซต์นี้แล้วหรือไม่
	const existingRole = await Role.findOne({ siteId, userId: toUserId });
	if (existingRole) {
		throw new HttpError(409, 'ผู้ใช้มีบทบาทในเว็บไซต์นี้แล้ว');
	}

	// ตรวจสอบว่ามีคำเชิญที่รอดำเนินการอยู่หรือไม่
	const existingInvitation = await Invitation.findOne({
		siteId,
		toUserId,
		status: 'pending',
		expiresAt: { $gt: new Date() },
	});
	if (existingInvitation) {
		throw new HttpError(409, 'มีคำเชิญที่รอดำเนินการอยู่แล้ว');
	}

	// ตรวจสอบว่า toUserId มีอยู่จริง
	if (toUserId) {
		const targetUser = await User.findById(toUserId);
		if (!targetUser) {
			throw new HttpError(404, 'ไม่พบผู้ใช้ที่ต้องการเชิญ');
		}
	}

	const invitation = new Invitation({
		siteId,
		fromUserId,
		toUserId,
		toEmail,
		role,
		message,
		status: 'pending',
	});

	await invitation.save();

	// TODO: ส่งอีเมลแจ้งเตือน
	// await sendInvitationEmail(invitation);

	return {
		success: true,
		message: 'ส่งคำเชิญสำเร็จ',
		statusMessage: 'สำเร็จ!',
		timestamp: new Date().toISOString(),
		data: invitation,
	};
}

// รับคำเชิญ
export async function acceptInvitation(invitationId: string, userId: string): Promise<any> {
	const invitation = await Invitation.findById(invitationId);

	if (!invitation) {
		throw new HttpError(404, 'ไม่พบคำเชิญ');
	}

	if (invitation.toUserId !== userId) {
		throw new HttpError(403, 'คำเชิญนี้ไม่ใช่ของคุณ');
	}

	if (invitation.status !== 'pending') {
		throw new HttpError(400, 'คำเชิญนี้ได้ดำเนินการแล้ว');
	}

	if (invitation.expiresAt < new Date()) {
		invitation.status = 'expired';
		await invitation.save();
		throw new HttpError(400, 'คำเชิญหมดอายุแล้ว');
	}

	// ตรวจสอบว่าผู้ใช้มี role ในเว็บไซต์นี้แล้วหรือไม่
	const existingRole = await Role.findOne({ siteId: invitation.siteId, userId });
	if (existingRole) {
		throw new HttpError(409, 'คุณมีบทบาทในเว็บไซต์นี้แล้ว');
	}

	// สร้าง role ใหม่
	const newRole = new Role({
		siteId: invitation.siteId,
		userId,
		role: invitation.role,
	});

	await newRole.save();

	// อัพเดตสถานะคำเชิญ
	invitation.status = 'accepted';
	await invitation.save();

	return {
		success: true,
		message: 'รับคำเชิญสำเร็จ',
		statusMessage: 'สำเร็จ!',
		timestamp: new Date().toISOString(),
		data: { invitation, role: newRole },
	};
}

// ปฏิเสธคำเชิญ
export async function rejectInvitation(invitationId: string, userId: string): Promise<any> {
	const invitation = await Invitation.findById(invitationId);

	if (!invitation) {
		throw new HttpError(404, 'ไม่พบคำเชิญ');
	}

	if (invitation.toUserId !== userId) {
		throw new HttpError(403, 'คำเชิญนี้ไม่ใช่ของคุณ');
	}

	if (invitation.status !== 'pending') {
		throw new HttpError(400, 'คำเชิญนี้ได้ดำเนินการแล้ว');
	}

	invitation.status = 'rejected';
	await invitation.save();

	return {
		success: true,
		message: 'ปฏิเสธคำเชิญสำเร็จ',
		statusMessage: 'สำเร็จ!',
		timestamp: new Date().toISOString(),
		data: invitation,
	};
}

// ดูคำเชิญที่ส่งออกไป
export async function getSentInvitations(siteId: string, fromUserId: string, query: any = {}): Promise<any> {
	const { status, role, page = 1, limit = 20 } = query;
	const skip = (page - 1) * limit;

	const filter: any = { siteId, fromUserId };
	if (status) filter.status = status;
	if (role) filter.role = role;

	const invitations = await Invitation.find(filter)
		.populate('toUserId', 'email avatar')
		.sort({ createdAt: -1 })
		.skip(skip)
		.limit(limit);

	const total = await Invitation.countDocuments(filter);

	return {
		success: true,
		message: 'ดึงคำเชิญที่ส่งสำเร็จ',
		statusMessage: 'สำเร็จ!',
		timestamp: new Date().toISOString(),
		data: {
			invitations,
			pagination: {
				page: Number(page),
				limit: Number(limit),
				total,
				pages: Math.ceil(total / limit),
			},
		},
	};
}

// ดูคำเชิญที่ได้รับ
export async function getReceivedInvitations(userId: string, query: any = {}): Promise<any> {
	const { status = 'pending', role, page = 1, limit = 20 } = query;
	const skip = (page - 1) * limit;

	const filter: any = {
		toUserId: userId,
		status,
		expiresAt: { $gt: new Date() },
	};
	if (role) filter.role = role;

	const invitations = await Invitation.find(filter)
		.populate('fromUserId', 'email avatar')
		.populate({
			path: 'siteId',
			select: 'name description logo',
			model: 'Site',
		})
		.sort({ createdAt: -1 })
		.skip(skip)
		.limit(limit);

	const total = await Invitation.countDocuments(filter);

	return {
		success: true,
		message: 'ดึงคำเชิญที่ได้รับสำเร็จ',
		statusMessage: 'สำเร็จ!',
		timestamp: new Date().toISOString(),
		data: {
			invitations,
			pagination: {
				page: Number(page),
				limit: Number(limit),
				total,
				pages: Math.ceil(total / limit),
			},
		},
	};
}

// ยกเลิกคำเชิญ
export async function cancelInvitation(invitationId: string, fromUserId: string): Promise<any> {
	const invitation = await Invitation.findById(invitationId);

	if (!invitation) {
		throw new HttpError(404, 'ไม่พบคำเชิญ');
	}

	if (invitation.fromUserId !== fromUserId) {
		throw new HttpError(403, 'คุณไม่ใช่ผู้ส่งคำเชิญนี้');
	}

	if (invitation.status !== 'pending') {
		throw new HttpError(400, 'ไม่สามารถยกเลิกคำเชิญที่ดำเนินการแล้ว');
	}

	await invitation.deleteOne();

	return {
		success: true,
		message: 'ยกเลิกคำเชิญสำเร็จ',
		statusMessage: 'สำเร็จ!',
		timestamp: new Date().toISOString(),
	};
}
// ดูคำเชิญเฉพาะ
export async function getInvitationById(invitationId: string, userId: string): Promise<any> {
	const invitation = await Invitation.findById(invitationId)
		.populate('fromUserId', 'email avatar')
		.populate('toUserId', 'email avatar')
		.populate({
			path: 'siteId',
			select: 'name description logo',
			model: 'Site',
		});

	if (!invitation) {
		throw new HttpError(404, 'ไม่พบคำเชิญ');
	}

	// ตรวจสอบสิทธิ์ (ต้องเป็นผู้ส่งหรือผู้รับ)
	if (invitation.fromUserId !== userId && invitation.toUserId !== userId) {
		throw new HttpError(403, 'คุณไม่มีสิทธิ์ดูคำเชิญนี้');
	}

	return {
		success: true,
		message: 'ดึงข้อมูลคำเชิญสำเร็จ',
		statusMessage: 'สำเร็จ!',
		timestamp: new Date().toISOString(),
		data: invitation,
	};
}

// สร้างคำเชิญหลายรายการ
export async function createBulkInvitations(
	siteId: string,
	fromUserId: string,
	invitations: Array<{
		toUserId?: string;
		toEmail?: string;
		role: SiteRole;
		message?: string;
	}>
): Promise<any> {
	// ตรวจสอบสิทธิ์
	const senderRole = await Role.findOne({ siteId, userId: fromUserId });
	if (!senderRole || (senderRole.role !== 'owner' && senderRole.role !== 'admin')) {
		throw new HttpError(403, 'คุณไม่มีสิทธิ์ส่งคำเชิญ');
	}

	const results = [];
	const errors = [];

	for (let i = 0; i < invitations.length; i++) {
		const invitationData = invitations[i];

		try {
			// ตรวจสอบว่าผู้รับมี role ในเว็บไซต์นี้แล้วหรือไม่
			if (invitationData.toUserId) {
				const existingRole = await Role.findOne({ siteId, userId: invitationData.toUserId });
				if (existingRole) {
					errors.push({
						index: i,
						error: 'ผู้ใช้มีบทบาทในเว็บไซต์นี้แล้ว',
						data: invitationData,
					});
					continue;
				}

				// ตรวจสอบคำเชิญที่รอดำเนินการ
				const existingInvitation = await Invitation.findOne({
					siteId,
					toUserId: invitationData.toUserId,
					status: 'pending',
					expiresAt: { $gt: new Date() },
				});
				if (existingInvitation) {
					errors.push({
						index: i,
						error: 'มีคำเชิญที่รอดำเนินการอยู่แล้ว',
						data: invitationData,
					});
					continue;
				}
			}

			const invitation = new Invitation({
				siteId,
				fromUserId,
				toUserId: invitationData.toUserId,
				toEmail: invitationData.toEmail,
				role: invitationData.role,
				message: invitationData.message,
				status: 'pending',
			});

			await invitation.save();
			results.push(invitation);

			// TODO: ส่งอีเมลแจ้งเตือน
			// await sendInvitationEmail(invitation);
		} catch (error: any) {
			errors.push({
				index: i,
				error: error.message,
				data: invitationData,
			});
		}
	}

	return {
		success: true,
		message: `ส่งคำเชิญสำเร็จ ${results.length} รายการ${errors.length > 0 ? `, ล้มเหลว ${errors.length} รายการ` : ''}`,
		statusMessage: 'สำเร็จ!',
		timestamp: new Date().toISOString(),
		data: {
			successful: results,
			failed: errors,
			summary: {
				total: invitations.length,
				successful: results.length,
				failed: errors.length,
			},
		},
	};
}

// ส่งคำเชิญใหม่
export async function resendInvitation(invitationId: string, fromUserId: string): Promise<any> {
	const invitation = await Invitation.findById(invitationId);

	if (!invitation) {
		throw new HttpError(404, 'ไม่พบคำเชิญ');
	}

	if (invitation.fromUserId !== fromUserId) {
		throw new HttpError(403, 'คุณไม่ใช่ผู้ส่งคำเชิญนี้');
	}

	if (invitation.status !== 'pending') {
		throw new HttpError(400, 'ไม่สามารถส่งคำเชิญที่ดำเนินการแล้วใหม่ได้');
	}

	// อัปเดตวันหมดอายุ
	invitation.expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 วันใหม่
	await invitation.save();

	// TODO: ส่งอีเมลแจ้งเตือนใหม่
	// await sendInvitationEmail(invitation);

	return {
		success: true,
		message: 'ส่งคำเชิญใหม่สำเร็จ',
		statusMessage: 'สำเร็จ!',
		timestamp: new Date().toISOString(),
		data: invitation,
	};
}

// ฟังก์ชันสำหรับทำความสะอาดคำเชิญที่หมดอายุ
export async function cleanupExpiredInvitations(): Promise<any> {
	const result = await Invitation.updateMany(
		{
			status: 'pending',
			expiresAt: { $lt: new Date() },
		},
		{
			$set: { status: 'expired' },
		}
	);

	return {
		success: true,
		message: `อัปเดตคำเชิญที่หมดอายุ ${result.modifiedCount} รายการ`,
		statusMessage: 'สำเร็จ!',
		timestamp: new Date().toISOString(),
		data: {
			modifiedCount: result.modifiedCount,
		},
	};
}

// ดูสถิติคำเชิญ
export async function getInvitationStats(siteId: string, fromUserId: string): Promise<any> {
	const stats = await Invitation.aggregate([
		{ $match: { siteId, fromUserId } },
		{
			$group: {
				_id: '$status',
				count: { $sum: 1 },
			},
		},
	]);

	const totalInvitations = await Invitation.countDocuments({ siteId, fromUserId });
	const recentInvitations = await Invitation.countDocuments({
		siteId,
		fromUserId,
		createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }, // 30 วันที่แล้ว
	});

	const formattedStats = {
		pending: 0,
		accepted: 0,
		rejected: 0,
		expired: 0,
	};

	stats.forEach(stat => {
		formattedStats[stat._id as keyof typeof formattedStats] = stat.count;
	});

	return {
		success: true,
		message: 'ดึงสถิติคำเชิญสำเร็จ',
		statusMessage: 'สำเร็จ!',
		timestamp: new Date().toISOString(),
		data: {
			byStatus: formattedStats,
			total: totalInvitations,
			recent: recentInvitations,
		},
	};
}
