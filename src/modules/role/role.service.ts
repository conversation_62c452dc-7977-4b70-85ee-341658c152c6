import { createPaginationResult, getPaginationParams } from '@/core/utils/pagination';
import { Role } from './role.model';
export class HttpError extends Error {
	status: number;
	constructor(status: number, message: string) {
		super(message);
		this.status = status;
	}
}

// ✅ เพิ่ม function ชื่อใหม่ที่ชัดเจนขึ้น
export async function addSiteRole(siteId: string, userId: string, role: SiteRole): Promise<IRole> {
	const exist = await Role.findOne({ siteId, userId });
	if (exist) throw new HttpError(409, 'ผู้ใช้นี้มีบทบาทในเว็บไซต์นี้แล้ว');
	const newRole = new Role({ siteId, userId, role });
	await newRole.save();
	return newRole;
}

// ✅ เก็บ function เดิมไว้เพื่อ backward compatibility
export async function addCustomer(siteId: string, userId: string, role: SiteRole): Promise<IRole> {
	return addSiteRole(siteId, userId, role);
}

export async function updateRole(siteId: string, userId: string, role: SiteRole): Promise<IRole> {
	const roleDoc = await Role.findOne({ siteId, userId });
	if (!roleDoc) throw new HttpError(404, 'ไม่พบบทบาท');
	roleDoc.role = role;
	await roleDoc.save();
	return roleDoc;
}

export async function removeCustomer(siteId: string, userId: string): Promise<boolean> {
	const role = await Role.findOne({ siteId, userId });
	if (!role) throw new HttpError(404, 'ไม่พบบทบาท');
	await role.deleteOne();
	return true;
}

export async function getUserRole(siteId: string, userId: string): Promise<SiteRole | null> {
	const role = await Role.findOne({ siteId, userId });
	return role?.role || null;
}

export async function listCustomers(
	siteId: string,
	paginationParams: PaginationParams
): Promise<{ roles: any[]; pagination: PaginationResult }> {
	const { page, limit, skip } = getPaginationParams(paginationParams);
	const roles = await Role.aggregate([
		{ $match: { siteId } },
		{
			$lookup: {
				from: 'users',
				localField: 'userId',
				foreignField: '_id',
				as: 'userInfo',
				pipeline: [
					{
						$project: {
							_id: 1,
							email: 1,
							avatar: 1,
							moneyPoint: 1,
							goldPoint: 1,
							createdAt: 1,
							updatedAt: 1,
						},
					},
				],
			},
		},
		{ $unwind: '$userInfo' },
		{ $project: { _id: 1, role: 1, siteId: 1, userId: 1, userInfo: 1, createdAt: 1, updatedAt: 1 } },
		{ $sort: { 'userInfo.createdAt': -1 } },
		{ $skip: skip },
		{ $limit: limit },
	]);
	console.log('[listCustomers] roles aggregate result:', roles);
	const totalCount = await Role.aggregate([
		{ $match: { siteId } },
		{
			$lookup: {
				from: 'users',
				localField: 'userId',
				foreignField: '_id',
				as: 'userInfo',
			},
		},
		{ $unwind: '$userInfo' },
		{ $count: 'total' },
	]);
	const total = totalCount.length > 0 ? totalCount[0].total : 0;
	const safeRoles = roles.map((role: any) => ({
		_id: role._id,
		role: role.role,
		siteId: role.siteId,
		userInfo: role.userInfo,
		createdAt: role.createdAt,
		updatedAt: role.updatedAt,
	}));
	console.log('[listCustomers] safeRoles:', safeRoles);
	const paginationResult = createPaginationResult(page, limit, total);
	return { roles: safeRoles, pagination: paginationResult };
}

export async function getRoleByUserId(userId: string): Promise<IRole | null> {
	const role = await Role.findOne({ userId });
	return role || null;
}

export async function transferOwner(siteId: string, fromUserId: string, toUserId: string): Promise<boolean> {
	const fromRole = await Role.findOne({ siteId, userId: fromUserId });
	const toRole = await Role.findOne({ siteId, userId: toUserId });
	if (!fromRole || !toRole) throw new HttpError(404, 'ไม่พบบทบาท');
	fromRole.role = 'admin';
	toRole.role = 'owner';
	await fromRole.save();
	await toRole.save();
	return true;
}
