# 📦 Site Package Subscription System

ระบบ subscription สำหรับ **site owner เช่าแพ็คเกจเพิ่มเวลาใช้งาน site** ไม่ใช่สำหรับ customer สมัครสมาชิก

## 🎯 วัตถุประสงค์

- Site owner สามารถเช่าแพ็คเกจเพิ่มเวลาใช้งาน site ได้
- รองรับการต่ออายุอัตโนมัติ (auto-renewal)
- จัดการประวัติการชำระเงินและการต่ออายุ
- ระบบแจ้งเตือนก่อนหมดอายุ
- รองรับรหัสส่วนลด

## 📋 โครงสร้างระบบ

### Models

#### 1. **Subscription** - การสมัครแพ็คเกจของ site

```typescript
interface ISubscription {
	siteId: string; // ไซต์ที่สมัคร
	userId: string; // site owner
	packageType: string; // ประเภทแพ็คเกจ
	status: string; // สถานะ
	autoRenew: boolean; // ต่ออายุอัตโนมัติ
	pricing: object; // ข้อมูลราคา
	billing: object; // ข้อมูลการชำระเงิน
	renewalHistory: array; // ประวัติการต่ออายุ
	stats: object; // สถิติ
}
```

#### 2. **SubscriptionDiscount** - รหัสส่วนลด

```typescript
interface ISubscriptionDiscount {
	code: string; // รหัสส่วนลด
	type: string; // ประเภท (percentage/fixed)
	value: number; // ค่าส่วนลด
	packageTypes: string[]; // แพ็คเกจที่ใช้ได้
	usageLimit: number; // จำกัดการใช้งาน
	validFrom: Date; // วันที่เริ่มใช้
	validTo: Date; // วันที่สิ้นสุด
}
```

#### 3. **SubscriptionNotification** - การแจ้งเตือน

```typescript
interface ISubscriptionNotification {
	siteId: string; // ไซต์
	userId: string; // ผู้รับแจ้งเตือน
	type: string; // ประเภทการแจ้งเตือน
	title: string; // หัวข้อ
	message: string; // ข้อความ
	isRead: boolean; // อ่านแล้วหรือยัง
}
```

### Services

#### 1. **createSiteSubscription** - สร้าง subscription

```typescript
await createSiteSubscription(siteId, userId, packageType, {
	autoRenew: true,
	paymentMethod: 'moneyPoint',
	discountCode: 'SAVE20',
});
```

#### 2. **renewSubscription** - ต่ออายุ subscription

```typescript
await renewSubscription(subscriptionId);
```

#### 3. **toggleAutoRenew** - เปิด/ปิดการต่ออายุอัตโนมัติ

```typescript
await toggleAutoRenew(siteId, subscriptionId, userId, true);
```

#### 4. **getUserSubscriptions** - ดึงรายการ subscription

```typescript
await getUserSubscriptions(userId, {
	page: 1,
	limit: 20,
	status: 'active',
	siteId: 'specific-site',
});
```

## 🚀 การใช้งาน

### 1. สร้าง Subscription ใหม่

```typescript
import { createSiteSubscription } from '@/modules/subscription/subscription.service';

// Site owner เช่าแพ็คเกจรายเดือน
const subscription = await createSiteSubscription(
	'site123', // siteId
	'user456', // userId (site owner)
	'monthly', // packageType
	{
		autoRenew: true,
		paymentMethod: 'moneyPoint',
		discountCode: 'SAVE10',
	}
);
```

### 2. ดึงรายการ Subscription

```typescript
import { getUserSubscriptions } from '@/modules/subscription/subscription.service';

const result = await getUserSubscriptions('user456', {
	page: 1,
	limit: 10,
	status: 'active',
});

console.log(result.subscriptions);
console.log(result.pagination);
```

### 3. จัดการ Auto Renewal

```typescript
import { toggleAutoRenew } from '@/modules/subscription/subscription.service';

// เปิดการต่ออายุอัตโนมัติ
await toggleAutoRenew('site123', 'sub789', 'user456', true);

// ปิดการต่ออายุอัตโนมัติ
await toggleAutoRenew('site123', 'sub789', 'user456', false);
```

## 📡 API Endpoints

### Public Routes

```
GET    /subscription/packages              # ดึงข้อมูลแพ็คเกจ
POST   /subscription/validate-discount     # ตรวจสอบรหัสส่วนลด
```

### User Routes (ต้อง login)

```
GET    /subscription/my-subscriptions      # ดึงรายการ subscription ของ user
GET    /subscription/notifications         # ดึงการแจ้งเตือน
PUT    /subscription/notifications/:id/read # อ่านการแจ้งเตือน
```

### Site Owner Routes (ต้องเป็น owner)

```
POST   /subscription/sites/:siteId/subscribe                    # สร้าง subscription
GET    /subscription/sites/:siteId/subscriptions               # ดึงรายการ subscription ของ site
PUT    /subscription/sites/:siteId/subscriptions/:id/auto-renew # เปิด/ปิด auto renew
PUT    /subscription/sites/:siteId/subscriptions/:id/cancel    # ยกเลิก subscription
POST   /subscription/sites/:siteId/subscriptions/:id/renew     # ต่ออายุแบบ manual
GET    /subscription/sites/:siteId/notifications               # ดึงการแจ้งเตือนของ site
```

## ⚙️ Cron Jobs

### 1. Auto Renewal Process

```typescript
import { processAutoRenewals } from '@/modules/subscription/subscription.cron';

// รันทุกวันเวลา 00:00
await processAutoRenewals();
```

### 2. Expiry Warnings

```typescript
import { sendExpiryWarnings } from '@/modules/subscription/subscription.cron';

// ส่งการแจ้งเตือนก่อนหมดอายุ 7 วัน
await sendExpiryWarnings(7);
```

### 3. Cleanup Expired Subscriptions

```typescript
import { cleanupExpiredSubscriptions } from '@/modules/subscription/subscription.cron';

// ทำความสะอาด subscription ที่หมดอายุ
await cleanupExpiredSubscriptions();
```

## 💰 Package Types

| Package   | Days  | Price (MoneyPoint) | Description       |
| --------- | ----- | ------------------ | ----------------- |
| monthly   | 30    | 99                 | เช่ารายเดือน      |
| yearly    | 365   | 999                | เช่ารายปี         |
| permanent | 36500 | 9999               | เช่าถาวร (100 ปี) |

## 🔔 Notification Types

| Type                    | Description            |
| ----------------------- | ---------------------- |
| `expiry_warning`        | แจ้งเตือนก่อนหมดอายุ   |
| `renewal_success`       | ต่ออายุสำเร็จ          |
| `renewal_failure`       | ต่ออายุล้มเหลว         |
| `auto_renewal_disabled` | ปิดการต่ออายุอัตโนมัติ |
| `payment_failed`        | การชำระเงินล้มเหลว     |

## 🔄 Integration กับ Site System

### 1. เมื่อสร้าง Site ใหม่

```typescript
// ใน site.service.ts
import { createSiteSubscription } from '@/modules/subscription/subscription.service';

export async function createSite(data) {
	// สร้าง site
	const site = await Site.create(data);

	// สร้าง subscription
	const subscription = await createSiteSubscription(site._id, data.userId, data.packageType, {
		paymentMethod: 'moneyPoint',
	});

	return { site, subscription };
}
```

### 2. ตรวจสอบสถานะ Site

```typescript
// ใน site middleware
import { Subscription } from '@/modules/subscription/subscription.model';

export async function checkSiteStatus(siteId) {
	const activeSubscription = await Subscription.findOne({
		siteId,
		status: 'active',
	});

	return {
		hasActiveSubscription: !!activeSubscription,
		subscription: activeSubscription,
	};
}
```

## 🛡️ Security & Validation

### 1. Site Owner Validation

- ตรวจสอบว่า user เป็น owner ของ site
- ใช้ `requireSiteOwner` middleware

### 2. Payment Validation

- ตรวจสอบยอดเงิน moneyPoint
- บันทึกประวัติการชำระเงิน

### 3. Discount Code Validation

- ตรวจสอบความถูกต้องของรหัส
- ตรวจสอบวันหมดอายุและจำนวนการใช้งาน

## 📊 Statistics & Analytics

### Subscription Stats

```typescript
interface SubscriptionStats {
	totalRenewals: number; // จำนวนครั้งที่ต่ออายุ
	totalSpent: number; // ยอดเงินที่ใช้ทั้งหมด
	averageRenewalAmount: number; // ยอดเงินเฉลี่ยต่อครั้ง
	longestActiveStreak: number; // วันที่ active ต่อเนื่องนานที่สุด
	currentActiveStreak: number; // วันที่ active ต่อเนื่องปัจจุบัน
}
```

## 🔧 Configuration

### Environment Variables

```env
# Subscription settings
SUBSCRIPTION_AUTO_RENEW_ENABLED=true
SUBSCRIPTION_EXPIRY_WARNING_DAYS=7,3,1
SUBSCRIPTION_CLEANUP_ENABLED=true

# Cron job settings
SUBSCRIPTION_CRON_AUTO_RENEW="0 0 * * *"      # ทุกวันเวลา 00:00
SUBSCRIPTION_CRON_WARNINGS="0 9 * * *"        # ทุกวันเวลา 09:00
SUBSCRIPTION_CRON_CLEANUP="0 2 * * *"         # ทุกวันเวลา 02:00
```

## 🚨 Error Handling

### Common Errors

- `INSUFFICIENT_FUNDS` - ยอดเงินไม่เพียงพอ
- `SITE_NOT_FOUND` - ไม่พบไซต์
- `SUBSCRIPTION_NOT_FOUND` - ไม่พบ subscription
- `INVALID_PACKAGE_TYPE` - ประเภทแพ็คเกจไม่ถูกต้อง
- `DISCOUNT_CODE_INVALID` - รหัสส่วนลดไม่ถูกต้อง
- `AUTO_RENEW_FAILED` - การต่ออายุอัตโนมัติล้มเหลว

### Error Response Format

```json
{
	"success": false,
	"message": "ยอดเงินไม่เพียงพอ",
	"statusMessage": "ล้มเหลว!",
	"timestamp": "2024-01-01T00:00:00.000Z",
	"error": {
		"code": "INSUFFICIENT_FUNDS",
		"details": {
			"required": 99,
			"available": 50
		}
	}
}
```

## 📝 TODO

- [ ] เพิ่มระบบ webhook สำหรับการชำระเงินภายนอก
- [ ] เพิ่มระบบ analytics และ reporting
- [ ] เพิ่มการส่ง email notification
- [ ] เพิ่มระบบ refund
- [ ] เพิ่มการ export ประวัติการชำระเงิน
- [ ] เพิ่มระบบ bulk operations
- [ ] เพิ่มการ integration กับ payment gateway
