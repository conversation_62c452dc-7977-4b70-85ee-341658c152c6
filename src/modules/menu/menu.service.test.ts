import { describe, it, expect, beforeEach, afterEach } from 'bun:test';
import { MenuService } from './menu.service';
import { MenuItem } from './menu.model';
import { Page } from './page.model';

describe('MenuService', () => {
	let menuService: MenuService;
	const testSiteId = 'test-site-123';

	beforeEach(async () => {
		menuService = new MenuService();
	});

	afterEach(async () => {
		// ลบข้อมูลทดสอบ
		await MenuItem.deleteMany({});
		await Page.deleteMany({});
	});

	describe('Menu Items', () => {
		describe('createMenuItem', () => {
			it('ควรสร้างรายการเมนูได้', async () => {
				const menuData = {
					siteId: testSiteId,
					title: 'หน้าแรก',
					url: '/',
					type: 'internal' as const,
					order: 1,
					isActive: true,
					visibility: 'public' as const,
					showInHeader: true,
					showInFooter: false,
					showInMobile: true,
				};

				const menuItem = await menuService.createMenuItem(menuData);

				expect(menuItem.title).toBe('หน้าแรก');
				expect(menuItem.url).toBe('/');
				expect(menuItem.type).toBe('internal');
				expect(menuItem.siteId).toBe(testSiteId);
				expect(menuItem.isActive).toBe(true);
			});

			it('ควรสร้าง submenu ได้', async () => {
				// สร้างเมนูหลัก
				const parentMenu = await menuService.createMenuItem({
					siteId: testSiteId,
					title: 'สินค้า',
					url: '/products',
					type: 'internal',
					order: 1,
				});

				// สร้าง submenu
				const subMenu = await menuService.createMenuItem({
					siteId: testSiteId,
					title: 'หมวดหมู่ A',
					url: '/products/category-a',
					type: 'internal',
					parentId: parentMenu._id,
					order: 1,
				});

				expect(subMenu.parentId).toBe(parentMenu._id);
				expect(subMenu.title).toBe('หมวดหมู่ A');
			});

			it('ควร throw error เมื่อ parent menu ไม่มีอยู่', async () => {
				const menuData = {
					siteId: testSiteId,
					title: 'Submenu',
					url: '/submenu',
					type: 'internal' as const,
					parentId: 'invalid-parent-id',
				};

				await expect(menuService.createMenuItem(menuData)).rejects.toThrow('ไม่พบเมนูหลักที่ระบุ');
			});
		});

		describe('getMenuTree', () => {
			it('ควรดึงโครงสร้างเมนูแบบ hierarchical ได้', async () => {
				// สร้างเมนูหลัก
				const parentMenu = await menuService.createMenuItem({
					siteId: testSiteId,
					title: 'สินค้า',
					url: '/products',
					type: 'internal',
					order: 1,
					showInHeader: true,
				});

				// สร้าง submenu
				await menuService.createMenuItem({
					siteId: testSiteId,
					title: 'หมวดหมู่ A',
					url: '/products/category-a',
					type: 'internal',
					parentId: parentMenu._id,
					order: 1,
					showInHeader: true,
				});

				const menuTree = await menuService.getMenuTree(testSiteId, 'header');

				expect(menuTree).toHaveLength(1);
				expect(menuTree[0].title).toBe('สินค้า');
				expect(menuTree[0].children).toBeDefined();
			});
		});

		describe('reorderMenuItems', () => {
			it('ควรจัดเรียงลำดับเมนูได้', async () => {
				const menu1 = await menuService.createMenuItem({
					siteId: testSiteId,
					title: 'เมนู 1',
					url: '/menu1',
					type: 'internal',
					order: 1,
				});

				const menu2 = await menuService.createMenuItem({
					siteId: testSiteId,
					title: 'เมนู 2',
					url: '/menu2',
					type: 'internal',
					order: 2,
				});

				// สลับลำดับ
				await menuService.reorderMenuItems([
					{ id: menu1._id, order: 2 },
					{ id: menu2._id, order: 1 },
				]);

				const updatedMenu1 = await MenuItem.findById(menu1._id);
				const updatedMenu2 = await MenuItem.findById(menu2._id);

				expect(updatedMenu1?.order).toBe(2);
				expect(updatedMenu2?.order).toBe(1);
			});
		});
	});

	describe('Pages', () => {
		describe('createPage', () => {
			it('ควรสร้างหน้าเว็บได้', async () => {
				const pageData = {
					siteId: testSiteId,
					title: 'เกี่ยวกับเรา',
					slug: 'about-us',
					content: '<h1>เกี่ยวกับเรา</h1><p>เนื้อหาเกี่ยวกับเรา</p>',
					template: 'about' as const,
					isActive: true,
					isPublic: true,
					visibility: 'public' as const,
				};

				const page = await menuService.createPage(pageData);

				expect(page.title).toBe('เกี่ยวกับเรา');
				expect(page.slug).toBe('about-us');
				expect(page.template).toBe('about');
				expect(page.siteId).toBe(testSiteId);
			});

			it('ควรสร้าง slug อัตโนมัติจากชื่อหน้า', async () => {
				const pageData = {
					siteId: testSiteId,
					title: 'ติดต่อเรา',
					content: '<h1>ติดต่อเรา</h1>',
					template: 'contact' as const,
				};

				const page = await menuService.createPage(pageData);

				expect(page.slug).toBe('ติดต่อเรา'.toLowerCase().replace(/\s+/g, '-'));
			});

			it('ควร throw error เมื่อ slug ซ้ำ', async () => {
				await menuService.createPage({
					siteId: testSiteId,
					title: 'หน้าแรก',
					slug: 'home',
					content: '<h1>หน้าแรก</h1>',
				});

				await expect(
					menuService.createPage({
						siteId: testSiteId,
						title: 'หน้าแรก 2',
						slug: 'home',
						content: '<h1>หน้าแรก 2</h1>',
					})
				).rejects.toThrow('Slug นี้ถูกใช้แล้ว กรุณาเลือก slug อื่น');
			});
		});

		describe('getPageBySlug', () => {
			it('ควรดึงหน้าเว็บตาม slug ได้', async () => {
				const createdPage = await menuService.createPage({
					siteId: testSiteId,
					title: 'ทดสอบ',
					slug: 'test-page',
					content: '<h1>ทดสอบ</h1>',
					isActive: true,
					isPublic: true,
				});

				const page = await menuService.getPageBySlug(testSiteId, 'test-page');

				expect(page).toBeDefined();
				expect(page?.title).toBe('ทดสอบ');
				expect(page?.slug).toBe('test-page');
			});

			it('ควรเพิ่มจำนวนการดูเมื่อเข้าชมหน้า', async () => {
				await menuService.createPage({
					siteId: testSiteId,
					title: 'ทดสอบ View Count',
					slug: 'test-view-count',
					content: '<h1>ทดสอบ</h1>',
					isActive: true,
					isPublic: true,
				});

				// เข้าชมหน้าครั้งแรก
				await menuService.getPageBySlug(testSiteId, 'test-view-count');

				// ตรวจสอบจำนวนการดู
				const page = await Page.findOne({ siteId: testSiteId, slug: 'test-view-count' });
				expect(page?.viewCount).toBe(1);
			});
		});

		describe('generateSitemap', () => {
			it('ควรสร้าง sitemap ได้', async () => {
				await menuService.createPage({
					siteId: testSiteId,
					title: 'หน้าแรก',
					slug: 'home',
					content: '<h1>หน้าแรก</h1>',
					isActive: true,
					isPublic: true,
					showInSitemap: true,
				});

				await menuService.createPage({
					siteId: testSiteId,
					title: 'เกี่ยวกับเรา',
					slug: 'about',
					content: '<h1>เกี่ยวกับเรา</h1>',
					isActive: true,
					isPublic: true,
					showInSitemap: true,
				});

				// หน้าที่ไม่แสดงใน sitemap
				await menuService.createPage({
					siteId: testSiteId,
					title: 'หน้าส่วนตัว',
					slug: 'private',
					content: '<h1>หน้าส่วนตัว</h1>',
					isActive: true,
					isPublic: true,
					showInSitemap: false,
				});

				const sitemap = await menuService.generateSitemap(testSiteId);

				expect(sitemap).toHaveLength(2);
				expect(sitemap.find(p => p.slug === 'home')).toBeDefined();
				expect(sitemap.find(p => p.slug === 'about')).toBeDefined();
				expect(sitemap.find(p => p.slug === 'private')).toBeUndefined();
			});
		});
	});
});
