import { Elysia, t } from 'elysia';
import * as affiliateService from './affiliate.service';
import { userPlugin } from '@/core/middleware/checkUser';

export const affiliateRoutes = new Elysia({ prefix: '/affiliate' })
	.use(userPlugin)
	.get(
		'/',
		async ({ query, store }: any) => {
			const { page = 1, limit = 10, _status } = query;

			const result = await affiliateService.getAllAffiliates(store.siteId, Number(page), Number(limit));

			return {
				success: true,
				data: result,
			};
		},
		{
			query: t.Object({
				page: t.Optional(t.String()),
				limit: t.Optional(t.String()),
				status: t.Optional(t.Union([t.Literal('active'), t.Literal('inactive'), t.Literal('suspended')])),
			}),
		}
	)
	.get(
		'/:affiliateId',
		async ({ params, store }: any) => {
			const { affiliateId } = params;

			const affiliate = await affiliateService.getAffiliate(store.siteId, affiliateId);

			return {
				success: true,
				data: affiliate,
			};
		},
		{
			params: t.Object({
				affiliateId: t.String(),
			}),
		}
	)
	.get(
		'/customer/:customerId',
		async ({ params, store }: any) => {
			const { customerId } = params;

			const affiliate = await affiliateService.getAffiliateByCustomer(store.siteId, customerId);

			return {
				success: true,
				data: affiliate,
			};
		},
		{
			params: t.Object({
				customerId: t.String(),
			}),
		}
	)
	.post(
		'/',
		async ({ body, store }: any) => {
			const { customerId, commission, settings } = body;

			const affiliate = await affiliateService.createAffiliate(store.siteId, customerId, { commission, settings });

			return {
				success: true,
				data: affiliate,
			};
		},
		{
			body: t.Object({
				customerId: t.String(),
				commission: t.Optional(
					t.Object({
						rate: t.Number({ minimum: 0, maximum: 100 }),
						minAmount: t.Optional(t.Number()),
						maxAmount: t.Optional(t.Number()),
					})
				),
				settings: t.Optional(
					t.Object({
						autoApprove: t.Optional(t.Boolean()),
						requireApproval: t.Optional(t.Boolean()),
						minPayout: t.Optional(t.Number()),
						payoutSchedule: t.Optional(t.Union([t.Literal('weekly'), t.Literal('monthly'), t.Literal('quarterly')])),
						paymentMethod: t.Optional(t.Union([t.Literal('bank'), t.Literal('paypal'), t.Literal('crypto')])),
					})
				),
			}),
		}
	)
	.put(
		'/:affiliateId',
		async ({ params, body, store }: any) => {
			const { affiliateId } = params;

			const affiliate = await affiliateService.updateAffiliate(store.siteId, affiliateId, body);

			return {
				success: true,
				data: affiliate,
			};
		},
		{
			params: t.Object({
				affiliateId: t.String(),
			}),
			body: t.Record(t.String(), t.Any()),
		}
	)
	.delete(
		'/:affiliateId',
		async ({ params, store }: any) => {
			const { affiliateId } = params;

			const result = await affiliateService.deleteAffiliate(store.siteId, affiliateId);

			return {
				success: true,
				data: result,
			};
		},
		{
			params: t.Object({
				affiliateId: t.String(),
			}),
		}
	)
	.post(
		'/:affiliateId/products',
		async ({ params, body, store }: any) => {
			const { affiliateId } = params;
			const { productId, commissionRate } = body;

			const affiliate = await affiliateService.addProductToAffiliate(
				store.siteId,
				affiliateId,
				productId,
				commissionRate
			);

			return {
				success: true,
				data: affiliate,
			};
		},
		{
			params: t.Object({
				affiliateId: t.String(),
			}),
			body: t.Object({
				productId: t.String(),
				commissionRate: t.Number({ minimum: 0, maximum: 100 }),
			}),
		}
	)
	.delete(
		'/:affiliateId/products/:productId',
		async ({ params, store }: any) => {
			const { affiliateId, productId } = params;

			const affiliate = await affiliateService.removeProductFromAffiliate(store.siteId, affiliateId, productId);

			return {
				success: true,
				data: affiliate,
			};
		},
		{
			params: t.Object({
				affiliateId: t.String(),
				productId: t.String(),
			}),
		}
	)
	.post(
		'/click',
		async ({ body, store }: any) => {
			const { affiliateCode, productId, trackingData } = body;

			const click = await affiliateService.trackAffiliateClick(store.siteId, affiliateCode, productId, trackingData);

			return {
				success: true,
				data: click,
			};
		},
		{
			body: t.Object({
				affiliateCode: t.String(),
				productId: t.String(),
				trackingData: t.Object({
					customerId: t.Optional(t.String()),
					ipAddress: t.String(),
					userAgent: t.Optional(t.String()),
					referrer: t.Optional(t.String()),
				}),
			}),
		}
	)
	.post(
		'/order',
		async ({ body, store }: any) => {
			const { orderId, affiliateCode, trackingData } = body;

			const affiliateOrders = await affiliateService.processAffiliateOrder(
				store.siteId,
				orderId,
				affiliateCode,
				trackingData
			);

			return {
				success: true,
				data: affiliateOrders,
			};
		},
		{
			body: t.Object({
				orderId: t.String(),
				affiliateCode: t.String(),
				trackingData: t.Object({
					clickTime: t.String(),
					orderTime: t.String(),
					ipAddress: t.Optional(t.String()),
					userAgent: t.Optional(t.String()),
					referrer: t.Optional(t.String()),
				}),
			}),
		}
	)
	.get(
		'/:affiliateId/orders',
		async ({ params, query, store }: any) => {
			const { affiliateId } = params;
			const { page = 1, limit = 10 } = query;

			const result = await affiliateService.getAffiliateOrders(store.siteId, affiliateId, Number(page), Number(limit));

			return {
				success: true,
				data: result,
			};
		},
		{
			params: t.Object({
				affiliateId: t.String(),
			}),
			query: t.Object({
				page: t.Optional(t.String()),
				limit: t.Optional(t.String()),
			}),
		}
	)
	.post(
		'/orders/:orderId/approve',
		async ({ params, store }: any) => {
			const { orderId } = params;

			const affiliateOrder = await affiliateService.approveCommission(store.siteId, orderId);

			return {
				success: true,
				data: affiliateOrder,
			};
		},
		{
			params: t.Object({
				orderId: t.String(),
			}),
		}
	)
	.get(
		'/stats/:affiliateId',
		async ({ params, store }: any) => {
			const { affiliateId } = params;

			const stats = await affiliateService.getAffiliateStats(store.siteId, affiliateId);

			return {
				success: true,
				data: stats,
			};
		},
		{
			params: t.Object({
				affiliateId: t.String(),
			}),
		}
	)
	.get(
		'/top-performers',
		async ({ query, store }: any) => {
			const { limit = 10 } = query;

			const performers = await affiliateService.getTopPerformers(store.siteId, Number(limit));

			return {
				success: true,
				data: performers,
			};
		},
		{
			query: t.Object({
				limit: t.Optional(t.String()),
			}),
		}
	)
	.get(
		'/code/:affiliateCode',
		async ({ params, _store }: any) => {
			const { affiliateCode } = params;

			const affiliate = await affiliateService.getAffiliateByCode(affiliateCode);

			return {
				success: true,
				data: affiliate,
			};
		},
		{
			params: t.Object({
				affiliateCode: t.String(),
			}),
		}
	);
