import { Affiliate, AffiliateOrder, AffiliateClick } from './affiliate.model';
import { Product } from '../product/product.model';
import { Order } from '../order/order.model';
import { Customer } from '@/modules/customer/customer.model';
import { HttpError } from '@/core/utils/error';

// Affiliate Service
export async function createAffiliate(siteId: string, customerId: string, settings: any) {
	try {
		// ตรวจสอบว่าสมาชิกมีอยู่หรือไม่
		const customer = await Customer.findById(customerId);
		if (!customer) {
			throw new HttpError(404, 'ไม่พบสมาชิก');
		}

		// ตรวจสอบว่าสมาชิกมี affiliate อยู่แล้วหรือไม่
		const existingAffiliate = await (Affiliate as any).findByCustomer(siteId, customerId);
		if (existingAffiliate) {
			throw new HttpError(400, 'สมาชิกนี้มี affiliate อยู่แล้ว');
		}

		const affiliate = await Affiliate.create({
			siteId,
			customerId,
			...settings,
		});

		return affiliate;
	} catch (err: any) {
		console.error('Error in createAffiliate:', err);
		if (err instanceof HttpError) throw err;
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะสร้าง affiliate');
	}
}

export async function getAffiliate(siteId: string, affiliateId: string) {
	try {
		const affiliate = await Affiliate.findById(affiliateId);
		if (!affiliate || affiliate.siteId !== siteId) {
			throw new HttpError(404, 'ไม่พบ affiliate');
		}
		return affiliate;
	} catch (err: any) {
		console.error('Error in getAffiliate:', err);
		if (err instanceof HttpError) throw err;
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง affiliate');
	}
}

export async function getAffiliateByCustomer(siteId: string, customerId: string) {
	try {
		const affiliate = await (Affiliate as any).findByCustomer(siteId, customerId);
		if (!affiliate) {
			throw new HttpError(404, 'ไม่พบ affiliate');
		}
		return affiliate;
	} catch (err: any) {
		console.error('Error in getAffiliateByCustomer:', err);
		if (err instanceof HttpError) throw err;
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง affiliate');
	}
}

export async function getAffiliateByCode(affiliateCode: string) {
	try {
		const affiliate = await (Affiliate as any).findByCode(affiliateCode);
		if (!affiliate) {
			throw new HttpError(404, 'ไม่พบ affiliate code');
		}
		return affiliate;
	} catch (err: any) {
		console.error('Error in getAffiliateByCode:', err);
		if (err instanceof HttpError) throw err;
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง affiliate');
	}
}

export async function getAllAffiliates(siteId: string, page: number = 1, limit: number = 10) {
	try {
		const skip = (page - 1) * limit;
		const affiliates = await Affiliate.find({ siteId }).sort({ createdAt: -1 }).skip(skip).limit(limit);

		const total = await Affiliate.countDocuments({ siteId });

		return {
			affiliates,
			pagination: {
				page,
				limit,
				total,
				pages: Math.ceil(total / limit),
			},
		};
	} catch (err: any) {
		console.error('Error in getAllAffiliates:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง affiliates');
	}
}

export async function updateAffiliate(siteId: string, affiliateId: string, updates: any) {
	try {
		const affiliate = await Affiliate.findOneAndUpdate(
			{ _id: affiliateId, siteId },
			{ $set: updates },
			{ new: true, runValidators: true }
		);

		if (!affiliate) {
			throw new HttpError(404, 'ไม่พบ affiliate');
		}

		return affiliate;
	} catch (err: any) {
		console.error('Error in updateAffiliate:', err);
		if (err instanceof HttpError) throw err;
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะอัปเดต affiliate');
	}
}

export async function deleteAffiliate(siteId: string, affiliateId: string) {
	try {
		const affiliate = await Affiliate.findOneAndDelete({ _id: affiliateId, siteId });

		if (!affiliate) {
			throw new HttpError(404, 'ไม่พบ affiliate');
		}

		return { message: 'ลบ affiliate สำเร็จ' };
	} catch (err: any) {
		console.error('Error in deleteAffiliate:', err);
		if (err instanceof HttpError) throw err;
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะลบ affiliate');
	}
}

export async function addProductToAffiliate(
	siteId: string,
	affiliateId: string,
	productId: string,
	commissionRate: number
) {
	try {
		// ตรวจสอบว่าสินค้ามีอยู่หรือไม่
		const product = await Product.findById(productId);
		if (!product || product.siteId !== siteId) {
			throw new HttpError(404, 'ไม่พบสินค้า');
		}

		const affiliate = await Affiliate.findOneAndUpdate(
			{ _id: affiliateId, siteId },
			{
				$push: {
					products: {
						productId,
						commissionRate,
						isActive: true,
					},
				},
			},
			{ new: true }
		);

		if (!affiliate) {
			throw new HttpError(404, 'ไม่พบ affiliate');
		}

		return affiliate;
	} catch (err: any) {
		console.error('Error in addProductToAffiliate:', err);
		if (err instanceof HttpError) throw err;
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะเพิ่มสินค้าให้ affiliate');
	}
}

export async function removeProductFromAffiliate(siteId: string, affiliateId: string, productId: string) {
	try {
		const affiliate = await Affiliate.findOneAndUpdate(
			{ _id: affiliateId, siteId },
			{ $pull: { products: { productId } } },
			{ new: true }
		);

		if (!affiliate) {
			throw new HttpError(404, 'ไม่พบ affiliate');
		}

		return affiliate;
	} catch (err: any) {
		console.error('Error in removeProductFromAffiliate:', err);
		if (err instanceof HttpError) throw err;
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะลบสินค้าจาก affiliate');
	}
}

export async function trackAffiliateClick(siteId: string, affiliateCode: string, productId: string, trackingData: any) {
	try {
		const affiliate = await (Affiliate as any).findByCode(affiliateCode);
		if (!affiliate || affiliate.siteId !== siteId) {
			throw new HttpError(404, 'ไม่พบ affiliate code');
		}

		// ตรวจสอบว่าสินค้าอยู่ในรายการของ affiliate หรือไม่
		const productInAffiliate = affiliate.products.find((p: any) => p.productId === productId && p.isActive);
		if (!productInAffiliate) {
			throw new HttpError(400, 'สินค้านี้ไม่อยู่ในรายการของ affiliate');
		}

		// บันทึกการคลิก
		const click = await AffiliateClick.create({
			siteId,
			affiliateId: affiliate._id,
			productId,
			customerId: trackingData.customerId,
			ipAddress: trackingData.ipAddress,
			userAgent: trackingData.userAgent,
			referrer: trackingData.referrer,
		});

		// อัปเดตสถิติ affiliate
		await Affiliate.findByIdAndUpdate(affiliate._id, {
			$inc: {
				'performance.clicks': 1,
				'tracking.totalClicks': 1,
			},
			$set: { 'tracking.lastActivity': new Date() },
		});

		return click;
	} catch (err: any) {
		console.error('Error in trackAffiliateClick:', err);
		if (err instanceof HttpError) throw err;
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะบันทึกการคลิก');
	}
}

export async function processAffiliateOrder(siteId: string, orderId: string, affiliateCode: string, trackingData: any) {
	try {
		const affiliate = await (Affiliate as any).findByCode(affiliateCode);
		if (!affiliate || affiliate.siteId !== siteId) {
			throw new HttpError(404, 'ไม่พบ affiliate code');
		}

		const order = await Order.findById(orderId);
		if (!order || order.siteId !== siteId) {
			throw new HttpError(404, 'ไม่พบออเดอร์');
		}

		// คำนวณ commission สำหรับแต่ละสินค้า
		const affiliateOrders = [];

		for (const item of order.items) {
			const productInAffiliate = affiliate.products.find(
				(p: any) => p.productId === item.productId.toString() && p.isActive
			);

			if (productInAffiliate) {
				const commissionAmount = (item.price * item.quantity * productInAffiliate.commissionRate) / 100;

				const affiliateOrder = await AffiliateOrder.create({
					siteId,
					affiliateId: affiliate._id,
					orderId,
					productId: item.productId.toString(),
					customerId: order.userId.toString(),
					orderAmount: item.price * item.quantity,
					commissionAmount,
					commissionRate: productInAffiliate.commissionRate,
					status: affiliate.settings.autoApprove ? 'approved' : 'pending',
					trackingData,
				});

				affiliateOrders.push(affiliateOrder);
			}
		}

		// อัปเดตสถิติ affiliate
		const totalCommission = affiliateOrders.reduce((sum, ao) => sum + ao.commissionAmount, 0);

		await Affiliate.findByIdAndUpdate(affiliate._id, {
			$inc: {
				'performance.totalSales': 1,
				'performance.totalRevenue': order.totalAmount,
				'performance.totalCommission': totalCommission,
				'performance.totalOrders': 1,
			},
		});

		// อัปเดต conversion rate
		const stats = await (AffiliateClick as any).getConversionStats(siteId, affiliate._id);
		if (stats[0]) {
			const conversionRate = stats[0].totalClicks > 0 ? (stats[0].conversions / stats[0].totalClicks) * 100 : 0;

			await Affiliate.findByIdAndUpdate(affiliate._id, {
				$set: { 'performance.conversionRate': conversionRate },
			});
		}

		return affiliateOrders;
	} catch (err: any) {
		console.error('Error in processAffiliateOrder:', err);
		if (err instanceof HttpError) throw err;
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะประมวลผล affiliate order');
	}
}

export async function getAffiliateOrders(siteId: string, affiliateId: string, page: number = 1, limit: number = 10) {
	try {
		const skip = (page - 1) * limit;
		const orders = await (AffiliateOrder as any).findByAffiliate(siteId, affiliateId).skip(skip).limit(limit);

		const total = await AffiliateOrder.countDocuments({ siteId, affiliateId });

		return {
			orders,
			pagination: {
				page,
				limit,
				total,
				pages: Math.ceil(total / limit),
			},
		};
	} catch (err: any) {
		console.error('Error in getAffiliateOrders:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง affiliate orders');
	}
}

export async function approveCommission(siteId: string, orderId: string) {
	try {
		const affiliateOrder = await AffiliateOrder.findOneAndUpdate(
			{ siteId, orderId, status: 'pending' },
			{ $set: { status: 'approved' } },
			{ new: true }
		);

		if (!affiliateOrder) {
			throw new HttpError(404, 'ไม่พบ affiliate order');
		}

		return affiliateOrder;
	} catch (err: any) {
		console.error('Error in approveCommission:', err);
		if (err instanceof HttpError) throw err;
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะอนุมัติ commission');
	}
}

export async function getTopPerformers(siteId: string, limit: number = 10) {
	try {
		const performers = await (Affiliate as any).getTopPerformers(siteId, limit);
		return performers;
	} catch (err: any) {
		console.error('Error in getTopPerformers:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง top performers');
	}
}

export async function getAffiliateStats(siteId: string, affiliateId: string) {
	try {
		const affiliate = await Affiliate.findById(affiliateId);
		if (!affiliate || affiliate.siteId !== siteId) {
			throw new HttpError(404, 'ไม่พบ affiliate');
		}

		const clickStats = await (AffiliateClick as any).getConversionStats(siteId, affiliateId);
		const pendingCommissions = await AffiliateOrder.countDocuments({
			siteId,
			affiliateId,
			status: 'pending',
		});

		return {
			affiliate,
			clickStats: clickStats[0] || { totalClicks: 0, uniqueClicks: 0, conversions: 0 },
			pendingCommissions,
		};
	} catch (err: any) {
		console.error('Error in getAffiliateStats:', err);
		if (err instanceof HttpError) throw err;
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง affiliate stats');
	}
}
