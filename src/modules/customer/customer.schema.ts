import { t } from 'elysia';
// User validation schemas
export const signupSchema = t.Object({
	email: t.String({
		format: 'email',
		description: 'Valid email address',
		error: 'อีเมลไม่ถูกต้อง',
	}),
	password: t.String({
		minLength: 6,
		description: 'Password must be at least 6 characters',
		error: 'รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร',
	}),
});

export const signinSchema = t.Object({
	email: t.String({
		format: 'email',
		description: 'Valid email address',
		error: 'อีเมลไม่ถูกต้อง',
	}),
	password: t.String({
		minLength: 6,
		description: 'Password must be at least 6 characters',
		error: 'รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร',
	}),
});

export const resetPasswordCustomerSchema = t.Object({
	token: t.String({
		minLength: 1,
		description: 'Reset password token',
		error: 'โทเค็นรีเซ็ตรหัสผ่านต้องไม่ว่าง',
	}),
	newPassword: t.String({
		minLength: 6,
		description: 'New password must be at least 6 characters',
		error: 'รหัสผ่านใหม่ต้องมีอย่างน้อย 6 ตัวอักษร',
	}),
});

// Response schemas
export const authResponseCustomerSchema = t.Object({
	success: t.<PERSON>({ error: 'success ต้องเป็น true หรือ false เท่านั้น' }),
	message: t.String({ error: 'message ต้องเป็นข้อความ' }),
	statusMessage: t.String({ error: 'statusMessage ต้องเป็นข้อความ' }),
	timestamp: t.String({ error: 'timestamp ต้องเป็นข้อความ' }),
	data: t.Optional(
		t.Object({
			customer: t.Object({
				_id: t.String({ error: '_id ต้องเป็นข้อความ' }),
				email: t.String({ error: 'email ต้องเป็นข้อความ' }),
				firstName: t.Optional(t.String({ error: 'firstName ต้องเป็นข้อความ' })),
				lastName: t.Optional(t.String({ error: 'lastName ต้องเป็นข้อความ' })),
				phone: t.Optional(t.String({ error: 'phone ต้องเป็นข้อความ' })),
				avatar: t.Optional(t.String({ error: 'avatar ต้องเป็นข้อความ' })),
				cover: t.Optional(t.String({ error: 'cover ต้องเป็นข้อความ' })),
				isEmailVerified: t.Boolean({ error: 'isEmailVerified ต้องเป็น true หรือ false เท่านั้น' }),
				siteId: t.String({ error: 'siteId ต้องเป็นข้อความ' }),
				moneyPoint: t.Number({ error: 'moneyPoint ต้องเป็นตัวเลข' }),
				goldPoint: t.Number({ error: 'goldPoint ต้องเป็นตัวเลข' }),
			}),
			token: t.String({ error: 'token ต้องเป็นข้อความ' }),
			refreshToken: t.String({ error: 'refreshToken ต้องเป็นข้อความ' }),
		})
	),
});

export const customerAuthResponseSchema = t.Object({
	success: t.Boolean({ error: 'success ต้องเป็น true หรือ false เท่านั้น' }),
	message: t.String({ error: 'message ต้องเป็นข้อความ' }),
	data: t.Optional(
		t.Object({
			customer: t.Object({
				id: t.String({ error: 'id ต้องเป็นข้อความ' }),
				siteId: t.String({ error: 'siteId ต้องเป็นข้อความ' }),
				email: t.String({ error: 'email ต้องเป็นข้อความ' }),
				firstName: t.Optional(t.String({ error: 'firstName ต้องเป็นข้อความ' })),
				lastName: t.Optional(t.String({ error: 'lastName ต้องเป็นข้อความ' })),
				phone: t.Optional(t.String({ error: 'phone ต้องเป็นข้อความ' })),
				avatar: t.Optional(t.String({ error: 'avatar ต้องเป็นข้อความ' })),
				isEmailVerified: t.Boolean({ error: 'isEmailVerified ต้องเป็น true หรือ false เท่านั้น' }),
				moneyPoint: t.Number({ error: 'moneyPoint ต้องเป็นตัวเลข' }),
				goldPoint: t.Number({ error: 'goldPoint ต้องเป็นตัวเลข' }),
			}),
			token: t.String({ error: 'token ต้องเป็นข้อความ' }),
			refreshToken: t.String({ error: 'refreshToken ต้องเป็นข้อความ' }),
		})
	),
});

export const customerProfileResponseSchema = t.Object({
	success: t.Boolean({ error: 'success ต้องเป็น true หรือ false เท่านั้น' }),
	message: t.String({ error: 'message ต้องเป็นข้อความ' }),
	statusMessage: t.String({ error: 'statusMessage ต้องเป็นข้อความ' }),
	timestamp: t.String({ error: 'timestamp ต้องเป็นข้อความ' }),
	data: t.Object({
		_id: t.String({ error: '_id ต้องเป็นข้อความ' }),
		email: t.String({ error: 'email ต้องเป็นข้อความ' }),
		firstName: t.Optional(t.String({ error: 'firstName ต้องเป็นข้อความ' })),
		lastName: t.Optional(t.String({ error: 'lastName ต้องเป็นข้อความ' })),
		phone: t.Optional(t.String({ error: 'phone ต้องเป็นข้อความ' })),
		avatar: t.Optional(t.String({ error: 'avatar ต้องเป็นข้อความ' })),
		cover: t.Optional(t.String({ error: 'cover ต้องเป็นข้อความ' })),
		isEmailVerified: t.Boolean({ error: 'isEmailVerified ต้องเป็น true หรือ false เท่านั้น' }),
		siteId: t.String({ error: 'siteId ต้องเป็นข้อความ' }),
		moneyPoint: t.Number({ error: 'moneyPoint ต้องเป็นตัวเลข' }),
		goldPoint: t.Number({ error: 'goldPoint ต้องเป็นตัวเลข' }),
	}),
});
