import { generateFileId } from '@/core/utils/idGenerator';
import { Schema, model } from 'mongoose';

export interface IMediaBase {
	_id: string;
	siteId: string;
	filename: string;
	originalName: string;
	mimeType: string;
	size: number; // bytes
	path: string;
	url: string;
	type: 'image' | 'video' | 'audio' | 'document' | 'archive' | 'other';
	category: 'product' | 'banner' | 'avatar' | 'content' | 'document' | 'other';
	alt?: string;
	title?: string;
	description?: string;
	tags: string[];
	metadata: {
		width?: number;
		height?: number;
		duration?: number; // for video/audio
		pages?: number; // for documents
		compression?: string;
		colorSpace?: string;
		format?: string;
		quality?: number;
	};
	thumbnails?: {
		small?: string;
		medium?: string;
		large?: string;
	};
	isPublic: boolean;
	isOptimized: boolean;
	uploadedBy: string;
	usageCount: number;
	lastUsed?: Date;
	expiresAt?: Date;
}

export interface IMedia extends IMediaBase {
	createdAt: Date;
	updatedAt: Date;
}

const mediaSchema = new Schema<IMedia>(
	{
		_id: {
			type: String,
			required: true,
			trim: true,
			default: () => generateFileId(10),
		},
		siteId: {
			type: String,
			required: true,
			ref: 'Site',
		},
		filename: {
			type: String,
			required: true,
			trim: true,
		},
		originalName: {
			type: String,
			required: true,
			trim: true,
		},
		mimeType: {
			type: String,
			required: true,
		},
		size: {
			type: Number,
			required: true,
			min: 0,
		},
		path: {
			type: String,
			required: true,
		},
		url: {
			type: String,
			required: true,
		},
		type: {
			type: String,
			required: true,
			enum: ['image', 'video', 'audio', 'document', 'archive', 'other'],
		},
		category: {
			type: String,
			required: true,
			enum: ['product', 'banner', 'avatar', 'content', 'document', 'other'],
			default: 'other',
		},
		alt: {
			type: String,
			default: '',
		},
		title: {
			type: String,
			default: '',
		},
		description: {
			type: String,
			default: '',
		},
		tags: [
			{
				type: String,
				trim: true,
			},
		],
		metadata: {
			width: { type: Number },
			height: { type: Number },
			duration: { type: Number },
			pages: { type: Number },
			compression: { type: String },
			colorSpace: { type: String },
			format: { type: String },
			quality: { type: Number },
		},
		thumbnails: {
			small: { type: String },
			medium: { type: String },
			large: { type: String },
		},
		isPublic: {
			type: Boolean,
			default: true,
		},
		isOptimized: {
			type: Boolean,
			default: false,
		},
		uploadedBy: {
			type: String,
			required: true,
			ref: 'User',
		},
		usageCount: {
			type: Number,
			default: 0,
		},
		lastUsed: {
			type: Date,
		},
		expiresAt: {
			type: Date,
		},
	},
	{
		timestamps: true,
		id: false,
		versionKey: false,
		toJSON: {
			virtuals: true,
		},
		toObject: {
			virtuals: true,
		},
	}
);

// Indexes for better performance
mediaSchema.index({ siteId: 1, type: 1 });
mediaSchema.index({ siteId: 1, category: 1 });
mediaSchema.index({ siteId: 1, uploadedBy: 1 });
mediaSchema.index({ siteId: 1, createdAt: -1 });
mediaSchema.index({ siteId: 1, usageCount: -1 });
mediaSchema.index({ tags: 1 });
mediaSchema.index({ filename: 'text', originalName: 'text', alt: 'text', title: 'text' });

// Virtual for file extension
mediaSchema.virtual('extension').get(function () {
	return this.filename.split('.').pop()?.toLowerCase();
});

// Virtual for human readable size
mediaSchema.virtual('humanSize').get(function () {
	const bytes = this.size;
	const sizes = ['Bytes', 'KB', 'MB', 'GB'];
	if (bytes === 0) return '0 Bytes';
	const i = Math.floor(Math.log(bytes) / Math.log(1024));
	return Math.round((bytes / 1024 ** i) * 100) / 100 + ' ' + sizes[i];
});

export const Media = model<IMedia>('Media', mediaSchema);
