import { Media } from './media.model';
import { logger } from '../../core/utils';
import path from 'path';
import sharp from 'sharp';
import { generateFileId } from '../../core/utils';
import fs from 'fs/promises'; 

import { IMedia } from './media.model';

export class MediaService {
	private uploadDir = 'public/uploads';
	private allowedImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'];
	private allowedVideoTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/webm'];
	private allowedAudioTypes = ['audio/mp3', 'audio/wav', 'audio/ogg', 'audio/m4a'];
	private allowedDocumentTypes = [
		'application/pdf',
		'text/plain',
		'application/msword',
		'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
	];
	private allowedArchiveTypes = ['application/zip', 'application/x-rar-compressed', 'application/x-7z-compressed'];

	constructor() {
		this.ensureUploadDir();
	}

	// อัพโหลดไฟล์
	async uploadFile(
		file: File,
		siteId: string,
		uploadedBy: string,
		options: {
			category?: string;
			alt?: string;
			title?: string;
			description?: string;
			tags?: string[];
			isPublic?: boolean;
			expiresAt?: Date;
		} = {}
	): Promise<IMedia> {
		try {
			// ตรวจสอบประเภทไฟล์
			const fileType = this.getFileType(file.type);
			if (!this.isAllowedFileType(file.type)) {
				throw new Error('ประเภทไฟล์ไม่ได้รับอนุญาต');
			}

			// ตรวจสอบขนาดไฟล์ (50MB max)
			const maxSize = 50 * 1024 * 1024;
			if (file.size > maxSize) {
				throw new Error('ขนาดไฟล์เกิน 50MB');
			}

			// สร้างชื่อไฟล์ใหม่
			const fileId = generateFileId(12);
			const extension = this.getFileExtension(file.name);
			const filename = `${fileId}.${extension}`;

			// สร้างโฟลเดอร์ตามวันที่
			const dateFolder = new Date().toISOString().slice(0, 7); // YYYY-MM
			const uploadPath = path.join(this.uploadDir, siteId, dateFolder);
			await this.ensureDir(uploadPath);

			// บันทึกไฟล์
			const filePath = path.join(uploadPath, filename);
			const buffer = await file.arrayBuffer();
			await fs.writeFile(filePath, Buffer.from(buffer));

			// สร้าง URL
			const url = `/uploads/${siteId}/${dateFolder}/${filename}`;

			// ดึง metadata
			const metadata = await this.extractMetadata(filePath, fileType);

			// สร้างข้อมูลในฐานข้อมูล
			const media = new Media({
				siteId,
				filename,
				originalName: file.name,
				mimeType: file.type,
				size: file.size,
				path: filePath,
				url,
				type: fileType,
				category: options.category || 'other',
				alt: options.alt || '',
				title: options.title || file.name,
				description: options.description || '',
				tags: options.tags || [],
				metadata,
				isPublic: options.isPublic !== false,
				uploadedBy,
				expiresAt: options.expiresAt,
			});

			await media.save();

			// สร้าง thumbnails สำหรับรูปภาพ
			if (fileType === 'image' && this.allowedImageTypes.includes(file.type)) {
				await this.generateThumbnails(media as any);
			}

			logger.info(`อัพโหลดไฟล์: ${file.name} -> ${filename} สำหรับเว็บไซต์ ${siteId}`);

			return media as any;
		} catch (error) {
			logger.error('เกิดข้อผิดพลาดในการอัพโหลดไฟล์');
			throw error;
		}
	}

	// อัพเดทข้อมูลไฟล์
	async updateMedia(mediaId: string, siteId: string, data: Partial<IMedia>): Promise<IMedia | null> {
		try {
			const media = await Media.findOneAndUpdate({ _id: mediaId, siteId }, data, { new: true });

			if (!media) {
				throw new Error('ไม่พบไฟล์ที่ระบุ');
			}

			logger.info(`อัพเดทข้อมูลไฟล์: ${media.filename}`);

			return media as any;
		} catch (error) {
			logger.error('เกิดข้อผิดพลาดในการอัพเดทข้อมูลไฟล์');
			throw error;
		}
	}

	// ลบไฟล์
	async deleteMedia(mediaId: string, siteId: string): Promise<boolean> {
		try {
			const media = await Media.findOne({ _id: mediaId, siteId });
			if (!media) {
				throw new Error('ไม่พบไฟล์ที่ระบุ');
			}

			// ลบไฟล์จากระบบ
			try {
				await fs.unlink(media.path);

				// ลบ thumbnails
				if (media.thumbnails) {
					for (const [size, thumbPath] of Object.entries(media.thumbnails)) {

						if (thumbPath) {
							const fullThumbPath = path.join('public', thumbPath);
							try {
								await fs.unlink(fullThumbPath);
							} catch (_e) {
								// ไม่ต้องทำอะไรถ้าลบ thumbnail ไม่ได้
							}
						}
					}
				}
			 
			} catch (fileError) {
				logger.warn('ไม่สามารถลบไฟล์จากระบบได้');
			}

			// ลบข้อมูลจากฐานข้อมูล
			await Media.findByIdAndDelete(mediaId);

			logger.info(`ลบไฟล์: ${media.filename}`);

			return true;
		} catch (error) {
			logger.error('เกิดข้อผิดพลาดในการลบไฟล์');
			throw error;
		}
	}

	// ดึงรายการไฟล์
	async getMedia(query: {
		siteId?: string;
		type?: string;
		category?: string;
		isPublic?: boolean;
		isOptimized?: boolean;
		uploadedBy?: string;
		tags?: string;
		search?: string;
		minSize?: number;
		maxSize?: number;
		startDate?: Date;
		endDate?: Date;
		sortBy?: string;
		sortOrder?: 'asc' | 'desc';
		page?: number;
		limit?: number;
	}): Promise<{ media: IMedia[]; total: number; page: number; totalPages: number }> {
		try {
			const page = query.page || 1;
			const limit = query.limit || 20;
			const skip = (page - 1) * limit;

			const filter: any = {};

			if (query.siteId) filter.siteId = query.siteId;
			if (query.type) filter.type = query.type;
			if (query.category) filter.category = query.category;
			if (query.isPublic !== undefined) filter.isPublic = query.isPublic;
			if (query.isOptimized !== undefined) filter.isOptimized = query.isOptimized;
			if (query.uploadedBy) filter.uploadedBy = query.uploadedBy;

			if (query.tags) {
				filter.tags = { $in: query.tags.split(',').map(tag => tag.trim()) };
			}

			if (query.search) {
				filter.$text = { $search: query.search };
			}

			if (query.minSize || query.maxSize) {
				filter.size = {};
				if (query.minSize) filter.size.$gte = query.minSize;
				if (query.maxSize) filter.size.$lte = query.maxSize;
			}

			if (query.startDate || query.endDate) {
				filter.createdAt = {};
				if (query.startDate) filter.createdAt.$gte = query.startDate;
				if (query.endDate) filter.createdAt.$lte = query.endDate;
			}

			// Sorting
			const sortBy = query.sortBy || 'createdAt';
			const sortOrder = query.sortOrder === 'asc' ? 1 : -1;
			const sort: any = { [sortBy]: sortOrder };

			const [media, total] = await Promise.all([
				Media.find(filter).sort(sort).skip(skip).limit(limit).lean(),
				Media.countDocuments(filter),
			]);

			return {
				media: media as unknown as IMedia[],
				total,
				page,
				totalPages: Math.ceil(total / limit),
			};
		} catch (error) {
			logger.error('เกิดข้อผิดพลาดในการดึงรายการไฟล์');
			throw error;
		}
	}

	// ดึงไฟล์ตาม ID
	async getMediaById(mediaId: string, siteId: string): Promise<IMedia | null> {
		try {
			return (await Media.findOne({ _id: mediaId, siteId }).lean()) as IMedia | null;
		} catch (error) {
			logger.error('เกิดข้อผิดพลาดในการดึงไฟล์');
			throw error;
		}
	}

	// ปรับขนาดรูปภาพ
	async resizeImage(
		mediaId: string,
		siteId: string,
		options: {
			width?: number;
			height?: number;
			quality?: number;
			format?: 'jpeg' | 'png' | 'webp' | 'avif';
			fit?: 'cover' | 'contain' | 'fill' | 'inside' | 'outside';
		}
	): Promise<IMedia> {
		try {
			const media = await Media.findOne({ _id: mediaId, siteId });
			if (!media) {
				throw new Error('ไม่พบไฟล์ที่ระบุ');
			}

			if (media.type !== 'image') {
				throw new Error('สามารถปรับขนาดได้เฉพาะรูปภาพเท่านั้น');
			}

			// สร้างชื่อไฟล์ใหม่
			const newFilename = `${generateFileId(12)}.${options.format || 'jpeg'}`;
			const newPath = path.join(path.dirname(media.path), newFilename);
			const newUrl = media.url.replace(media.filename, newFilename);

			// ปรับขนาดรูปภาพ
			let sharpInstance = sharp(media.path);

			if (options.width || options.height) {
				sharpInstance = sharpInstance.resize(options.width, options.height, {
					fit: options.fit || 'cover',
				});
			}

			if (options.format) {
				switch (options.format) {
					case 'jpeg':
						sharpInstance = sharpInstance.jpeg({ quality: options.quality || 80 });
						break;
					case 'png':
						sharpInstance = sharpInstance.png({ quality: options.quality || 80 });
						break;
					case 'webp':
						sharpInstance = sharpInstance.webp({ quality: options.quality || 80 });
						break;
					case 'avif':
						sharpInstance = sharpInstance.avif({ quality: options.quality || 80 });
						break;
				}
			}

			await sharpInstance.toFile(newPath);

			// ดึงข้อมูลไฟล์ใหม่
			const stats = await fs.stat(newPath);
			const newMetadata = await this.extractMetadata(newPath, 'image');

			// สร้างข้อมูลใหม่
			const newMedia = new Media({
				...media.toObject(),
				_id: generateFileId(10),
				filename: newFilename,
				path: newPath,
				url: newUrl,
				size: stats.size,
				metadata: newMetadata,
				isOptimized: true,
				createdAt: new Date(),
				updatedAt: new Date(),
			});

			await newMedia.save();

			logger.info(`ปรับขนาดรูปภาพ: ${media.filename} -> ${newFilename}`);

			return newMedia as any;
		} catch (error) {
			logger.error('เกิดข้อผิดพลาดในการปรับขนาดรูปภาพ');
			throw error;
		}
	}

	// สร้าง thumbnails
	async generateThumbnails(
		media: IMedia,
		sizes: {
			small?: { width: number; height: number };
			medium?: { width: number; height: number };
			large?: { width: number; height: number };
		} = {
			small: { width: 150, height: 150 },
			medium: { width: 300, height: 300 },
			large: { width: 600, height: 600 },
		},
		quality: number = 80,
		format: 'jpeg' | 'png' | 'webp' = 'webp'
	): Promise<IMedia> {
		try {
			if ((media as any).type !== 'image') {
				throw new Error('สามารถสร้าง thumbnail ได้เฉพาะรูปภาพเท่านั้น');
			}

			const thumbnails: any = {};
			const baseDir = path.dirname(media.path);
			const baseName = path.parse(media.filename).name;

			for (const [sizeName, dimensions] of Object.entries(sizes)) {
				if (dimensions) {
					const thumbFilename = `${baseName}_${sizeName}.${format}`;
					const thumbPath = path.join(baseDir, thumbFilename);
					const thumbUrl = media.url.replace(media.filename, thumbFilename);

					await sharp(media.path)
						.resize(dimensions.width, dimensions.height, { fit: 'cover' })
						.webp({ quality })
						.toFile(thumbPath);

					thumbnails[sizeName] = thumbUrl;
				}
			}

			// อัพเดทข้อมูลในฐานข้อมูล
			const updatedMedia = await Media.findByIdAndUpdate((media as any)._id, { thumbnails }, { new: true });

			logger.info(`สร้าง thumbnails สำหรับ: ${media.filename}`);

			return updatedMedia as any;
		} catch (error) {
			logger.error('เกิดข้อผิดพลาดในการสร้าง thumbnails');
			throw error;
		}
	}

	// ดำเนินการกับหลายไฟล์
	async bulkAction(
		mediaIds: string[],
		siteId: string,
		action: 'delete' | 'optimize' | 'makePublic' | 'makePrivate' | 'addTags' | 'removeTags' | 'changeCategory',
		data?: {
			tags?: string[];
			category?: string;
		}
	): Promise<{ success: number; failed: number; errors: string[] }> {
		try {
			let success = 0;
			let failed = 0;
			const errors: string[] = [];

			for (const mediaId of mediaIds) {
				try {
					switch (action) {
						case 'delete':
							await this.deleteMedia(mediaId, siteId);
							break;
						case 'optimize': {
							const media = await Media.findOne({ _id: mediaId, siteId });
							if (media && (media as any).type === 'image') {
								await this.generateThumbnails(media as any);
								await Media.findByIdAndUpdate(mediaId, { isOptimized: true });
							}
							break;
						}
						case 'makePublic':
							await Media.findOneAndUpdate({ _id: mediaId, siteId }, { isPublic: true });
							break;
						case 'makePrivate':
							await Media.findOneAndUpdate({ _id: mediaId, siteId }, { isPublic: false });
							break;
						case 'addTags':
							if (data?.tags) {
								await Media.findOneAndUpdate({ _id: mediaId, siteId }, { $addToSet: { tags: { $each: data.tags } } });
							}
							break;
						case 'removeTags':
							if (data?.tags) {
								await Media.findOneAndUpdate({ _id: mediaId, siteId }, { $pullAll: { tags: data.tags } });
							}
							break;
						case 'changeCategory':
							if (data?.category) {
								await Media.findOneAndUpdate({ _id: mediaId, siteId }, { category: data.category });
							}
							break;
					}
					success++;
				} catch (error: any) {
					failed++;
					errors.push(`${mediaId}: ${error.message}`);
				}
			}

			logger.info(`Bulk action ${action}: ${success} สำเร็จ, ${failed} ล้มเหลว`);

			return { success, failed, errors };
		} catch (error) {
			logger.error('เกิดข้อผิดพลาดในการดำเนินการกับหลายไฟล์');
			throw error;
		}
	}

	// ดึงสถิติไฟล์
	async getMediaStats(siteId: string): Promise<{
		totalFiles: number;
		totalSize: number;
		filesByType: Array<{ type: string; count: number; size: number }>;
		filesByCategory: Array<{ category: string; count: number; size: number }>;
		recentUploads: number;
		mostUsed: IMedia[];
	}> {
		try {
			const [totalStats, typeStats, categoryStats, recentCount, mostUsed] = await Promise.all([
				Media.aggregate([
					{ $match: { siteId } },
					{
						$group: {
							_id: null,
							totalFiles: { $sum: 1 },
							totalSize: { $sum: '$size' },
						},
					},
				]),
				Media.aggregate([
					{ $match: { siteId } },
					{
						$group: {
							_id: '$type',
							count: { $sum: 1 },
							size: { $sum: '$size' },
						},
					},
					{ $sort: { count: -1 } },
				]),
				Media.aggregate([
					{ $match: { siteId } },
					{
						$group: {
							_id: '$category',
							count: { $sum: 1 },
							size: { $sum: '$size' },
						},
					},
					{ $sort: { count: -1 } },
				]),
				Media.countDocuments({
					siteId,
					createdAt: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) },
				}),
				Media.find({ siteId }).sort({ usageCount: -1 }).limit(10).lean(),
			]);

			const stats = totalStats[0] || { totalFiles: 0, totalSize: 0 };

			return {
				totalFiles: stats.totalFiles,
				totalSize: stats.totalSize,
				filesByType: typeStats.map(stat => ({
					type: stat._id,
					count: stat.count,
					size: stat.size,
				})),
				filesByCategory: categoryStats.map(stat => ({
					category: stat._id,
					count: stat.count,
					size: stat.size,
				})),
				recentUploads: recentCount,
				mostUsed: mostUsed as unknown as IMedia[],
			};
		} catch (error) {
			logger.error('เกิดข้อผิดพลาดในการดึงสถิติไฟล์');
			throw error;
		}
	}

	// Helper methods
	private async ensureUploadDir(): Promise<void> {
		try {
			await fs.access(this.uploadDir);
		} catch {
			await fs.mkdir(this.uploadDir, { recursive: true });
		}
	}

	private async ensureDir(dirPath: string): Promise<void> {
		try {
			await fs.access(dirPath);
		} catch {
			await fs.mkdir(dirPath, { recursive: true });
		}
	}

	private getFileType(mimeType: string): 'image' | 'video' | 'audio' | 'document' | 'archive' | 'other' {
		if (this.allowedImageTypes.includes(mimeType)) return 'image';
		if (this.allowedVideoTypes.includes(mimeType)) return 'video';
		if (this.allowedAudioTypes.includes(mimeType)) return 'audio';
		if (this.allowedDocumentTypes.includes(mimeType)) return 'document';
		if (this.allowedArchiveTypes.includes(mimeType)) return 'archive';
		return 'other';
	}

	private isAllowedFileType(mimeType: string): boolean {
		return [
			...this.allowedImageTypes,
			...this.allowedVideoTypes,
			...this.allowedAudioTypes,
			...this.allowedDocumentTypes,
			...this.allowedArchiveTypes,
		].includes(mimeType);
	}

	private getFileExtension(filename: string): string {
		return filename.split('.').pop()?.toLowerCase() || '';
	}

	private async extractMetadata(filePath: string, fileType: string): Promise<any> {
		const metadata: any = {};

		try {
			if (fileType === 'image') {
				const imageInfo = await sharp(filePath).metadata();
				metadata.width = imageInfo.width;
				metadata.height = imageInfo.height;
				metadata.format = imageInfo.format;
				metadata.colorSpace = imageInfo.space;
			}
		} catch (error) {
			logger.warn('ไม่สามารถดึง metadata ได้');
		}

		return metadata;
	}

	// อัพเดทการใช้งาน
	async incrementUsage(mediaId: string): Promise<void> {
		try {
			await Media.findByIdAndUpdate(mediaId, {
				$inc: { usageCount: 1 },
				lastUsed: new Date(),
			});
		} catch (error) {
			logger.error('เกิดข้อผิดพลาดในการอัพเดทการใช้งาน');
		}
	}
}
