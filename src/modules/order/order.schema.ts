import { t } from 'elysia';

// Address Schema
const addressSchema = t.Object({
	firstName: t.String({ minLength: 1, error: 'First name is required' }),
	lastName: t.String({ minLength: 1, error: 'Last name is required' }),
	phone: t.String({ minLength: 1, error: 'Phone number is required' }),
	email: t.String({ format: 'email', error: 'Invalid email address' }),
	address: t.String({ minLength: 1, error: 'Address is required' }),
	city: t.String({ minLength: 1, error: 'City is required' }),
	state: t.String({ minLength: 1, error: 'State is required' }),
	postalCode: t.String({ minLength: 1, error: 'Postal code is required' }),
	country: t.String({ minLength: 1, error: 'Country is required' }),
});

// Order Item Schema
const orderItemSchema = t.Object({
	productId: t.String({ minLength: 1, error: 'Product ID is required' }),
	variantId: t.Optional(t.String({ error: 'Variant ID is optional' })),
	quantity: t.Number({ minimum: 1, error: 'Quantity must be at least 1' }),
});

// Create Order Schema
export const createOrderSchema = t.Object({
	customerEmail: t.String({ format: 'email', error: 'Invalid email address' }),
	customerName: t.String({ minLength: 1, error: 'Customer name is required' }),
	items: t.Array(orderItemSchema, { minItems: 1, error: 'At least one item is required' }),
	shippingAddress: addressSchema,
	billingAddress: addressSchema,
	paymentMethod: t.Union(
		[
			t.Literal('cash', { error: 'Cash payment method is not available' }),
			t.Literal('bank_transfer', { error: 'Bank transfer payment method is not available' }),
			t.Literal('credit_card', { error: 'Credit card payment method is not available' }),
			t.Literal('online_payment', { error: 'Online payment method is not available' }),
		],
		{ error: 'Invalid payment method' }
	),
	customerNotes: t.Optional(t.String({ error: 'Customer notes are optional' })),
	adminNotes: t.Optional(t.String({ error: 'Admin notes are optional' })),
});

// Update Order Status Schema
export const updateOrderStatusSchema = t.Object({
	status: t.Union(
		[
			t.Literal('pending', { error: 'Status cannot be pending' }),
			t.Literal('confirmed', { error: 'Status cannot be confirmed' }),
			t.Literal('processing', { error: 'Status cannot be processing' }),
			t.Literal('shipped', { error: 'Status cannot be shipped' }),
			t.Literal('delivered', { error: 'Status cannot be delivered' }),
			t.Literal('cancelled', { error: 'Status cannot be cancelled' }),
			t.Literal('refunded', { error: 'Status cannot be refunded' }),
		],
		{ error: 'Invalid status' }
	),
});

// Update Payment Status Schema
export const updatePaymentStatusSchema = t.Object({
	paymentStatus: t.Union(
		[
			t.Literal('pending', { error: 'Payment status cannot be pending' }),
			t.Literal('paid', { error: 'Payment status cannot be paid' }),
			t.Literal('failed', { error: 'Payment status cannot be failed' }),
			t.Literal('refunded', { error: 'Payment status cannot be refunded' }),
		],
		{ error: 'Invalid payment status' }
	),
	paymentId: t.Optional(t.String({ error: 'Payment ID is optional' })),
});

// Update Shipping Schema
export const updateShippingSchema = t.Object({
	trackingNumber: t.Optional(t.String({ error: 'Tracking number is optional' })),
	estimatedDelivery: t.Optional(t.String({ format: 'date-time', error: 'Invalid date format' })),
	shippingMethod: t.Optional(t.String({ error: 'Shipping method is optional' })),
});

// Cancel Order Schema
export const cancelOrderSchema = t.Object({
	reason: t.Optional(t.String({ error: 'Reason for cancellation is optional' })),
});

// Order Filter Schema
export const orderFilterSchema = t.Object({
	status: t.Optional(
		t.Union(
			[
				t.Literal('pending', { error: 'Status cannot be pending' }),
				t.Literal('confirmed', { error: 'Status cannot be confirmed' }),
				t.Literal('processing', { error: 'Status cannot be processing' }),
				t.Literal('shipped', { error: 'Status cannot be shipped' }),
				t.Literal('delivered', { error: 'Status cannot be delivered' }),
				t.Literal('cancelled', { error: 'Status cannot be cancelled' }),
				t.Literal('refunded', { error: 'Status cannot be refunded' }),
			],
			{ error: 'Invalid status' }
		)
	),
	paymentStatus: t.Optional(
		t.Union(
			[
				t.Literal('pending', { error: 'Payment status cannot be pending' }),
				t.Literal('paid', { error: 'Payment status cannot be paid' }),
				t.Literal('failed', { error: 'Payment status cannot be failed' }),
				t.Literal('refunded', { error: 'Payment status cannot be refunded' }),
			],
			{ error: 'Invalid payment status' }
		)
	),
	startDate: t.Optional(t.String({ format: 'date-time', error: 'Invalid date format' })),
	endDate: t.Optional(t.String({ format: 'date-time', error: 'Invalid date format' })),
	page: t.Optional(t.Number({ minimum: 1, error: 'Page must be at least 1' })),
	limit: t.Optional(t.Number({ minimum: 1, maximum: 100, error: 'Limit must be between 1 and 100' })),
});

// Order Response Schema
export const orderResponseSchema = t.Object({
	success: t.Boolean({ error: 'Success must be a boolean' }),
	message: t.String({ error: 'Message is required' }),
	statusMessage: t.String({ error: 'Status message is required' }),
	timestamp: t.String({ error: 'Timestamp is required' }),
	data: t.Optional(t.Any({ error: 'Data is optional' })),
});

// Order List Response Schema
export const orderListResponseSchema = t.Object({
	success: t.Boolean({ error: 'Success must be a boolean' }),
	message: t.String({ error: 'Message is required' }),
	statusMessage: t.String({ error: 'Status message is required' }),
	timestamp: t.String({ error: 'Timestamp is required' }),
	data: t.Array(t.Any({ error: 'Data must be an array' }), { error: 'At least one item is required' }),
	total: t.Number({ error: 'Total is required' }),
	page: t.Number({ error: 'Page is required' }),
	limit: t.Number({ error: 'Limit is required' }),
});

// Order Stats Response Schema
export const orderStatsResponseSchema = t.Object({
	success: t.Boolean({ error: 'Success must be a boolean' }),
	message: t.String({ error: 'Message is required' }),
	statusMessage: t.String({ error: 'Status message is required' }),
	timestamp: t.String({ error: 'Timestamp is required' }),
	data: t.Object(
		{
			totalOrders: t.Number({ error: 'Total orders is required' }),
			todayOrders: t.Number({ error: 'Today orders is required' }),
			monthlyOrders: t.Number({ error: 'Monthly orders is required' }),
			pendingOrders: t.Number({ error: 'Pending orders is required' }),
			paidOrders: t.Number({ error: 'Paid orders is required' }),
			totalRevenue: t.Number({ error: 'Total revenue is required' }),
		},
		{ error: 'Data structure is incorrect' }
	),
});
