import mongoose, { Schema, type Document } from 'mongoose';
import { generateFileId } from '@/core/utils/idGenerator';

export type OrderStatus = 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'refunded';
export type PaymentStatus = 'pending' | 'paid' | 'failed' | 'refunded';
export type PaymentMethod = 'cash' | 'bank_transfer' | 'credit_card' | 'online_payment';

export interface IOrderItem {
	_id: string;
	productId: string;
	variantId?: string;
	name: string;
	sku?: string;
	quantity: number;
	unitPrice: number;
	totalPrice: number;
	discount?: number;
	finalPrice: number;
}

export interface IAddress {
	firstName: string;
	lastName: string;
	phone: string;
	email: string;
	address: string;
	city: string;
	state: string;
	postalCode: string;
	country: string;
}

export interface IOrder extends Document {
	_id: string;
	siteId: string;
	orderNumber: string;
	customerId: string;
	customerEmail: string;
	customerName: string;

	// Order details
	items: IOrderItem[];
	subtotal: number;
	tax: number;
	shipping: number;
	discount: number;
	total: number;

	// Status
	status: OrderStatus;
	paymentStatus: PaymentStatus;
	paymentMethod: PaymentMethod;

	// Addresses
	shippingAddress: IAddress;
	billingAddress: IAddress;

	// Shipping
	shippingMethod?: string;
	trackingNumber?: string;
	estimatedDelivery?: Date;

	// Payment
	paymentId?: string;
	paidAt?: Date;

	// Notes
	customerNotes?: string;
	adminNotes?: string;

	// Timestamps
	createdAt: Date;
	updatedAt: Date;
	confirmedAt?: Date;
	shippedAt?: Date;
	deliveredAt?: Date;
	cancelledAt?: Date;
}

const orderItemSchema = new Schema(
	{
		_id: { type: String, default: () => generateFileId(3) },
		productId: { type: String, required: true },
		variantId: { type: String },
		name: { type: String, required: true },
		sku: { type: String },
		quantity: { type: Number, required: true, min: 1 },
		unitPrice: { type: Number, required: true },
		totalPrice: { type: Number, required: true },
		discount: { type: Number, default: 0 },
		finalPrice: { type: Number, required: true },
	},
	{ _id: false }
);

const addressSchema = new Schema(
	{
		firstName: { type: String, required: true },
		lastName: { type: String, required: true },
		phone: { type: String, required: true },
		email: { type: String, required: true },
		address: { type: String, required: true },
		city: { type: String, required: true },
		state: { type: String, required: true },
		postalCode: { type: String, required: true },
		country: { type: String, required: true },
	},
	{ _id: false }
);

const orderSchema = new Schema<IOrder>(
	{
		_id: { type: String, default: () => generateFileId(5) },
		siteId: { type: String, required: true, index: true },
		orderNumber: { type: String, required: true, unique: true },
		customerId: { type: String, required: true, index: true },
		customerEmail: { type: String, required: true },
		customerName: { type: String, required: true },

		// Order details
		items: [orderItemSchema],
		subtotal: { type: Number, required: true },
		tax: { type: Number, default: 0 },
		shipping: { type: Number, default: 0 },
		discount: { type: Number, default: 0 },
		total: { type: Number, required: true },

		// Status
		status: {
			type: String,
			enum: ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded'],
			default: 'pending',
			index: true,
		},
		paymentStatus: {
			type: String,
			enum: ['pending', 'paid', 'failed', 'refunded'],
			default: 'pending',
			index: true,
		},
		paymentMethod: {
			type: String,
			enum: ['cash', 'bank_transfer', 'credit_card', 'online_payment'],
			required: true,
		},

		// Addresses
		shippingAddress: { type: addressSchema, required: true },
		billingAddress: { type: addressSchema, required: true },

		// Shipping
		shippingMethod: { type: String },
		trackingNumber: { type: String },
		estimatedDelivery: { type: Date },

		// Payment
		paymentId: { type: String },
		paidAt: { type: Date },

		// Notes
		customerNotes: { type: String },
		adminNotes: { type: String },

		// Timestamps
		confirmedAt: { type: Date },
		shippedAt: { type: Date },
		deliveredAt: { type: Date },
		cancelledAt: { type: Date },
	},
	{
		timestamps: true,
		versionKey: false,
	}
);

// Indexes
orderSchema.index({ siteId: 1, orderNumber: 1 }, { unique: true });
orderSchema.index({ siteId: 1, customerId: 1 });
orderSchema.index({ siteId: 1, status: 1 });
orderSchema.index({ siteId: 1, paymentStatus: 1 });
orderSchema.index({ siteId: 1, createdAt: -1 });

// Methods
orderSchema.methods.updateStatus = function (newStatus: OrderStatus) {
	this.status = newStatus;

	switch (newStatus) {
		case 'confirmed':
			this.confirmedAt = new Date();
			break;
		case 'shipped':
			this.shippedAt = new Date();
			break;
		case 'delivered':
			this.deliveredAt = new Date();
			break;
		case 'cancelled':
			this.cancelledAt = new Date();
			break;
	}

	return this.save();
};

orderSchema.methods.updatePaymentStatus = function (newStatus: PaymentStatus) {
	this.paymentStatus = newStatus;

	if (newStatus === 'paid') {
		this.paidAt = new Date();
	}

	return this.save();
};

// Generate order number
orderSchema.pre('save', async function (next) {
	if (this.isNew && !this.orderNumber) {
		const date = new Date();
		const year = date.getFullYear();
		const month = String(date.getMonth() + 1).padStart(2, '0');
		const day = String(date.getDate()).padStart(2, '0');

		// Count orders for today
		const todayOrders = await mongoose.model('Order').countDocuments({
			siteId: this.siteId,
			createdAt: {
				$gte: new Date(year, date.getMonth(), date.getDate()),
				$lt: new Date(year, date.getMonth(), date.getDate() + 1),
			},
		});

		const orderNumber = `${year}${month}${day}-${String(todayOrders + 1).padStart(4, '0')}`;
		this.orderNumber = orderNumber;
	}
	next();
});

export const Order = mongoose.model<IOrder>('Order', orderSchema);
