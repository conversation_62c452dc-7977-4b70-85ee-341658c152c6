import { t } from 'elysia';

// Create Comment Schema
export const createCommentSchema = t.Object({
	type: t.Union(
		[
			t.Literal('product', { error: 'ต้องเป็น product เท่านั้น' }),
			t.Literal('review', { error: 'ต้องเป็น review เท่านั้น' }),
			t.Literal('article', { error: 'ต้องเป็น article เท่านั้น' }),
			t.Literal('general', { error: 'ต้องเป็น general เท่านั้น' }),
		],
		{ error: 'type ไม่ถูกต้อง' }
	),
	targetId: t.String({ minLength: 1, error: 'targetId ต้องไม่ว่าง' }),
	parentId: t.Optional(t.String({ error: 'parentId ต้องเป็นข้อความ' })),
	content: t.String({ minLength: 1, maxLength: 2000, error: 'content ต้องมีความยาว 1-2000 ตัวอักษร' }),
	images: t.Optional(
		t.Array(t.String({ error: 'images ต้องเป็น array ของข้อความ' }), { error: 'images ต้องเป็น array ของข้อความ' })
	),
});

// Update Comment Schema
export const updateCommentSchema = t.Object({
	content: t.String({ minLength: 1, maxLength: 2000, error: 'content ต้องมีความยาว 1-2000 ตัวอักษร' }),
	images: t.Optional(
		t.Array(t.String({ error: 'images ต้องเป็น array ของข้อความ' }), { error: 'images ต้องเป็น array ของข้อความ' })
	),
});

// Comment Filter Schema
export const commentFilterSchema = t.Object({
	type: t.Optional(
		t.Union(
			[
				t.Literal('product', { error: 'ต้องเป็น product เท่านั้น' }),
				t.Literal('review', { error: 'ต้องเป็น review เท่านั้น' }),
				t.Literal('article', { error: 'ต้องเป็น article เท่านั้น' }),
				t.Literal('general', { error: 'ต้องเป็น general เท่านั้น' }),
			],
			{ error: 'type ไม่ถูกต้อง' }
		)
	),
	isVerified: t.Optional(t.Boolean({ error: 'isVerified ต้องเป็น true หรือ false เท่านั้น' })),
	sortBy: t.Optional(
		t.Union(
			[
				t.Literal('newest', { error: 'ต้องเป็น newest เท่านั้น' }),
				t.Literal('oldest', { error: 'ต้องเป็น oldest เท่านั้น' }),
				t.Literal('likes', { error: 'ต้องเป็น likes เท่านั้น' }),
				t.Literal('replies', { error: 'ต้องเป็น replies เท่านั้น' }),
			],
			{ error: 'sortBy ไม่ถูกต้อง' }
		)
	),
	page: t.Optional(t.Number({ minimum: 1, error: 'page ต้องเป็นตัวเลขและมากกว่า 0' })),
	limit: t.Optional(t.Number({ minimum: 1, maximum: 50, error: 'limit ต้องเป็นตัวเลข 1-50' })),
});

// Comment Response Schema
export const commentResponseSchema = t.Object({
	success: t.Boolean({ error: 'success ต้องเป็น true หรือ false เท่านั้น' }),
	message: t.String({ error: 'message ต้องเป็นข้อความ' }),
	statusMessage: t.String({ error: 'statusMessage ต้องเป็นข้อความ' }),
	timestamp: t.String({ error: 'timestamp ต้องเป็นข้อความ' }),
	data: t.Optional(t.Any()),
});

// Comment List Response Schema
export const commentListResponseSchema = t.Object({
	success: t.Boolean({ error: 'success ต้องเป็น true หรือ false เท่านั้น' }),
	message: t.String({ error: 'message ต้องเป็นข้อความ' }),
	statusMessage: t.String({ error: 'statusMessage ต้องเป็นข้อความ' }),
	timestamp: t.String({ error: 'timestamp ต้องเป็นข้อความ' }),
	data: t.Array(t.Any()),
	total: t.Number({ error: 'total ต้องเป็นตัวเลข' }),
	page: t.Number({ error: 'page ต้องเป็นตัวเลข' }),
	limit: t.Number({ error: 'limit ต้องเป็นตัวเลข' }),
});

// Comment Tree Response Schema
export const commentTreeResponseSchema = t.Object({
	success: t.Boolean({ error: 'success ต้องเป็น true หรือ false เท่านั้น' }),
	message: t.String({ error: 'message ต้องเป็นข้อความ' }),
	statusMessage: t.String({ error: 'statusMessage ต้องเป็นข้อความ' }),
	timestamp: t.String({ error: 'timestamp ต้องเป็นข้อความ' }),
	data: t.Array(t.Any()),
});

// Comment Stats Response Schema
export const commentStatsResponseSchema = t.Object({
	success: t.Boolean({ error: 'success ต้องเป็น true หรือ false เท่านั้น' }),
	message: t.String({ error: 'message ต้องเป็นข้อความ' }),
	statusMessage: t.String({ error: 'statusMessage ต้องเป็นข้อความ' }),
	timestamp: t.String({ error: 'timestamp ต้องเป็นข้อความ' }),
	data: t.Object({
		totalComments: t.Number({ error: 'totalComments ต้องเป็นตัวเลข' }),
		todayComments: t.Number({ error: 'todayComments ต้องเป็นตัวเลข' }),
		monthlyComments: t.Number({ error: 'monthlyComments ต้องเป็นตัวเลข' }),
		pendingComments: t.Number({ error: 'pendingComments ต้องเป็นตัวเลข' }),
		verifiedComments: t.Number({ error: 'verifiedComments ต้องเป็นตัวเลข' }),
		totalLikes: t.Number({ error: 'totalLikes ต้องเป็นตัวเลข' }),
	}),
});
