import mongoose, { Schema, type Document } from 'mongoose';
import { generateFileId } from '../../core/utils/idGenerator';

export interface ICategory extends Document {
	_id: string;
	target: 'product' | 'category' | 'page' | 'brand' | 'blog' | 'news';
	siteId: string;
	name: string;
	cover?: string;
	parentId?: string;
	createdAt: Date;
	updatedAt: Date;
}

const categorySchema = new Schema<ICategory>(
	{
		_id: { type: String, default: () => generateFileId(5) },
		target: {
			type: String,
			enum: ['product', 'category', 'page', 'brand', 'blog', 'news'] as const,
			default: 'product',
		},
		siteId: { type: String, required: true, index: true },
		name: { type: String, required: true },
		cover: { type: String, default: null },
		parentId: { type: String, default: null },
	},
	{
		timestamps: true,
		id: false,
		versionKey: false,
		toJSON: {
			virtuals: true,
		},
		toObject: {
			virtuals: true,
		},
	}
);

categorySchema.index({ siteId: 1, name: 1 });

export const Category = mongoose.model<ICategory>('Category', categorySchema);
