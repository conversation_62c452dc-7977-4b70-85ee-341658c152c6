import mongoose, { Schema, type Document } from 'mongoose';
import { generateFileId } from '@/core/utils/idGenerator';

export interface IType extends Document {
	_id: string;
	target: 'product' | 'category' | 'page' | 'brand' | 'blog' | 'news';
	siteId: string;
	name: string;
	cover?: string;
	parentId?: string;
	createdAt: Date;
	updatedAt: Date;
}

const typeSchema = new Schema<IType>(
	{
		_id: { type: String, default: () => generateFileId(5) },
		siteId: { type: String, required: true, index: true },
		name: { type: String, required: true },
		target: {
			type: String,
			enum: ['product', 'category', 'page', 'brand', 'blog', 'news'] as const,
			default: 'product',
		},
		cover: { type: String, default: null },
		parentId: { type: String, default: null },
	},
	{
		timestamps: true,
		id: false,
		versionKey: false,
		toJSON: {
			virtuals: true,
		},
		toObject: {
			virtuals: true,
		},
	}
);

typeSchema.index({ siteId: 1, name: 1 });

export const Type = mongoose.model<IType>('Type', typeSchema);
