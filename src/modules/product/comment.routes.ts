import { Elysia } from 'elysia';
import { createComment, getCommentsByTarget, getCommentTree, getCommentsByUser, getCommentById, updateComment, deleteComment, likeComment, unlikeComment, dislikeComment, undislikeComment, reportComment, approveComment, rejectComment, markCommentAsSpam, getCommentStats } from './comment.service';
import { userAuthPlugin } from '@/core/plugins/auth';
import { HttpError } from '@/core/utils/error';
import { createCommentSchema, updateCommentSchema, commentFilterSchema, commentResponseSchema, commentListResponseSchema, commentTreeResponseSchema, commentStatsResponseSchema } from './comment.schema';
export const commentRoutes = new Elysia({ prefix: '/comment' })
	.use(userAuthPlugin)

	// สร้างคอมเมนต์ใหม่
	.post(
		'/create',
		async ({ body, user }: any) => {
			const { siteId, type, targetId, parentId, content, images } = body;
			const commentData = {
				siteId,
				type,
				targetId,
				parentId,
				userId: user._id,
				userName: user.name || user.email,
				userEmail: user.email,
				userAvatar: user.avatar,
				content,
				images,
			};

			const result = await createComment(commentData);
			return {
				...result,
				statusMessage: 'สำเร็จ!',
				timestamp: new Date().toISOString(),
			};
		},
		{
			body: createCommentSchema,
			response: commentResponseSchema,
		}
	)

	// ดึงคอมเมนต์ของ target
	.get(
		'/target/:type/:targetId',
		async ({ params, query }: any) => {
			const { type, targetId } = params;
			const { siteId } = query;
			const filter: any = {};

			if (query.isVerified !== undefined) filter.isVerified = query.isVerified === 'true';

			let sortQuery: any = { createdAt: -1 };
			if (query.sortBy) {
				switch (query.sortBy) {
					case 'newest':
						sortQuery = { createdAt: -1 };
						break;
					case 'oldest':
						sortQuery = { createdAt: 1 };
						break;
					case 'likes':
						_sortQuery = { likes: -1, createdAt: -1 };
						break;
					case 'replies':
						_sortQuery = { replies: -1, createdAt: -1 };
						break;
				}
			}

			const page = parseInt(query.page) || 1;
			const limit = parseInt(query.limit) || 20;
			const skip = (page - 1) * limit;

			const comments = await getCommentsByTarget(siteId, type, targetId, filter);
			const total = comments.length;
			const paginatedComments = comments.slice(skip, skip + limit);

			return {
				success: true,
				message: 'ดึงคอมเมนต์สำเร็จ',
				statusMessage: 'สำเร็จ!',
				timestamp: new Date().toISOString(),
				data: paginatedComments,
				total,
				page,
				limit,
			};
		},
		{
			query: commentFilterSchema,
			response: commentListResponseSchema,
		}
	)

	// ดึงคอมเมนต์แบบ tree structure
	.get(
		'/tree/:type/:targetId',
		async ({ params, query }: any) => {
			const { type, targetId } = params;
			const { siteId } = query;

			const commentTree = await getCommentTree(siteId, type, targetId);

			return {
				success: true,
				message: 'ดึงคอมเมนต์แบบ tree สำเร็จ',
				statusMessage: 'สำเร็จ!',
				timestamp: new Date().toISOString(),
				data: commentTree,
			};
		},
		{
			response: commentTreeResponseSchema,
		}
	)

	// ดึงคอมเมนต์ของผู้ใช้
	.get(
		'/my-comments',
		async ({ user, query }: any) => {
			const { siteId } = query;
			if (!siteId) {
				throw new HttpError(400, 'กรุณาระบุ siteId');
			}

			const comments = await getCommentsByUser(user._id, siteId);

			return {
				success: true,
				message: 'ดึงคอมเมนต์ของผู้ใช้สำเร็จ',
				statusMessage: 'สำเร็จ!',
				timestamp: new Date().toISOString(),
				data: comments,
			};
		},
		{
			response: commentResponseSchema,
		}
	)

	// ดึงข้อมูลคอมเมนต์เฉพาะ
	.get(
		'/:commentId',
		async ({ params }: any) => {
			const { commentId } = params;
			const comment = await getCommentById(commentId);

			return {
				success: true,
				message: 'ดึงข้อมูลคอมเมนต์สำเร็จ',
				statusMessage: 'สำเร็จ!',
				timestamp: new Date().toISOString(),
				data: comment,
			};
		},
		{
			response: commentResponseSchema,
		}
	)

	// อัปเดตคอมเมนต์
	.put(
		'/:commentId',
		async ({ params, body, user }: any) => {
			const { commentId } = params;
			const comment = await getCommentById(commentId);

			// ตรวจสอบว่าเป็นเจ้าของคอมเมนต์
			if (comment.userId !== user._id) {
				throw new HttpError(403, 'ไม่มีสิทธิ์แก้ไขคอมเมนต์นี้');
			}

			const result = await updateComment(commentId, body);
			return {
				...result,
				statusMessage: 'สำเร็จ!',
				timestamp: new Date().toISOString(),
			};
		},
		{
			body: updateCommentSchema,
			response: commentResponseSchema,
		}
	)

	// ลบคอมเมนต์
	.delete(
		'/:commentId',
		async ({ params, user }: any) => {
			const { commentId } = params;

			const result = await deleteComment(commentId, user._id);
			return {
				...result,
				statusMessage: 'สำเร็จ!',
				timestamp: new Date().toISOString(),
			};
		},
		{
			response: commentResponseSchema,
		}
	)

	// กดไลค์คอมเมนต์
	.post(
		'/:commentId/like',
		async ({ params, user }: any) => {
			const { commentId } = params;

			const result = await likeComment(commentId, user._id);
			return {
				...result,
				statusMessage: 'สำเร็จ!',
				timestamp: new Date().toISOString(),
			};
		},
		{
			response: commentResponseSchema,
		}
	)

	// ยกเลิกการไลค์
	.delete(
		'/:commentId/like',
		async ({ params, user }: any) => {
			const { commentId } = params;

			const result = await unlikeComment(commentId, user._id);
			return {
				...result,
				statusMessage: 'สำเร็จ!',
				timestamp: new Date().toISOString(),
			};
		},
		{
			response: commentResponseSchema,
		}
	)

	// กดดิสไลค์คอมเมนต์
	.post(
		'/:commentId/dislike',
		async ({ params, user }: any) => {
			const { commentId } = params;

			const result = await dislikeComment(commentId, user._id);
			return {
				...result,
				statusMessage: 'สำเร็จ!',
				timestamp: new Date().toISOString(),
			};
		},
		{
			response: commentResponseSchema,
		}
	)

	// ยกเลิกการดิสไลค์
	.delete(
		'/:commentId/dislike',
		async ({ params, user }: any) => {
			const { commentId } = params;

			const result = await undislikeComment(commentId, user._id);
			return {
				...result,
				statusMessage: 'สำเร็จ!',
				timestamp: new Date().toISOString(),
			};
		},
		{
			response: commentResponseSchema,
		}
	)

	// รายงานคอมเมนต์
	.post(
		'/:commentId/report',
		async ({ params, user }: any) => {
			const { commentId } = params;

			const result = await reportComment(commentId, user._id);
			return {
				...result,
				statusMessage: 'สำเร็จ!',
				timestamp: new Date().toISOString(),
			};
		},
		{
			response: commentResponseSchema,
		}
	)

	// อนุมัติคอมเมนต์ (Admin)
	.put(
		'/:commentId/approve',
		async ({ params }: any) => {
			const { commentId } = params;

			const result = await approveComment(commentId);
			return {
				...result,
				statusMessage: 'สำเร็จ!',
				timestamp: new Date().toISOString(),
			};
		},
		{
			response: commentResponseSchema,
		}
	)

	// ปฏิเสธคอมเมนต์ (Admin)
	.put(
		'/:commentId/reject',
		async ({ params }: any) => {
			const { commentId } = params;

			const result = await rejectComment(commentId);
			return {
				...result,
				statusMessage: 'สำเร็จ!',
				timestamp: new Date().toISOString(),
			};
		},
		{
			response: commentResponseSchema,
		}
	)

	// ทำเครื่องหมายเป็นสแปม (Admin)
	.put(
		'/:commentId/spam',
		async ({ params }: any) => {
			const { commentId } = params;

			const result = await markCommentAsSpam(commentId);
			return {
				...result,
				statusMessage: 'สำเร็จ!',
				timestamp: new Date().toISOString(),
			};
		},
		{
			response: commentResponseSchema,
		}
	)

	// สถิติคอมเมนต์
	.get(
		'/stats/:siteId',
		async ({ params }: any) => {
			const { siteId } = params;
			const stats = await getCommentStats(siteId);

			return {
				success: true,
				message: 'ดึงสถิติคอมเมนต์สำเร็จ',
				statusMessage: 'สำเร็จ!',
				timestamp: new Date().toISOString(),
				data: stats,
			};
		},
		{
			response: commentStatsResponseSchema,
		}
	);
