import { Shipping } from './shipping.model';
import { Order } from '../order/order.model';
import { Product } from '../product/product.model';
import { HttpError } from '@/core/utils/error';

// Shipping Service
export async function createShipping(shippingData: {
	siteId: string;
	orderId: string;
	shippingMethod: ShippingMethod;
	fromAddress: any;
	toAddress: any;
	packageDetails: any;
	insuranceCost?: number;
}) {
	try {
		const { siteId, orderId, shippingMethod, fromAddress, toAddress, packageDetails, insuranceCost = 0 } = shippingData;

		// ตรวจสอบว่าออเดอร์มีอยู่จริง
		const order = await Order.findById(orderId);
		if (!order) {
			throw new HttpError(404, 'ไม่พบออเดอร์');
		}

		// ตรวจสอบว่ามี shipping record นี้แล้วหรือไม่
		const existingShipping = await Shipping.findOne({ siteId, orderId });
		if (existingShipping) {
			throw new HttpError(400, 'มี shipping record นี้แล้ว');
		}

		// คำนวณน้ำหนักรวม
		let totalWeight = 0;
		for (const item of packageDetails.items) {
			const product = await Product.findById(item.productId);
			if (product) {
				totalWeight += ((product as any).weight || 0.5) * item.quantity;
			}
		}

		const shipping = await Shipping.create({
			siteId,
			orderId,
			shippingMethod,
			fromAddress,
			toAddress,
			packageDetails: {
				...packageDetails,
				weight: totalWeight,
			},
			insuranceCost,
		});

		// คำนวณค่าจัดส่ง
		await (shipping as any).calculateShippingCost();

		return {
			success: true,
			message: 'สร้าง shipping record สำเร็จ',
			data: shipping,
		};
	} catch (err: any) {
		console.error('Error in createShipping:', err);
		if (err instanceof HttpError) throw err;
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะสร้าง shipping record');
	}
}

export async function getShippingByOrder(orderId: string, siteId: string) {
	try {
		const shipping = await (Shipping as any).getShippingByOrder(orderId, siteId);
		if (!shipping) {
			throw new HttpError(404, 'ไม่พบ shipping record');
		}
		return shipping;
	} catch (err: any) {
		console.error('Error in getShippingByOrder:', err);
		if (err instanceof HttpError) throw err;
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงข้อมูล shipping');
	}
}

export async function getShippingByTracking(trackingNumber: string, siteId: string) {
	try {
		const shipping = await (Shipping as any).getShippingByTracking(trackingNumber, siteId);
		if (!shipping) {
			throw new HttpError(404, 'ไม่พบ shipping record');
		}
		return shipping;
	} catch (err: any) {
		console.error('Error in getShippingByTracking:', err);
		if (err instanceof HttpError) throw err;
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงข้อมูล shipping');
	}
}

export async function updateShippingStatus(shippingId: string, newStatus: ShippingStatus) {
	try {
		const shipping = await Shipping.findById(shippingId);
		if (!shipping) {
			throw new HttpError(404, 'ไม่พบ shipping record');
		}

		await (shipping as any).updateStatus(newStatus);

		return {
			success: true,
			message: 'อัปเดตสถานะ shipping สำเร็จ',
			data: shipping,
		};
	} catch (err: any) {
		console.error('Error in updateShippingStatus:', err);
		if (err instanceof HttpError) throw err;
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะอัปเดตสถานะ shipping');
	}
}

export async function addTrackingEvent(shippingId: string, trackingEvent: ITrackingEvent) {
	try {
		const shipping = await Shipping.findById(shippingId);
		if (!shipping) {
			throw new HttpError(404, 'ไม่พบ shipping record');
		}

		await (shipping as any).addTrackingEvent(trackingEvent);

		return {
			success: true,
			message: 'เพิ่ม tracking event สำเร็จ',
			data: shipping,
		};
	} catch (err: any) {
		console.error('Error in addTrackingEvent:', err);
		if (err instanceof HttpError) throw err;
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะเพิ่ม tracking event');
	}
}

export async function updateShippingInfo(
	shippingId: string,
	updateData: {
		trackingNumber?: string;
		carrier?: string;
		estimatedDelivery?: Date;
		shippingMethod?: ShippingMethod;
		insuranceCost?: number;
	}
) {
	try {
		const shipping = await Shipping.findById(shippingId);
		if (!shipping) {
			throw new HttpError(404, 'ไม่พบ shipping record');
		}

		// อัปเดตข้อมูล
		Object.assign(shipping, updateData);

		// คำนวณค่าจัดส่งใหม่ถ้าจำเป็น
		if (updateData.shippingMethod || updateData.insuranceCost !== undefined) {
			await (shipping as any).calculateShippingCost();
		} else {
			await shipping.save();
		}

		return {
			success: true,
			message: 'อัปเดตข้อมูล shipping สำเร็จ',
			data: shipping,
		};
	} catch (err: any) {
		console.error('Error in updateShippingInfo:', err);
		if (err instanceof HttpError) throw err;
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะอัปเดตข้อมูล shipping');
	}
}

export async function getShippingByStatus(siteId: string, status: ShippingStatus) {
	try {
		const shipping = await Shipping.find({ siteId, status }).sort({ createdAt: -1 });
		return shipping;
	} catch (err: any) {
		console.error('Error in getShippingByStatus:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง shipping records');
	}
}

export async function calculateShippingCost(shippingData: {
	weight: number;
	shippingMethod: ShippingMethod;
	insuranceCost?: number;
	distance?: number;
}) {
	try {
		const { weight, shippingMethod, insuranceCost = 0, distance = 0 } = shippingData;

		// คำนวณค่าจัดส่งพื้นฐาน
		let baseCost = weight * 10; // 10 บาทต่อกิโลกรัม

		// คำนวณตามระยะทาง
		if (distance > 0) {
			baseCost += distance * 0.5; // 0.5 บาทต่อกิโลเมตร
		}

		// คำนวณตามวิธีการจัดส่ง
		let methodMultiplier = 1;
		switch (shippingMethod) {
			case 'standard':
				methodMultiplier = 1;
				break;
			case 'express':
				methodMultiplier = 1.5;
				break;
			case 'overnight':
				methodMultiplier = 2;
				break;
			case 'pickup':
				methodMultiplier = 0.5;
				break;
			case 'local':
				methodMultiplier = 0.8;
				break;
		}

		const shippingCost = baseCost * methodMultiplier;
		const totalCost = shippingCost + insuranceCost;

		return {
			shippingCost,
			insuranceCost,
			totalCost,
			estimatedDays: getEstimatedDays(shippingMethod),
		};
	} catch (err: any) {
		console.error('Error in calculateShippingCost:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะคำนวณค่าจัดส่ง');
	}
}

function getEstimatedDays(shippingMethod: ShippingMethod): number {
	switch (shippingMethod) {
		case 'standard':
			return 3;
		case 'express':
			return 2;
		case 'overnight':
			return 1;
		case 'pickup':
			return 0;
		case 'local':
			return 1;
		default:
			return 3;
	}
}

// Analytics functions
export async function getShippingStats(siteId: string) {
	try {
		const [totalShipments, pendingShipments, shippedShipments, deliveredShipments, totalCost] = await Promise.all([
			Shipping.countDocuments({ siteId }),
			Shipping.countDocuments({ siteId, status: 'pending' }),
			Shipping.countDocuments({ siteId, status: 'shipped' }),
			Shipping.countDocuments({ siteId, status: 'delivered' }),
			Shipping.aggregate([{ $match: { siteId } }, { $group: { _id: null, total: { $sum: '$totalCost' } } }]),
		]);

		return {
			totalShipments,
			pendingShipments,
			shippedShipments,
			deliveredShipments,
			totalCost: totalCost[0]?.total || 0,
		};
	} catch (err: any) {
		console.error('Error in getShippingStats:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงสถิติ shipping');
	}
}

export async function getShippingByDateRange(siteId: string, startDate: Date, endDate: Date) {
	try {
		const shipments = await Shipping.find({
			siteId,
			createdAt: { $gte: startDate, $lte: endDate },
		}).sort({ createdAt: -1 });

		return shipments;
	} catch (err: any) {
		console.error('Error in getShippingByDateRange:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงข้อมูล shipping');
	}
}
