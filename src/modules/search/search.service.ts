import { SearchIndex, SearchQuery, SearchSuggestion, SearchFilter } from './search.model';
import { Product } from '@/modules/product/product.model';
import { Category } from '@/modules/product/category.model';
import { HttpError } from '@/core/utils/error';
import { searchCache } from './search.cache';
import { ThaiTextProcessor, SearchAnalytics, AutocompleteEngine } from './search.utils';

// Search Service
export async function createSearchIndex(siteId: string, indexData: any) {
	try {
		const index = await SearchIndex.create({
			siteId,
			...indexData,
		});

		return index;
	} catch (err: any) {
		console.error('Error in createSearchIndex:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะสร้าง search index');
	}
}

export async function updateSearchIndex(siteId: string, entityType: string, entityId: string, indexData: any) {
	try {
		const index = await SearchIndex.findOneAndUpdate(
			{ siteId, entityType, entityId },
			{ $set: indexData },
			{ new: true, upsert: true }
		);

		return index;
	} catch (err: any) {
		console.error('Error in updateSearchIndex:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะอัปเดต search index');
	}
}

export async function deleteSearchIndex(siteId: string, entityType: string, entityId: string) {
	try {
		const _index = await SearchIndex.findOneAndDelete({
			siteId,
			entityType,
			entityId,
		});

		return { message: 'ลบ search index สำเร็จ' };
	} catch (err: any) {
		console.error('Error in deleteSearchIndex:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะลบ search index');
	}
}

export async function search(
	siteId: string,
	query: string,
	filters: any = {},
	sort: any = {},
	page: number = 1,
	limit: number = 20,
	userId?: string
) {
	try {
		// ตรวจสอบ siteId ก่อน
		if (!siteId) {
			throw new HttpError(400, 'siteId is required');
		}

		// Check cache first
		const cachedResults = await searchCache.getCachedSearchResults(siteId, query, { ...filters, sort, page, limit });
		if (cachedResults) {
			await SearchAnalytics.trackSearch(siteId, query, cachedResults, userId);
			return cachedResults;
		}

		// Process Thai query
		const processedQuery = ThaiTextProcessor.processQuery(query);

		const searchQuery: any = { siteId };

		// Enhanced search with Thai processing - แยก text search และ regex search
		if (query) {
			const searchTerms = [
				processedQuery.processed,
				...processedQuery.synonyms,
				...processedQuery.fuzzyVariants.slice(0, 3), // Limit fuzzy variants
			].filter(term => term && term.length > 1);

			if (searchTerms.length > 0) {
				// ใช้ regex search แทน text search เพื่อหลีกเลี่ยงปัญหา execution plan
				searchQuery.$or = [
					{ title: { $regex: processedQuery.original, $options: 'i' } },
					{ 'searchableFields.name': { $regex: processedQuery.original, $options: 'i' } },
					{ tags: { $regex: processedQuery.original, $options: 'i' } },
					{ content: { $regex: processedQuery.original, $options: 'i' } },
				];
			}
		}

		// Apply filters with better performance
		if (filters.category && filters.category.length > 0) {
			searchQuery['metadata.category'] = { $in: filters.category };
		}
		if (filters.brand && filters.brand.length > 0) {
			searchQuery['metadata.brand'] = { $in: filters.brand };
		}
		if (filters.priceRange) {
			searchQuery['metadata.price'] = {
				$gte: filters.priceRange.min || 0,
				$lte: filters.priceRange.max || Number.MAX_SAFE_INTEGER,
			};
		}
		if (filters.rating) {
			searchQuery['metadata.rating'] = { $gte: filters.rating };
		}
		if (filters.availability) {
			if (filters.availability === 'in_stock') {
				searchQuery['metadata.stock'] = { $gt: 0 };
			} else if (filters.availability === 'out_of_stock') {
				searchQuery['metadata.stock'] = { $lte: 0 };
			}
		}
		if (filters.status) {
			searchQuery['metadata.status'] = filters.status;
		}

		// Enhanced sorting - ลบ textScore ออกเพราะไม่ได้ใช้ $text search
		const sortOptions: any = {};
		if (sort.field && sort.order) {
			sortOptions[sort.field] = sort.order === 'asc' ? 1 : -1;
		} else if (query) {
			// เรียงตาม popularity และ lastUpdated แทน textScore
			sortOptions.popularity = -1;
			sortOptions.lastUpdated = -1;
		} else {
			sortOptions.popularity = -1;
			sortOptions.lastUpdated = -1;
		}

		const skip = (page - 1) * limit;

		// Execute search with better performance
		const [results, total] = await Promise.all([
			SearchIndex.find(searchQuery).sort(sortOptions).skip(skip).limit(limit).lean(), // Use lean() for better performance
			SearchIndex.countDocuments(searchQuery),
		]);

		// Apply custom relevance scoring for Thai text
		let scoredResults = results;
		if (query && results.length > 0) {
			scoredResults = results
				.map(result => ({
					...result,
					relevanceScore: ThaiTextProcessor.calculateRelevanceScore(query, result),
				}))
				.sort((a, b) => b.relevanceScore - a.relevanceScore);
		}

		// Generate suggestions and filters
		const [suggestions, availableFilters] = await Promise.all([
			generateSearchSuggestions(siteId, query, processedQuery),
			generateDynamicFilters(siteId, searchQuery, filters),
		]);

		const searchResults = {
			results: scoredResults,
			pagination: {
				page,
				limit,
				total,
				pages: Math.ceil(total / limit),
			},
			suggestions,
			filters: availableFilters,
			query: {
				original: query,
				processed: processedQuery.processed,
				tokens: processedQuery.tokens,
			},
		};

		// Cache results
		await searchCache.cacheSearchResults(siteId, query, { ...filters, sort, page, limit }, searchResults);

		// Log search query with enhanced data
		await logSearchQuery(siteId, query, filters, sort, total, results.length, userId, processedQuery);

		// Track analytics
		await SearchAnalytics.trackSearch(siteId, query, searchResults, userId);

		return searchResults;
	} catch (err: any) {
		console.error('Error in search:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะค้นหา');
	}
}

export async function logSearchQuery(
	siteId: string,
	query: string,
	filters: any,
	sort: any,
	total: number,
	returned: number,
	userId?: string,
	_processedQuery?: any
) {
	try {
		await SearchQuery.create({
			siteId,
			userId,
			query,
			filters,
			sortBy: sort.field || 'relevance',
			sortOrder: sort.order || 'desc',
			results: {
				total,
				returned,
				page: 1,
				limit: 20,
			},
			userAgent: 'API',
			ipAddress: '127.0.0.1',
		});
	} catch (err: any) {
		console.error('Error in logSearchQuery:', err);
	}
}

async function generateSearchSuggestions(siteId: string, query: string, _processedQuery: any): Promise<string[]> {
	try {
		// Check cache first
		const cached = await searchCache.getCachedSuggestions(siteId, query);
		if (cached.length > 0) {
			return cached;
		}

		// Get suggestions from multiple sources
		const [dbSuggestions, autocompleteSuggestions, popularCompletions] = await Promise.all([
			(SearchSuggestion as any).findSuggestions(siteId, query, 5),
			AutocompleteEngine.generateSuggestions(siteId, query, await SearchIndex.find({ siteId }).limit(100).lean()),
			getPopularCompletions(siteId, query),
		]);

		// Combine and deduplicate suggestions
		const allSuggestions = new Set([
			...dbSuggestions.map((s: any) => s.query),
			...autocompleteSuggestions,
			...popularCompletions,
		]);

		const suggestions = Array.from(allSuggestions)
			.filter(s => s.toLowerCase() !== query.toLowerCase())
			.slice(0, 8);

		// Cache suggestions
		await searchCache.cacheSuggestions(siteId, query, suggestions);

		return suggestions;
	} catch (err: any) {
		console.error('Error generating suggestions:', err);
		return [];
	}
}

async function generateDynamicFilters(siteId: string, searchQuery: any, _currentFilters: any): Promise<any> {
	try {
		// Get aggregated filter data
		const pipeline = [
			{ $match: searchQuery },
			{
				$group: {
					_id: null,
					categories: { $addToSet: '$metadata.category' },
					brands: { $addToSet: '$metadata.brand' },
					priceRange: {
						$push: {
							min: { $min: '$metadata.price' },
							max: { $max: '$metadata.price' },
						},
					},
					ratings: { $addToSet: '$metadata.rating' },
				},
			},
		];

		const [aggregated] = await SearchIndex.aggregate(pipeline);

		if (!aggregated) {
			return {
				categories: [],
				brands: [],
				priceRange: { min: 0, max: 0 },
				ratings: [],
			};
		}

		// Get category and brand counts
		const [categoryCounts, brandCounts] = await Promise.all([
			SearchIndex.aggregate([
				{ $match: searchQuery },
				{ $group: { _id: '$metadata.category', count: { $sum: 1 } } },
				{ $sort: { count: -1 } },
			]),
			SearchIndex.aggregate([
				{ $match: searchQuery },
				{ $group: { _id: '$metadata.brand', count: { $sum: 1 } } },
				{ $sort: { count: -1 } },
			]),
		]);

		return {
			categories: categoryCounts.filter(c => c._id).map(c => ({ name: c._id, count: c.count })),
			brands: brandCounts.filter(b => b._id).map(b => ({ name: b._id, count: b.count })),
			priceRange: {
				min: Math.min(...aggregated.priceRange.map((p: any) => p.min).filter(Boolean)) || 0,
				max: Math.max(...aggregated.priceRange.map((p: any) => p.max).filter(Boolean)) || 0,
			},
			ratings: aggregated.ratings
				.filter(Boolean)
				.sort((a: number, b: number) => b - a)
				.map((rating: number) => ({ rating, count: 0 })), // Count would need separate aggregation
		};
	} catch (err: any) {
		console.error('Error generating dynamic filters:', err);
		return {
			categories: [],
			brands: [],
			priceRange: { min: 0, max: 0 },
			ratings: [],
		};
	}
}

async function getPopularCompletions(siteId: string, query: string): Promise<string[]> {
	try {
		const recentSearches = await SearchQuery.find({
			siteId,
			query: { $regex: `^${query}`, $options: 'i' },
			createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }, // Last 30 days
		})
			.sort({ createdAt: -1 })
			.limit(50)
			.lean();

		return AutocompleteEngine.getPopularCompletions(siteId, query, recentSearches);
	} catch (err: any) {
		console.error('Error getting popular completions:', err);
		return [];
	}
}

export async function getSearchSuggestions(siteId: string, query: string, limit: number = 5) {
	try {
		const suggestions = await (SearchSuggestion as any).findSuggestions(siteId, query, limit);
		return suggestions;
	} catch (err: any) {
		console.error('Error in getSearchSuggestions:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงคำแนะนำการค้นหา');
	}
}

export async function createSearchSuggestion(siteId: string, suggestionData: any) {
	try {
		const suggestion = await SearchSuggestion.create({
			siteId,
			...suggestionData,
		});

		return suggestion;
	} catch (err: any) {
		console.error('Error in createSearchSuggestion:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะสร้างคำแนะนำการค้นหา');
	}
}

export async function updateSearchSuggestion(siteId: string, suggestionId: string, updates: any) {
	try {
		const suggestion = await SearchSuggestion.findOneAndUpdate(
			{ siteId, _id: suggestionId },
			{ $set: updates },
			{ new: true }
		);

		if (!suggestion) {
			throw new HttpError(404, 'ไม่พบคำแนะนำการค้นหา');
		}

		return suggestion;
	} catch (err: any) {
		console.error('Error in updateSearchSuggestion:', err);
		if (err instanceof HttpError) throw err;
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะอัปเดตคำแนะนำการค้นหา');
	}
}

export async function deleteSearchSuggestion(siteId: string, suggestionId: string) {
	try {
		const suggestion = await SearchSuggestion.findOneAndDelete({ siteId, _id: suggestionId });

		if (!suggestion) {
			throw new HttpError(404, 'ไม่พบคำแนะนำการค้นหา');
		}

		return { message: 'ลบคำแนะนำการค้นหาสำเร็จ' };
	} catch (err: any) {
		console.error('Error in deleteSearchSuggestion:', err);
		if (err instanceof HttpError) throw err;
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะลบคำแนะนำการค้นหา');
	}
}

export async function getSearchFilters(siteId: string) {
	try {
		const filters = await (SearchFilter as any).findActive(siteId);
		return filters;
	} catch (err: any) {
		console.error('Error in getSearchFilters:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงตัวกรองการค้นหา');
	}
}

export async function createSearchFilter(siteId: string, filterData: any) {
	try {
		const filter = await SearchFilter.create({
			siteId,
			...filterData,
		});

		return filter;
	} catch (err: any) {
		console.error('Error in createSearchFilter:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะสร้างตัวกรองการค้นหา');
	}
}

export async function updateSearchFilter(siteId: string, filterId: string, updates: any) {
	try {
		const filter = await SearchFilter.findOneAndUpdate({ siteId, _id: filterId }, { $set: updates }, { new: true });

		if (!filter) {
			throw new HttpError(404, 'ไม่พบตัวกรองการค้นหา');
		}

		return filter;
	} catch (err: any) {
		console.error('Error in updateSearchFilter:', err);
		if (err instanceof HttpError) throw err;
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะอัปเดตตัวกรองการค้นหา');
	}
}

export async function deleteSearchFilter(siteId: string, filterId: string) {
	try {
		const filter = await SearchFilter.findOneAndDelete({ siteId, _id: filterId });

		if (!filter) {
			throw new HttpError(404, 'ไม่พบตัวกรองการค้นหา');
		}

		return { message: 'ลบตัวกรองการค้นหาสำเร็จ' };
	} catch (err: any) {
		console.error('Error in deleteSearchFilter:', err);
		if (err instanceof HttpError) throw err;
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะลบตัวกรองการค้นหา');
	}
}

export async function getPopularSearches(siteId: string, limit: number = 10) {
	try {
		const searches = await (SearchQuery as any).findPopular(siteId, limit);
		return searches;
	} catch (err: any) {
		console.error('Error in getPopularSearches:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงการค้นหายอดนิยม');
	}
}

export async function getSearchStats(siteId: string, timeRange: { start: Date; end: Date }) {
	try {
		const stats = await SearchQuery.aggregate([
			{
				$match: {
					siteId,
					createdAt: { $gte: timeRange.start, $lte: timeRange.end },
				},
			},
			{
				$group: {
					_id: '$query',
					count: { $sum: 1 },
					avgResults: { $avg: '$results.total' },
					avgReturned: { $avg: '$results.returned' },
				},
			},
			{ $sort: { count: -1 } },
			{ $limit: 20 },
		]);

		return stats;
	} catch (err: any) {
		console.error('Error in getSearchStats:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงสถิติการค้นหา');
	}
}

export async function reindexAll(siteId: string) {
	try {
		// Reindex products
		const products = await Product.find({ siteId });
		for (const product of products) {
			await updateSearchIndex(siteId, 'product', product._id, {
				title: product.name,
				description: product.description,
				content: `${product.name} ${product.description} ${product.tags?.join(' ') || ''}`,
				tags: product.tags || [],
				metadata: {
					price: product.price,
					category: product.categoryId,
					brand: (product as any).brand || '',
					rating: (product as any).rating || 0,
					reviewCount: (product as any).reviewCount || 0,
					stock: product.stock || 0,
					status: (product as any).status || 'active',
				},
				searchableFields: {
					name: product.name,
					description: product.description,
					tags: (product as any).tags || [],
					attributes: (product as any).attributes || {},
				},
				popularity: (product as any).popularity || 0,
				lastUpdated: new Date(),
			});
		}

		// Reindex categories
		const categories = await Category.find({ siteId });
		for (const category of categories) {
			await updateSearchIndex(siteId, 'category', category._id, {
				title: category.name,
				description: (category as any).description || '',
				content: `${category.name} ${(category as any).description || ''}`,
				tags: (category as any).tags || [],
				metadata: {
					status: (category as any).status || 'active',
				},
				searchableFields: {
					name: category.name,
					description: (category as any).description || '',
					tags: (category as any).tags || [],
					attributes: {},
				},
				popularity: 0,
				lastUpdated: new Date(),
			});
		}

		return { message: 'Reindex สำเร็จ' };
	} catch (err: any) {
		console.error('Error in reindexAll:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะ reindex');
	}
}
