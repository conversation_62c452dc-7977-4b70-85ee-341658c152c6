import { EventEmitter } from 'events';
import { searchCache } from './search.cache';
import { SearchIndex, SearchQuery } from './search.model';
import { ThaiTextProcessor, AutocompleteEngine } from './search.utils';

export class RealTimeSearch extends EventEmitter {
	private static instance: RealTimeSearch;
	private searchSessions: Map<string, SearchSession> = new Map();
	private debounceTimers: Map<string, NodeJS.Timeout> = new Map();

	static getInstance(): RealTimeSearch {
		if (!RealTimeSearch.instance) {
			RealTimeSearch.instance = new RealTimeSearch();
		}
		return RealTimeSearch.instance;
	}

	async startSearchSession(sessionId: string, siteId: string, userId?: string): Promise<SearchSession> {
		const session = new SearchSession(sessionId, siteId, userId);
		this.searchSessions.set(sessionId, session);

		this.emit('session:started', { sessionId, siteId, userId });

		return session;
	}

	async endSearchSession(sessionId: string): Promise<void> {
		const session = this.searchSessions.get(sessionId);
		if (session) {
			await session.end();
			this.searchSessions.delete(sessionId);
			this.emit('session:ended', { sessionId });
		}
	}

	async handleTyping(sessionId: string, query: string): Promise<AutocompleteResult> {
		const session = this.searchSessions.get(sessionId);
		if (!session) {
			throw new Error('Search session not found');
		}

		// Clear previous debounce timer
		const existingTimer = this.debounceTimers.get(sessionId);
		if (existingTimer) {
			clearTimeout(existingTimer);
		}

		// Debounce the search
		return new Promise(resolve => {
			const timer = setTimeout(async () => {
				try {
					const result = await this.performAutocomplete(session, query);
					this.debounceTimers.delete(sessionId);
					resolve(result);
				} catch (error) {
					console.error('Autocomplete error:', error);
					resolve({
						suggestions: [],
						predictions: [],
						popularQueries: [],
						processingTime: 0,
					});
				}
			}, 150); // 150ms debounce

			this.debounceTimers.set(sessionId, timer);
		});
	}

	private async performAutocomplete(session: SearchSession, query: string): Promise<AutocompleteResult> {
		const startTime = Date.now();

		if (!query || query.length < 2) {
			return {
				suggestions: [],
				predictions: [],
				popularQueries: await this.getPopularQueries(session.siteId),
				processingTime: Date.now() - startTime,
			};
		}

		// Process Thai query
		const processed = ThaiTextProcessor.processQuery(query);

		// Get suggestions from multiple sources in parallel
		const [cachedSuggestions, instantSuggestions, predictions, popularQueries] = await Promise.all([
			searchCache.getCachedSuggestions(session.siteId, query),
			this.getInstantSuggestions(session.siteId, query, processed),
			this.getPredictions(session.siteId, query, processed),
			this.getPopularQueries(session.siteId, query),
		]);

		// Combine and rank suggestions
		const allSuggestions = new Set([...cachedSuggestions, ...instantSuggestions, ...predictions]);

		const rankedSuggestions = Array.from(allSuggestions)
			.filter(s => s.toLowerCase() !== query.toLowerCase())
			.slice(0, 8);

		// Cache the results
		if (rankedSuggestions.length > 0) {
			await searchCache.cacheSuggestions(session.siteId, query, rankedSuggestions);
		}

		// Track typing analytics
		session.trackTyping(query, rankedSuggestions.length);

		const result: AutocompleteResult = {
			suggestions: rankedSuggestions,
			predictions: predictions.slice(0, 5),
			popularQueries: popularQueries.slice(0, 5),
			processingTime: Date.now() - startTime,
		};

		this.emit('autocomplete:generated', {
			sessionId: session.id,
			query,
			result,
			processingTime: result.processingTime,
		});

		return result;
	}

	private async getInstantSuggestions(siteId: string, query: string, _processed: any): Promise<string[]> {
		try {
			// Search in indexed titles and names for instant matches
			const pipeline = [
				{
					$match: {
						siteId,
						$or: [
							{ title: { $regex: query, $options: 'i' } },
							{ 'searchableFields.name': { $regex: query, $options: 'i' } },
							{ tags: { $in: [new RegExp(query, 'i')] } },
						],
					},
				},
				{
					$project: {
						title: 1,
						'searchableFields.name': 1,
						tags: 1,
						popularity: 1,
					},
				},
				{ $sort: { popularity: -1 as -1 } },
				{ $limit: 20 },
			];

			const results = await SearchIndex.aggregate(pipeline);
			const suggestions = new Set<string>();

			for (const result of results) {
				if (result.title?.toLowerCase().includes(query.toLowerCase())) {
					suggestions.add(result.title);
				}
				if (result.searchableFields?.name?.toLowerCase().includes(query.toLowerCase())) {
					suggestions.add(result.searchableFields.name);
				}
				if (result.tags) {
					for (const tag of result.tags) {
						if (tag.toLowerCase().includes(query.toLowerCase())) {
							suggestions.add(tag);
						}
					}
				}
			}

			return Array.from(suggestions).slice(0, 10);
		} catch (error) {
			console.error('Error getting instant suggestions:', error);
			return [];
		}
	}

	private async getPredictions(siteId: string, query: string, _processed: any): Promise<string[]> {
		try {
			// Get query completions based on historical data
			const recentQueries = await SearchQuery.find({
				siteId,
				query: { $regex: `^${query}`, $options: 'i' },
				createdAt: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) }, // Last 7 days
			})
				.sort({ createdAt: -1 })
				.limit(100)
				.lean();

			return AutocompleteEngine.getPopularCompletions(siteId, query, recentQueries);
		} catch (error) {
			console.error('Error getting predictions:', error);
			return [];
		}
	}

	private async getPopularQueries(siteId: string, excludeQuery?: string): Promise<string[]> {
		try {
			const cached = await searchCache.getCachedPopularSearches(siteId);
			if (cached.length > 0) {
				return cached.filter(q => !excludeQuery || q.toLowerCase() !== excludeQuery.toLowerCase()).slice(0, 5);
			}

			const popular = await SearchQuery.aggregate([
				{
					$match: {
						siteId,
						createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }, // Last 24 hours
					},
				},
				{
					$group: {
						_id: '$query',
						count: { $sum: 1 },
						lastUsed: { $max: '$createdAt' },
					},
				},
				{ $sort: { count: -1, lastUsed: -1 } },
				{ $limit: 10 },
			]);

			const queries = popular.map(p => p._id);
			await searchCache.cachePopularSearches(siteId, queries);

			return queries.filter(q => !excludeQuery || q.toLowerCase() !== excludeQuery.toLowerCase()).slice(0, 5);
		} catch (error) {
			console.error('Error getting popular queries:', error);
			return [];
		}
	}
}

class SearchSession {
	public readonly id: string;
	public readonly siteId: string;
	public readonly userId?: string;
	public readonly startTime: Date;
	private queries: string[] = [];
	private typingEvents: TypingEvent[] = [];

	constructor(id: string, siteId: string, userId?: string) {
		this.id = id;
		this.siteId = siteId;
		this.userId = userId;
		this.startTime = new Date();
	}

	trackTyping(query: string, suggestionCount: number): void {
		this.typingEvents.push({
			query,
			suggestionCount,
			timestamp: new Date(),
		});
	}

	trackQuery(query: string): void {
		this.queries.push(query);
	}

	async end(): Promise<SessionAnalytics> {
		const endTime = new Date();
		const duration = endTime.getTime() - this.startTime.getTime();

		const analytics: SessionAnalytics = {
			sessionId: this.id,
			siteId: this.siteId,
			userId: this.userId,
			startTime: this.startTime,
			endTime,
			duration,
			totalQueries: this.queries.length,
			totalTypingEvents: this.typingEvents.length,
			uniqueQueries: new Set(this.queries).size,
			avgTypingSpeed: this.calculateAvgTypingSpeed(),
			queries: this.queries,
			typingPattern: this.analyzeTypingPattern(),
		};

		// Store analytics (could be sent to analytics service)
		console.log('Session Analytics:', analytics);

		return analytics;
	}

	private calculateAvgTypingSpeed(): number {
		if (this.typingEvents.length < 2) return 0;

		const intervals = [];
		for (let i = 1; i < this.typingEvents.length; i++) {
			const interval = this.typingEvents[i].timestamp.getTime() - this.typingEvents[i - 1].timestamp.getTime();
			intervals.push(interval);
		}

		return intervals.reduce((sum, interval) => sum + interval, 0) / intervals.length;
	}

	private analyzeTypingPattern(): TypingPattern {
		const pattern: TypingPattern = {
			avgQueryLength: 0,
			mostCommonWords: [],
			languageDistribution: { thai: 0, english: 0, mixed: 0 },
			typingSpeed: this.calculateAvgTypingSpeed(),
		};

		if (this.queries.length === 0) return pattern;

		// Calculate average query length
		pattern.avgQueryLength = this.queries.reduce((sum, q) => sum + q.length, 0) / this.queries.length;

		// Analyze language distribution
		for (const query of this.queries) {
			const thaiChars = (query.match(/[\u0E00-\u0E7F]/g) || []).length;
			const englishChars = (query.match(/[a-zA-Z]/g) || []).length;

			if (thaiChars > englishChars) {
				pattern.languageDistribution.thai++;
			} else if (englishChars > thaiChars) {
				pattern.languageDistribution.english++;
			} else {
				pattern.languageDistribution.mixed++;
			}
		}

		// Find most common words
		const wordCount = new Map<string, number>();
		for (const query of this.queries) {
			const words = query.toLowerCase().split(/\s+/);
			for (const word of words) {
				if (word.length > 2) {
					wordCount.set(word, (wordCount.get(word) || 0) + 1);
				}
			}
		}

		pattern.mostCommonWords = Array.from(wordCount.entries())
			.sort((a, b) => b[1] - a[1])
			.slice(0, 5)
			.map(([word]) => word);

		return pattern;
	}
}

interface AutocompleteResult {
	suggestions: string[];
	predictions: string[];
	popularQueries: string[];
	processingTime: number;
}

interface TypingEvent {
	query: string;
	suggestionCount: number;
	timestamp: Date;
}

interface SessionAnalytics {
	sessionId: string;
	siteId: string;
	userId?: string;
	startTime: Date;
	endTime: Date;
	duration: number;
	totalQueries: number;
	totalTypingEvents: number;
	uniqueQueries: number;
	avgTypingSpeed: number;
	queries: string[];
	typingPattern: TypingPattern;
}

interface TypingPattern {
	avgQueryLength: number;
	mostCommonWords: string[];
	languageDistribution: {
		thai: number;
		english: number;
		mixed: number;
	};
	typingSpeed: number;
}

export const realTimeSearch = RealTimeSearch.getInstance();
