import { Elysia, t } from 'elysia';
import * as searchService from './search.service';
import { userPlugin } from '@/core/middleware/checkUser';
import { realTimeSearch } from './search.realtime';
import { AdvancedSearchAnalytics } from './search.analytics';
import { SearchExperimentEngine } from './search.experiments';

// Type for store with siteId
interface _StoreWithSiteId {
	siteId: string;
}

// Helper function to get siteId from multiple sources
const getSiteId = (store: any, headers: any, query: any = {}): string => {
	return store.siteId || headers['x-site-id'] || query.siteId || 'default-site';
};

export const searchRoutes = new Elysia({ prefix: '/search' })
	.use(userPlugin)
	.get(
		'/',
		async ({ query, store, headers }: any) => {
			const { q, filters, page = 1, limit = 20 } = query;

			// ดึง siteId จากหลายแหล่ง
			const siteId = store.siteId || headers['x-site-id'] || query.siteId || 'default-site';

			const result = await searchService.search(siteId, q, filters, {}, Number(page), Number(limit));

			return {
				success: true,
				data: result,
			};
		},
		{
			query: t.Object({
				q: t.String(),
				page: t.Optional(t.String()),
				limit: t.Optional(t.String()),
				filters: t.Optional(t.String()), // JSON string
			}),
		}
	)
	.get(
		'/suggestions',
		async ({ query, store, headers }: any) => {
			const { q, limit = 5 } = query;

			const siteId = store.siteId || headers['x-site-id'] || query.siteId || 'default-site';
			const suggestions = await searchService.getSearchSuggestions(siteId, q, Number(limit));

			return {
				success: true,
				data: suggestions,
			};
		},
		{
			query: t.Object({
				q: t.String(),
				limit: t.Optional(t.String()),
			}),
		}
	)
	.post(
		'/suggestions',
		async ({ body, store, headers }: any) => {
			const siteId = getSiteId(store, headers);
			const suggestion = await searchService.createSearchSuggestion(siteId, body);

			return {
				success: true,
				data: suggestion,
			};
		},
		{
			body: t.Object({
				query: t.String(),
				suggestions: t.Array(t.String()),
				category: t.Optional(t.String()),
			}),
		}
	)
	.put(
		'/suggestions/:suggestionId',
		async ({ params, body, store, headers }: any) => {
			const { suggestionId } = params;

			const siteId = getSiteId(store, headers);
			const suggestion = await searchService.updateSearchSuggestion(siteId, suggestionId, body);

			return {
				success: true,
				data: suggestion,
			};
		},
		{
			params: t.Object({
				suggestionId: t.String(),
			}),
			body: t.Object({
				query: t.Optional(t.String()),
				suggestions: t.Optional(t.Array(t.String())),
				category: t.Optional(t.String()),
				isActive: t.Optional(t.Boolean()),
			}),
		}
	)
	.delete(
		'/suggestions/:suggestionId',
		async ({ params, store, headers }: any) => {
			const { suggestionId } = params;

			const siteId = getSiteId(store, headers);
			const result = await searchService.deleteSearchSuggestion(siteId, suggestionId);

			return {
				success: true,
				data: result,
			};
		},
		{
			params: t.Object({
				suggestionId: t.String(),
			}),
		}
	)
	.get('/filters', async ({ store, headers }: any) => {
		const siteId = getSiteId(store, headers);
		const filters = await searchService.getSearchFilters(siteId);

		return {
			success: true,
			data: filters,
		};
	})
	.post(
		'/filters',
		async ({ body, store, headers }: any) => {
			const siteId = getSiteId(store, headers);
			const filter = await searchService.createSearchFilter(siteId, body);

			return {
				success: true,
				data: filter,
			};
		},
		{
			body: t.Object({
				name: t.String(),
				type: t.Union([
					t.Literal('category'),
					t.Literal('brand'),
					t.Literal('price'),
					t.Literal('rating'),
					t.Literal('availability'),
					t.Literal('custom'),
				]),
				options: t.Array(
					t.Object({
						value: t.String(),
						label: t.String(),
						count: t.Optional(t.Number()),
					})
				),
				isActive: t.Optional(t.Boolean()),
				sortOrder: t.Optional(t.Number()),
			}),
		}
	)
	.put(
		'/filters/:filterId',
		async ({ params, body, store, headers }: any) => {
			const { filterId } = params;

			const siteId = getSiteId(store, headers);
			const filter = await searchService.updateSearchFilter(siteId, filterId, body);

			return {
				success: true,
				data: filter,
			};
		},
		{
			params: t.Object({
				filterId: t.String(),
			}),
			body: t.Object({
				name: t.Optional(t.String()),
				type: t.Optional(
					t.Union([
						t.Literal('category'),
						t.Literal('brand'),
						t.Literal('price'),
						t.Literal('rating'),
						t.Literal('availability'),
						t.Literal('custom'),
					])
				),
				options: t.Optional(
					t.Array(
						t.Object({
							value: t.String(),
							label: t.String(),
							count: t.Optional(t.Number()),
						})
					)
				),
				isActive: t.Optional(t.Boolean()),
				sortOrder: t.Optional(t.Number()),
			}),
		}
	)
	.delete(
		'/filters/:filterId',
		async ({ params, store, headers }: any) => {
			const { filterId } = params;

			const siteId = getSiteId(store, headers);
			const result = await searchService.deleteSearchFilter(siteId, filterId);

			return {
				success: true,
				data: result,
			};
		},
		{
			params: t.Object({
				filterId: t.String(),
			}),
		}
	)
	.get(
		'/popular',
		async ({ query, store, headers }: any) => {
			const { limit = 10 } = query;

			const siteId = getSiteId(store, headers, query);
			const searches = await searchService.getPopularSearches(siteId, Number(limit));

			return {
				success: true,
				data: searches,
			};
		},
		{
			query: t.Object({
				limit: t.Optional(t.String()),
			}),
		}
	)
	.get(
		'/stats',
		async ({ query, store, headers }: any) => {
			const { startDate, endDate } = query;

			const timeRange = {
				start: new Date(startDate),
				end: new Date(endDate),
			};

			const siteId = getSiteId(store, headers, query);
			const stats = await searchService.getSearchStats(siteId, timeRange);

			return {
				success: true,
				data: stats,
			};
		},
		{
			query: t.Object({
				startDate: t.String(),
				endDate: t.String(),
			}),
		}
	)
	.post(
		'/index',
		async ({ body, store, headers }: any) => {
			const siteId = getSiteId(store, headers);
			const index = await searchService.createSearchIndex(siteId, body);

			return {
				success: true,
				data: index,
			};
		},
		{
			body: t.Object({
				entityType: t.String(),
				entityId: t.String(),
				title: t.String(),
				description: t.Optional(t.String()),
				content: t.String(),
				tags: t.Array(t.String()),
				metadata: t.Object({
					price: t.Optional(t.Number()),
					category: t.Optional(t.String()),
					brand: t.Optional(t.String()),
					rating: t.Optional(t.Number()),
					reviewCount: t.Optional(t.Number()),
					stock: t.Optional(t.Number()),
					status: t.Optional(t.String()),
				}),
				searchableFields: t.Object({
					name: t.String(),
					description: t.Optional(t.String()),
					tags: t.Array(t.String()),
					attributes: t.Record(t.String(), t.Any()),
				}),
				popularity: t.Optional(t.Number()),
			}),
		}
	)
	.put(
		'/index/:entityType/:entityId',
		async ({ params, body, store, headers }: any) => {
			const { entityType, entityId } = params;

			const siteId = getSiteId(store, headers);
			const index = await searchService.updateSearchIndex(siteId, entityType, entityId, body);

			return {
				success: true,
				data: index,
			};
		},
		{
			params: t.Object({
				entityType: t.String(),
				entityId: t.String(),
			}),
			body: t.Object({
				title: t.Optional(t.String()),
				description: t.Optional(t.String()),
				content: t.Optional(t.String()),
				tags: t.Optional(t.Array(t.String())),
				metadata: t.Optional(
					t.Object({
						price: t.Optional(t.Number()),
						category: t.Optional(t.String()),
						brand: t.Optional(t.String()),
						rating: t.Optional(t.Number()),
						reviewCount: t.Optional(t.Number()),
						stock: t.Optional(t.Number()),
						status: t.Optional(t.String()),
					})
				),
				searchableFields: t.Optional(
					t.Object({
						name: t.Optional(t.String()),
						description: t.Optional(t.String()),
						tags: t.Optional(t.Array(t.String())),
						attributes: t.Optional(t.Record(t.String(), t.Any())),
					})
				),
				popularity: t.Optional(t.Number()),
			}),
		}
	)
	.delete(
		'/index/:entityType/:entityId',
		async ({ params, store, headers }: any) => {
			const { entityType, entityId } = params;

			const siteId = getSiteId(store, headers);
			const result = await searchService.deleteSearchIndex(siteId, entityType, entityId);

			return {
				success: true,
				data: result,
			};
		},
		{
			params: t.Object({
				entityType: t.String(),
				entityId: t.String(),
			}),
		}
	)
	.post('/reindex', async ({ store, headers }: any) => {
		const siteId = getSiteId(store, headers);
		const result = await searchService.reindexAll(siteId);

		return {
			success: true,
			data: result,
		};
	})

	// Real-time search and autocomplete
	.post(
		'/session/start',
		async ({ body, store, headers }: any) => {
			const { sessionId, userId } = body;
			const siteId = getSiteId(store, headers);
			const session = await realTimeSearch.startSearchSession(sessionId, siteId, userId);

			return {
				success: true,
				data: { sessionId: session.id },
			};
		},
		{
			body: t.Object({
				sessionId: t.String(),
				userId: t.Optional(t.String()),
			}),
		}
	)

	.post(
		'/session/:sessionId/end',
		async ({ params, _store }: any) => {
			const { sessionId } = params;
			await realTimeSearch.endSearchSession(sessionId);

			return {
				success: true,
				data: { message: 'Session ended' },
			};
		},
		{
			params: t.Object({
				sessionId: t.String(),
			}),
		}
	)

	.post(
		'/autocomplete',
		async ({ body, _store }: any) => {
			const { sessionId, query } = body;
			const result = await realTimeSearch.handleTyping(sessionId, query);

			return {
				success: true,
				data: result,
			};
		},
		{
			body: t.Object({
				sessionId: t.String(),
				query: t.String(),
			}),
		}
	)

	// Advanced Analytics
	.get(
		'/analytics/dashboard',
		async ({ query, store, headers }: any) => {
			const { startDate, endDate } = query;
			const timeRange = {
				start: new Date(startDate),
				end: new Date(endDate),
			};

			const siteId = getSiteId(store, headers, query);
			const dashboard = await AdvancedSearchAnalytics.generateDashboardData(siteId, timeRange);

			return {
				success: true,
				data: dashboard,
			};
		},
		{
			query: t.Object({
				startDate: t.String(),
				endDate: t.String(),
			}),
		}
	)

	.get(
		'/analytics/funnel',
		async ({ query, store, headers }: any) => {
			const { startDate, endDate } = query;
			const timeRange = {
				start: new Date(startDate),
				end: new Date(endDate),
			};

			const siteId = getSiteId(store, headers, query);
			const funnel = await AdvancedSearchAnalytics.getSearchFunnel(siteId, timeRange);

			return {
				success: true,
				data: funnel,
			};
		},
		{
			query: t.Object({
				startDate: t.String(),
				endDate: t.String(),
			}),
		}
	)

	.get(
		'/analytics/query/:query',
		async ({ params, query, store, headers }: any) => {
			const { query: searchQuery } = params;
			const { startDate, endDate } = query;
			const timeRange = {
				start: new Date(startDate),
				end: new Date(endDate),
			};

			const siteId = getSiteId(store, headers, query);
			const report = await AdvancedSearchAnalytics.getQueryPerformanceReport(siteId, searchQuery, timeRange);

			return {
				success: true,
				data: report,
			};
		},
		{
			params: t.Object({
				query: t.String(),
			}),
			query: t.Object({
				startDate: t.String(),
				endDate: t.String(),
			}),
		}
	)

	// A/B Testing and Experiments
	.post(
		'/experiments',
		async ({ body, store, headers }: any) => {
			const siteId = getSiteId(store, headers);
			const experiment = await SearchExperimentEngine.createExperiment({
				...body,
				siteId,
			});

			return {
				success: true,
				data: experiment,
			};
		},
		{
			body: t.Object({
				id: t.String(),
				name: t.String(),
				description: t.String(),
				variants: t.Array(
					t.Object({
						name: t.String(),
						description: t.String(),
						algorithm: t.Union([
							t.Literal('standard'),
							t.Literal('semantic'),
							t.Literal('popularity_boost'),
							t.Literal('personalized'),
						]),
						parameters: t.Object({}),
					})
				),
				trafficAllocation: t.Object({}),
				targetMetric: t.String(),
				startDate: t.String(),
				endDate: t.String(),
			}),
		}
	)

	.post(
		'/experiments/:experimentId/start',
		async ({ params, _store }: any) => {
			const { experimentId } = params;
			await SearchExperimentEngine.startExperiment(experimentId);

			return {
				success: true,
				data: { message: 'Experiment started' },
			};
		},
		{
			params: t.Object({
				experimentId: t.String(),
			}),
		}
	)

	.post(
		'/experiments/:experimentId/stop',
		async ({ params, _store }: any) => {
			const { experimentId } = params;
			const results = await SearchExperimentEngine.stopExperiment(experimentId);

			return {
				success: true,
				data: results,
			};
		},
		{
			params: t.Object({
				experimentId: t.String(),
			}),
		}
	)

	.get(
		'/experiments',
		async ({ query, store, headers }: any) => {
			const { status } = query;
			let experiments;

			if (status) {
				experiments = await SearchExperimentEngine.getExperimentsByStatus(status as any);
			} else {
				experiments = SearchExperimentEngine.getAllExperiments();
			}

			const siteId = getSiteId(store, headers, query);
			return {
				success: true,
				data: experiments.filter(exp => exp.siteId === siteId),
			};
		},
		{
			query: t.Object({
				status: t.Optional(
					t.Union([
						t.Literal('draft'),
						t.Literal('running'),
						t.Literal('paused'),
						t.Literal('completed'),
						t.Literal('cancelled'),
					])
				),
			}),
		}
	)

	.get(
		'/experiments/:experimentId',
		async ({ params, store, headers }: any) => {
			const { experimentId } = params;
			const experiment = SearchExperimentEngine.getExperiment(experimentId);

			const siteId = getSiteId(store, headers);
			if (!experiment || experiment.siteId !== siteId) {
				throw new Error('Experiment not found');
			}

			return {
				success: true,
				data: experiment,
			};
		},
		{
			params: t.Object({
				experimentId: t.String(),
			}),
		}
	)

	// Enhanced search with experiment support
	.get(
		'/enhanced',
		async ({ query, store, headers }: any) => {
			const { q, filters, page = 1, limit = 20, experimentId, userId } = query;

			const siteId = getSiteId(store, headers, query);
			let result;

			if (experimentId && userId) {
				// Get user's variant assignment
				const variant = await SearchExperimentEngine.assignUserToVariant(experimentId, userId, siteId);

				// Execute search with experiment
				result = await SearchExperimentEngine.executeSearchWithExperiment(
					experimentId,
					variant,
					siteId,
					q,
					filters ? JSON.parse(filters) : {},
					{},
					Number(page),
					Number(limit),
					userId
				);

				result.experiment = {
					id: experimentId,
					variant,
					isExperiment: true,
				};
			} else {
				// Regular enhanced search
				result = await searchService.search(
					siteId,
					q,
					filters ? JSON.parse(filters) : {},
					{},
					Number(page),
					Number(limit),
					userId
				);
			}

			return {
				success: true,
				data: result,
			};
		},
		{
			query: t.Object({
				q: t.String(),
				page: t.Optional(t.String()),
				limit: t.Optional(t.String()),
				filters: t.Optional(t.String()),
				experimentId: t.Optional(t.String()),
				userId: t.Optional(t.String()),
			}),
		}
	)

	// Click tracking for analytics
	.post(
		'/track/click',
		async ({ body, store, headers }: any) => {
			const { query, itemId, position, userId, sessionId } = body;

			const siteId = getSiteId(store, headers);
			// Track click for analytics
			// This would integrate with your analytics system
			console.log('Search click tracked:', {
				siteId,
				query,
				itemId,
				position,
				userId,
				sessionId,
				timestamp: new Date(),
			});

			return {
				success: true,
				data: { message: 'Click tracked' },
			};
		},
		{
			body: t.Object({
				query: t.String(),
				itemId: t.String(),
				position: t.Number(),
				userId: t.Optional(t.String()),
				sessionId: t.Optional(t.String()),
			}),
		}
	)

	// Search performance monitoring
	.get('/health', async ({ store, headers }: any) => {
		const siteId = getSiteId(store, headers);
		const indexCount = await searchService.getSearchStats(siteId, {
			start: new Date(Date.now() - 24 * 60 * 60 * 1000),
			end: new Date(),
		});

		return {
			success: true,
			data: {
				status: 'healthy',
				indexSize: indexCount.length,
				timestamp: new Date(),
				features: {
					realTimeSearch: true,
					analytics: true,
					experiments: true,
					thaiLanguageSupport: true,
					caching: true,
				},
			},
		};
	});
