import mongoose, { Schema, type Document } from 'mongoose';
import { customAlphabet } from 'nanoid';

const generateId = () => {
	const timestamp = Date.now().toString(36);
	const nanoid = customAlphabet('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz', 12)();
	return `${timestamp}${nanoid}`;
};

export interface ISearchIndex extends Document {
	_id: string;
	siteId: string;
	entityType: 'product' | 'category' | 'brand' | 'article';
	entityId: string;
	title: string;
	description: string;
	content: string;
	tags: string[];
	metadata: {
		price?: number;
		category?: string;
		brand?: string;
		rating?: number;
		reviewCount?: number;
		stock?: number;
		status?: string;
	};
	searchableFields: {
		name: string;
		description: string;
		tags: string[];
		attributes: Record<string, any>;
	};
	popularity: number;
	lastUpdated: Date;
	createdAt: Date;
	updatedAt: Date;
}

export interface ISearchQuery extends Document {
	_id: string;
	siteId: string;
	userId?: string;
	query: string;
	filters: {
		category?: string[];
		brand?: string[];
		priceRange?: {
			min: number;
			max: number;
		};
		rating?: number;
		availability?: 'in_stock' | 'out_of_stock' | 'pre_order';
		attributes?: Record<string, any>;
	};
	sortBy: string;
	sortOrder: 'asc' | 'desc';
	results: {
		total: number;
		returned: number;
		page: number;
		limit: number;
	};
	userAgent: string;
	ipAddress: string;
	createdAt: Date;
}

export interface ISearchSuggestion extends Document {
	_id: string;
	siteId: string;
	query: string;
	suggestions: string[];
	frequency: number;
	lastUsed: Date;
	createdAt: Date;
	updatedAt: Date;
}

export interface ISearchFilter extends Document {
	_id: string;
	siteId: string;
	name: string;
	type: 'category' | 'brand' | 'price' | 'rating' | 'attribute' | 'custom';
	key: string;
	values: any[];
	isActive: boolean;
	order: number;
	settings: {
		multiple: boolean;
		required: boolean;
		defaultValue?: any;
		minValue?: number;
		maxValue?: number;
		step?: number;
	};
	createdAt: Date;
	updatedAt: Date;
}

const SearchIndexSchema = new Schema<ISearchIndex>(
	{
		_id: { type: String, default: generateId },
		siteId: { type: String, required: true, index: true },
		entityType: {
			type: String,
			required: true,
			enum: ['product', 'category', 'brand', 'article'],
		},
		entityId: { type: String, required: true, index: true },
		title: { type: String, required: true },
		description: { type: String },
		content: { type: String, required: true },
		tags: [{ type: String }],
		metadata: {
			price: { type: Number },
			category: { type: String },
			brand: { type: String },
			rating: { type: Number },
			reviewCount: { type: Number },
			stock: { type: Number },
			status: { type: String },
		},
		searchableFields: {
			name: { type: String, required: true },
			description: { type: String },
			tags: [{ type: String }],
			attributes: { type: Schema.Types.Mixed },
		},
		popularity: { type: Number, default: 0 },
		lastUpdated: { type: Date, default: Date.now },
	},
	{
		timestamps: true,
		versionKey: false,
	}
);

const SearchQuerySchema = new Schema<ISearchQuery>(
	{
		_id: { type: String, default: generateId },
		siteId: { type: String, required: true, index: true },
		userId: { type: String, index: true },
		query: { type: String, required: true },
		filters: {
			category: [{ type: String }],
			brand: [{ type: String }],
			priceRange: {
				min: { type: Number },
				max: { type: Number },
			},
			rating: { type: Number },
			availability: {
				type: String,
				enum: ['in_stock', 'out_of_stock', 'pre_order'],
			},
			attributes: { type: Schema.Types.Mixed },
		},
		sortBy: { type: String, default: 'relevance' },
		sortOrder: {
			type: String,
			enum: ['asc', 'desc'],
			default: 'desc',
		},
		results: {
			total: { type: Number, required: true },
			returned: { type: Number, required: true },
			page: { type: Number, required: true },
			limit: { type: Number, required: true },
		},
		userAgent: { type: String },
		ipAddress: { type: String },
	},
	{
		timestamps: true,
		versionKey: false,
	}
);

const SearchSuggestionSchema = new Schema<ISearchSuggestion>(
	{
		_id: { type: String, default: generateId },
		siteId: { type: String, required: true, index: true },
		query: { type: String, required: true },
		suggestions: [{ type: String }],
		frequency: { type: Number, default: 1 },
		lastUsed: { type: Date, default: Date.now },
	},
	{
		timestamps: true,
		versionKey: false,
	}
);

const SearchFilterSchema = new Schema<ISearchFilter>(
	{
		_id: { type: String, default: generateId },
		siteId: { type: String, required: true, index: true },
		name: { type: String, required: true },
		type: {
			type: String,
			required: true,
			enum: ['category', 'brand', 'price', 'rating', 'attribute', 'custom'],
		},
		key: { type: String, required: true },
		values: [{ type: Schema.Types.Mixed }],
		isActive: { type: Boolean, default: true },
		order: { type: Number, default: 0 },
		settings: {
			multiple: { type: Boolean, default: false },
			required: { type: Boolean, default: false },
			defaultValue: { type: Schema.Types.Mixed },
			minValue: { type: Number },
			maxValue: { type: Number },
			step: { type: Number },
		},
	},
	{
		timestamps: true,
		versionKey: false,
	}
);

// Compound Indexes for better performance
SearchIndexSchema.index({ siteId: 1, entityType: 1 });
SearchIndexSchema.index({ siteId: 1, entityId: 1 }, { unique: true });
SearchIndexSchema.index({ siteId: 1, 'metadata.category': 1, 'metadata.status': 1 });
SearchIndexSchema.index({ siteId: 1, 'metadata.brand': 1, 'metadata.status': 1 });
SearchIndexSchema.index({ siteId: 1, 'metadata.price': 1, 'metadata.rating': -1 });
SearchIndexSchema.index({ siteId: 1, popularity: -1, lastUpdated: -1 });
SearchIndexSchema.index({ siteId: 1, entityType: 1, 'metadata.status': 1, popularity: -1 });

// Text search indexes with weights
SearchIndexSchema.index(
	{
		title: 'text',
		content: 'text',
		'searchableFields.name': 'text',
		'searchableFields.description': 'text',
		tags: 'text',
	},
	{
		weights: {
			title: 10,
			'searchableFields.name': 8,
			tags: 5,
			'searchableFields.description': 3,
			content: 1,
		},
		name: 'search_text_index',
	}
);

SearchQuerySchema.index({ siteId: 1, query: 1 });
SearchQuerySchema.index({ siteId: 1, userId: 1 });
SearchQuerySchema.index({ createdAt: -1 });

SearchSuggestionSchema.index({ siteId: 1, query: 1 });
SearchSuggestionSchema.index({ siteId: 1, frequency: -1 });

SearchFilterSchema.index({ siteId: 1, isActive: 1 });
SearchFilterSchema.index({ siteId: 1, order: 1 });

// Static methods
SearchIndexSchema.statics.findByEntity = async function (siteId: string, entityType: string, entityId: string) {
	return this.findOne({ siteId, entityType, entityId });
};

SearchIndexSchema.statics.search = async function (siteId: string, query: string, filters: any = {}, sort: any = {}) {
	const searchQuery: any = { siteId };

	if (query) {
		searchQuery.$text = { $search: query };
	}

	// Apply filters
	if (filters.category) {
		searchQuery['metadata.category'] = { $in: filters.category };
	}
	if (filters.brand) {
		searchQuery['metadata.brand'] = { $in: filters.brand };
	}
	if (filters.priceRange) {
		searchQuery['metadata.price'] = {
			$gte: filters.priceRange.min,
			$lte: filters.priceRange.max,
		};
	}
	if (filters.rating) {
		searchQuery['metadata.rating'] = { $gte: filters.rating };
	}

	return this.find(searchQuery).sort(sort);
};

SearchQuerySchema.statics.findPopular = async function (siteId: string, limit: number = 10) {
	return this.aggregate([
		{ $match: { siteId } },
		{
			$group: {
				_id: '$query',
				count: { $sum: 1 },
				lastUsed: { $max: '$createdAt' },
			},
		},
		{ $sort: { count: -1 } },
		{ $limit: limit },
	]);
};

SearchSuggestionSchema.statics.findSuggestions = async function (siteId: string, query: string, limit: number = 5) {
	return this.find({
		siteId,
		query: { $regex: query, $options: 'i' },
	})
		.sort({ frequency: -1 })
		.limit(limit);
};

SearchFilterSchema.statics.findActive = async function (siteId: string) {
	return this.find({ siteId, isActive: true }).sort({ order: 1 });
};

export const SearchIndex = mongoose.model<ISearchIndex>('SearchIndex', SearchIndexSchema);
export const SearchQuery = mongoose.model<ISearchQuery>('SearchQuery', SearchQuerySchema);
export const SearchSuggestion = mongoose.model<ISearchSuggestion>('SearchSuggestion', SearchSuggestionSchema);
export const SearchFilter = mongoose.model<ISearchFilter>('SearchFilter', SearchFilterSchema);
