import { Elysia, t } from 'elysia';
import * as multilangService from './multilang.service';
import { userPlugin } from '@/core/middleware/checkUser';

export const multilangRoutes = new Elysia({ prefix: '/multilang' })
	.use(userPlugin)
	.get('/languages', async ({ store }: any) => {
		const languages = await multilangService.getLanguages(store.siteId);

		return {
			success: true,
			data: languages,
		};
	})
	.get('/languages/default', async ({ store }: any) => {
		const language = await multilangService.getDefaultLanguage(store.siteId);

		return {
			success: true,
			data: language,
		};
	})
	.post(
		'/languages',
		async ({ body, store }: any) => {
			const language = await multilangService.createLanguage(store.siteId, body);

			return {
				success: true,
				data: language,
			};
		},
		{
			body: t.Object({
				code: t.String(),
				name: t.String(),
				nativeName: t.String(),
				isActive: t.Optional(t.<PERSON>()),
				isDefault: t.Optional(t.<PERSON>()),
				direction: t.Optional(t.Union([t.Literal('ltr'), t.Literal('rtl')])),
				flag: t.Optional(t.String()),
				settings: t.Optional(
					t.Object({
						dateFormat: t.String(),
						timeFormat: t.String(),
						currency: t.String(),
						currencySymbol: t.String(),
						currencyPosition: t.Union([t.Literal('before'), t.Literal('after')]),
						decimalSeparator: t.String(),
						thousandSeparator: t.String(),
						timezone: t.String(),
					})
				),
			}),
		}
	)
	.put(
		'/languages/:languageId',
		async ({ params, body, store }: any) => {
			const { languageId } = params;

			const language = await multilangService.updateLanguage(store.siteId, languageId, body);

			return {
				success: true,
				data: language,
			};
		},
		{
			params: t.Object({
				languageId: t.String(),
			}),
			body: t.Object({
				name: t.Optional(t.String()),
				nativeName: t.Optional(t.String()),
				isActive: t.Optional(t.Boolean()),
				isDefault: t.Optional(t.Boolean()),
				direction: t.Optional(t.Union([t.Literal('ltr'), t.Literal('rtl')])),
				flag: t.Optional(t.String()),
				settings: t.Optional(
					t.Object({
						dateFormat: t.Optional(t.String()),
						timeFormat: t.Optional(t.String()),
						currency: t.Optional(t.String()),
						currencySymbol: t.Optional(t.String()),
						currencyPosition: t.Optional(t.Union([t.Literal('before'), t.Literal('after')])),
						decimalSeparator: t.Optional(t.String()),
						thousandSeparator: t.Optional(t.String()),
						timezone: t.Optional(t.String()),
					})
				),
			}),
		}
	)
	.delete(
		'/languages/:languageId',
		async ({ params, store }: any) => {
			const { languageId } = params;

			const result = await multilangService.deleteLanguage(store.siteId, languageId);

			return {
				success: true,
				data: result,
			};
		},
		{
			params: t.Object({
				languageId: t.String(),
			}),
		}
	)
	.get(
		'/translations/:languageCode/:namespace',
		async ({ params, store }: any) => {
			const { languageCode, namespace } = params;

			const translations = await multilangService.getTranslations(store.siteId, languageCode, namespace);

			return {
				success: true,
				data: translations,
			};
		},
		{
			params: t.Object({
				languageCode: t.String(),
				namespace: t.String(),
			}),
		}
	)
	.get(
		'/translations/:languageCode/key/:key',
		async ({ params, store }: any) => {
			const { languageCode, key } = params;

			const translation = await multilangService.getTranslation(store.siteId, languageCode, key);

			return {
				success: true,
				data: translation,
			};
		},
		{
			params: t.Object({
				languageCode: t.String(),
				key: t.String(),
			}),
		}
	)
	.post(
		'/translations',
		async ({ body, store }: any) => {
			const translation = await multilangService.createTranslation(store.siteId, body);

			return {
				success: true,
				data: translation,
			};
		},
		{
			body: t.Object({
				languageCode: t.String(),
				namespace: t.String(),
				key: t.String(),
				value: t.String(),
				context: t.Optional(t.String()),
			}),
		}
	)
	.put(
		'/translations/:translationId',
		async ({ params, body, store }: any) => {
			const { translationId } = params;

			const translation = await multilangService.updateTranslation(store.siteId, translationId, body);

			return {
				success: true,
				data: translation,
			};
		},
		{
			params: t.Object({
				translationId: t.String(),
			}),
			body: t.Object({
				value: t.Optional(t.String()),
				context: t.Optional(t.String()),
				isActive: t.Optional(t.Boolean()),
			}),
		}
	)
	.delete(
		'/translations/:translationId',
		async ({ params, store }: any) => {
			const { translationId } = params;

			const result = await multilangService.deleteTranslation(store.siteId, translationId);

			return {
				success: true,
				data: result,
			};
		},
		{
			params: t.Object({
				translationId: t.String(),
			}),
		}
	)
	.get('/currencies', async ({ store }: any) => {
		const currencies = await multilangService.getCurrencies(store.siteId);

		return {
			success: true,
			data: currencies,
		};
	})
	.get('/currencies/base', async ({ store }: any) => {
		const currency = await multilangService.getBaseCurrency(store.siteId);

		return {
			success: true,
			data: currency,
		};
	})
	.post(
		'/currencies',
		async ({ body, store }: any) => {
			const currency = await multilangService.createCurrency(store.siteId, body);

			return {
				success: true,
				data: currency,
			};
		},
		{
			body: t.Object({
				code: t.String(),
				name: t.String(),
				symbol: t.String(),
				rate: t.Number(),
				isBase: t.Optional(t.Boolean()),
				isActive: t.Optional(t.Boolean()),
				precision: t.Optional(t.Number()),
				position: t.Optional(t.Union([t.Literal('before'), t.Literal('after')])),
			}),
		}
	)
	.put(
		'/currencies/:currencyId',
		async ({ params, body, store }: any) => {
			const { currencyId } = params;

			const currency = await multilangService.updateCurrency(store.siteId, currencyId, body);

			return {
				success: true,
				data: currency,
			};
		},
		{
			params: t.Object({
				currencyId: t.String(),
			}),
			body: t.Object({
				name: t.Optional(t.String()),
				symbol: t.Optional(t.String()),
				rate: t.Optional(t.Number()),
				isBase: t.Optional(t.Boolean()),
				isActive: t.Optional(t.Boolean()),
				precision: t.Optional(t.Number()),
				position: t.Optional(t.Union([t.Literal('before'), t.Literal('after')])),
			}),
		}
	)
	.delete(
		'/currencies/:currencyId',
		async ({ params, store }: any) => {
			const { currencyId } = params;

			const result = await multilangService.deleteCurrency(store.siteId, currencyId);

			return {
				success: true,
				data: result,
			};
		},
		{
			params: t.Object({
				currencyId: t.String(),
			}),
		}
	)
	.get('/timezones', async ({ store }: any) => {
		const timezones = await multilangService.getTimezones(store.siteId);

		return {
			success: true,
			data: timezones,
		};
	})
	.get('/timezones/default', async ({ store }: any) => {
		const timezone = await multilangService.getDefaultTimezone(store.siteId);

		return {
			success: true,
			data: timezone,
		};
	})
	.post(
		'/timezones',
		async ({ body, store }: any) => {
			const timezone = await multilangService.createTimezone(store.siteId, body);

			return {
				success: true,
				data: timezone,
			};
		},
		{
			body: t.Object({
				name: t.String(),
				offset: t.String(),
				abbreviation: t.String(),
				isActive: t.Optional(t.Boolean()),
				isDefault: t.Optional(t.Boolean()),
			}),
		}
	)
	.put(
		'/timezones/:timezoneId',
		async ({ params, body, store }: any) => {
			const { timezoneId } = params;

			const timezone = await multilangService.updateTimezone(store.siteId, timezoneId, body);

			return {
				success: true,
				data: timezone,
			};
		},
		{
			params: t.Object({
				timezoneId: t.String(),
			}),
			body: t.Object({
				name: t.Optional(t.String()),
				offset: t.Optional(t.String()),
				abbreviation: t.Optional(t.String()),
				isActive: t.Optional(t.Boolean()),
				isDefault: t.Optional(t.Boolean()),
			}),
		}
	)
	.delete(
		'/timezones/:timezoneId',
		async ({ params, store }: any) => {
			const { timezoneId } = params;

			const result = await multilangService.deleteTimezone(store.siteId, timezoneId);

			return {
				success: true,
				data: result,
			};
		},
		{
			params: t.Object({
				timezoneId: t.String(),
			}),
		}
	)
	.post(
		'/convert-currency',
		async ({ body, store }: any) => {
			const result = await multilangService.convertCurrency(
				store.siteId,
				body.amount,
				body.fromCurrency,
				body.toCurrency
			);

			return {
				success: true,
				data: result,
			};
		},
		{
			body: t.Object({
				amount: t.Number(),
				fromCurrency: t.String(),
				toCurrency: t.String(),
			}),
		}
	)
	.get('/settings', async ({ store }: any) => {
		const settings = await multilangService.getLocalizationSettings(store.siteId);

		return {
			success: true,
			data: settings,
		};
	});
