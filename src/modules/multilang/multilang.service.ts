import { Language, Translation, Currency, Timezone } from './multilang.model';
import { HttpError } from '@/core/utils/error';

// Multi-language Service
export async function createLanguage(siteId: string, languageData: any) {
	try {
		const language = await Language.create({
			siteId,
			...languageData,
		});

		return language;
	} catch (err: any) {
		console.error('Error in createLanguage:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะสร้างภาษา');
	}
}

export async function getLanguages(siteId: string) {
	try {
		const languages = await (Language as any).findActive(siteId);
		return languages;
	} catch (err: any) {
		console.error('Error in getLanguages:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงภาษา');
	}
}

export async function getDefaultLanguage(siteId: string) {
	try {
		const language = await (Language as any).findDefault(siteId);
		return language;
	} catch (err: any) {
		console.error('Error in getDefaultLanguage:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงภาษาเริ่มต้น');
	}
}

export async function updateLanguage(siteId: string, languageId: string, updates: any) {
	try {
		const language = await Language.findOneAndUpdate({ siteId, _id: languageId }, { $set: updates }, { new: true });

		if (!language) {
			throw new HttpError(404, 'ไม่พบภาษา');
		}

		return language;
	} catch (err: any) {
		console.error('Error in updateLanguage:', err);
		if (err instanceof HttpError) throw err;
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะอัปเดตภาษา');
	}
}

export async function deleteLanguage(siteId: string, languageId: string) {
	try {
		const language = await Language.findOneAndDelete({ siteId, _id: languageId });

		if (!language) {
			throw new HttpError(404, 'ไม่พบภาษา');
		}

		return { message: 'ลบภาษาสำเร็จ' };
	} catch (err: any) {
		console.error('Error in deleteLanguage:', err);
		if (err instanceof HttpError) throw err;
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะลบภาษา');
	}
}

export async function createTranslation(siteId: string, translationData: any) {
	try {
		const translation = await Translation.create({
			siteId,
			...translationData,
		});

		return translation;
	} catch (err: any) {
		console.error('Error in createTranslation:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะสร้างคำแปล');
	}
}

export async function getTranslations(siteId: string, languageCode: string, namespace: string) {
	try {
		const translations = await (Translation as any).findByNamespace(siteId, languageCode, namespace);
		return translations;
	} catch (err: any) {
		console.error('Error in getTranslations:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงคำแปล');
	}
}

export async function getTranslation(siteId: string, languageCode: string, key: string) {
	try {
		const translation = await (Translation as any).findByKey(siteId, languageCode, key);
		return translation;
	} catch (err: any) {
		console.error('Error in getTranslation:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงคำแปล');
	}
}

export async function updateTranslation(siteId: string, translationId: string, updates: any) {
	try {
		const translation = await Translation.findOneAndUpdate(
			{ siteId, _id: translationId },
			{ $set: updates },
			{ new: true }
		);

		if (!translation) {
			throw new HttpError(404, 'ไม่พบคำแปล');
		}

		return translation;
	} catch (err: any) {
		console.error('Error in updateTranslation:', err);
		if (err instanceof HttpError) throw err;
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะอัปเดตคำแปล');
	}
}

export async function deleteTranslation(siteId: string, translationId: string) {
	try {
		const translation = await Translation.findOneAndDelete({ siteId, _id: translationId });

		if (!translation) {
			throw new HttpError(404, 'ไม่พบคำแปล');
		}

		return { message: 'ลบคำแปลสำเร็จ' };
	} catch (err: any) {
		console.error('Error in deleteTranslation:', err);
		if (err instanceof HttpError) throw err;
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะลบคำแปล');
	}
}

export async function createCurrency(siteId: string, currencyData: any) {
	try {
		const currency = await Currency.create({
			siteId,
			...currencyData,
		});

		return currency;
	} catch (err: any) {
		console.error('Error in createCurrency:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะสร้างสกุลเงิน');
	}
}

export async function getCurrencies(siteId: string) {
	try {
		const currencies = await (Currency as any).findActive(siteId);
		return currencies;
	} catch (err: any) {
		console.error('Error in getCurrencies:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงสกุลเงิน');
	}
}

export async function getBaseCurrency(siteId: string) {
	try {
		const currency = await (Currency as any).findBase(siteId);
		return currency;
	} catch (err: any) {
		console.error('Error in getBaseCurrency:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงสกุลเงินหลัก');
	}
}

export async function updateCurrency(siteId: string, currencyId: string, updates: any) {
	try {
		const currency = await Currency.findOneAndUpdate({ siteId, _id: currencyId }, { $set: updates }, { new: true });

		if (!currency) {
			throw new HttpError(404, 'ไม่พบสกุลเงิน');
		}

		return currency;
	} catch (err: any) {
		console.error('Error in updateCurrency:', err);
		if (err instanceof HttpError) throw err;
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะอัปเดตสกุลเงิน');
	}
}

export async function deleteCurrency(siteId: string, currencyId: string) {
	try {
		const currency = await Currency.findOneAndDelete({ siteId, _id: currencyId });

		if (!currency) {
			throw new HttpError(404, 'ไม่พบสกุลเงิน');
		}

		return { message: 'ลบสกุลเงินสำเร็จ' };
	} catch (err: any) {
		console.error('Error in deleteCurrency:', err);
		if (err instanceof HttpError) throw err;
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะลบสกุลเงิน');
	}
}

export async function createTimezone(siteId: string, timezoneData: any) {
	try {
		const timezone = await Timezone.create({
			siteId,
			...timezoneData,
		});

		return timezone;
	} catch (err: any) {
		console.error('Error in createTimezone:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะสร้าง timezone');
	}
}

export async function getTimezones(siteId: string) {
	try {
		const timezones = await (Timezone as any).findActive(siteId);
		return timezones;
	} catch (err: any) {
		console.error('Error in getTimezones:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง timezone');
	}
}

export async function getDefaultTimezone(siteId: string) {
	try {
		const timezone = await (Timezone as any).findDefault(siteId);
		return timezone;
	} catch (err: any) {
		console.error('Error in getDefaultTimezone:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง timezone เริ่มต้น');
	}
}

export async function updateTimezone(siteId: string, timezoneId: string, updates: any) {
	try {
		const timezone = await Timezone.findOneAndUpdate({ siteId, _id: timezoneId }, { $set: updates }, { new: true });

		if (!timezone) {
			throw new HttpError(404, 'ไม่พบ timezone');
		}

		return timezone;
	} catch (err: any) {
		console.error('Error in updateTimezone:', err);
		if (err instanceof HttpError) throw err;
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะอัปเดต timezone');
	}
}

export async function deleteTimezone(siteId: string, timezoneId: string) {
	try {
		const timezone = await Timezone.findOneAndDelete({ siteId, _id: timezoneId });

		if (!timezone) {
			throw new HttpError(404, 'ไม่พบ timezone');
		}

		return { message: 'ลบ timezone สำเร็จ' };
	} catch (err: any) {
		console.error('Error in deleteTimezone:', err);
		if (err instanceof HttpError) throw err;
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะลบ timezone');
	}
}

export async function convertCurrency(siteId: string, amount: number, fromCurrency: string, toCurrency: string) {
	try {
		const fromCurr = await Currency.findOne({ siteId, code: fromCurrency });
		const toCurr = await Currency.findOne({ siteId, code: toCurrency });

		if (!fromCurr || !toCurr) {
			throw new HttpError(404, 'ไม่พบสกุลเงิน');
		}

		// แปลงผ่านสกุลเงินหลัก
		const baseAmount = amount / fromCurr.rate;
		const convertedAmount = baseAmount * toCurr.rate;

		return {
			originalAmount: amount,
			originalCurrency: fromCurrency,
			convertedAmount: Math.round(convertedAmount * 100) / 100,
			convertedCurrency: toCurrency,
			rate: toCurr.rate / fromCurr.rate,
		};
	} catch (err: any) {
		console.error('Error in convertCurrency:', err);
		if (err instanceof HttpError) throw err;
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะแปลงสกุลเงิน');
	}
}

export async function formatCurrency(amount: number, currency: ICurrency) {
	try {
		const _formatted = new Intl.NumberFormat('en-US', {
			style: 'currency',
			currency: currency.code,
			minimumFractionDigits: currency.precision,
			maximumFractionDigits: currency.precision,
		}).format(amount);

		if (currency.position === 'before') {
			return `${currency.symbol}${amount.toFixed(currency.precision)}`;
		} else {
			return `${amount.toFixed(currency.precision)}${currency.symbol}`;
		}
	} catch (err: any) {
		console.error('Error in formatCurrency:', err);
		return amount.toFixed(2);
	}
}

export async function getLocalizationSettings(siteId: string) {
	try {
		const [languages, currencies, timezones] = await Promise.all([
			getLanguages(siteId),
			getCurrencies(siteId),
			getTimezones(siteId),
		]);

		return {
			languages,
			currencies,
			timezones,
		};
	} catch (err: any) {
		console.error('Error in getLocalizationSettings:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงการตั้งค่าภาษา');
	}
}
