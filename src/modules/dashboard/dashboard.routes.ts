import { Elysia, t } from 'elysia';
import { userAuthPlugin } from '@/core/plugins/auth';
import { dashboardService } from './dashboard.service';
import { isSiteOwner } from '@/core/middleware/checkUser';
import { HttpError } from '@/core/utils/error';

export const dashboardRoutes = new Elysia({ prefix: '/dashboard' })
	.use(userAuthPlugin)

	// Dashboard Overview - ข้อมูลรวมทั้งหมด
	.get(
		'/:siteId/overview',
		async ({ params, query, user }: any) => {
			try {
				const { siteId } = params;
				const userId = user?._id;

				if (!userId) {
					throw new HttpError(401, 'ไม่พบข้อมูลผู้ใช้');
				}

				// ตรวจสอบว่าเป็น site owner หรือไม่
				const isOwner = await isSiteOwner(siteId, userId);
				if (!isOwner) {
					throw new HttpError(403, 'ไม่มีสิทธิ์ในการเข้าถึงข้อมูลนี้');
				}

				// กำหนดพารามิเตอร์
				const params_data = {
					period: query.period || 'month',
					startDate: query.startDate ? new Date(query.startDate) : undefined,
					endDate: query.endDate ? new Date(query.endDate) : undefined,
				};

				const dashboardData = await dashboardService.getDashboardOverview(siteId, params_data);

				return {
					success: true,
					message: 'ดึงข้อมูล dashboard สำเร็จ',
					statusMessage: 'สำเร็จ!',
					timestamp: new Date().toISOString(),
					data: dashboardData,
				};
			} catch (err: any) {
				console.error('Error in dashboard overview:', err);
				if (err instanceof HttpError) throw err;
				throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงข้อมูล dashboard');
			}
		},
		{
			params: t.Object({
				siteId: t.String(),
			}),
			query: t.Object({
				period: t.Optional(t.Union([t.Literal('today'), t.Literal('week'), t.Literal('month'), t.Literal('year')])),
				startDate: t.Optional(t.String()),
				endDate: t.Optional(t.String()),
			}),
		}
	)

	// Dashboard Stats - เฉพาะสถิติ
	.get(
		'/:siteId/stats',
		async ({ params, query, user }: any) => {
			try {
				const { siteId } = params;
				const userId = user?._id;

				if (!userId) {
					throw new HttpError(401, 'ไม่พบข้อมูลผู้ใช้');
				}

				// ตรวจสอบว่าเป็น site owner หรือไม่
				const isOwner = await isSiteOwner(siteId, userId);
				if (!isOwner) {
					throw new HttpError(403, 'ไม่มีสิทธิ์ในการเข้าถึงข้อมูลนี้');
				}

				// กำหนดพารามิเตอร์
				const params_data = {
					period: query.period || 'month',
					startDate: query.startDate ? new Date(query.startDate) : undefined,
					endDate: query.endDate ? new Date(query.endDate) : undefined,
				};

				const stats = await dashboardService.getDashboardStats(siteId, params_data);

				return {
					success: true,
					message: 'ดึงสถิติ dashboard สำเร็จ',
					statusMessage: 'สำเร็จ!',
					timestamp: new Date().toISOString(),
					data: stats,
				};
			} catch (err: any) {
				console.error('Error in dashboard stats:', err);
				if (err instanceof HttpError) throw err;
				throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงสถิติ dashboard');
			}
		},
		{
			params: t.Object({
				siteId: t.String(),
			}),
			query: t.Object({
				period: t.Optional(t.Union([t.Literal('today'), t.Literal('week'), t.Literal('month'), t.Literal('year')])),
				startDate: t.Optional(t.String()),
				endDate: t.Optional(t.String()),
			}),
		}
	)

	// Recent Activities - กิจกรรมล่าสุด
	.get(
		'/:siteId/activities',
		async ({ params, query, user }: any) => {
			try {
				const { siteId } = params;
				const userId = user?._id;

				if (!userId) {
					throw new HttpError(401, 'ไม่พบข้อมูลผู้ใช้');
				}

				// ตรวจสอบว่าเป็น site owner หรือไม่
				const isOwner = await isSiteOwner(siteId, userId);
				if (!isOwner) {
					throw new HttpError(403, 'ไม่มีสิทธิ์ในการเข้าถึงข้อมูลนี้');
				}

				const limit = parseInt(query.limit || '10');
				const activities = await dashboardService.getRecentActivities(siteId, limit);

				return {
					success: true,
					message: 'ดึงกิจกรรมล่าสุดสำเร็จ',
					statusMessage: 'สำเร็จ!',
					timestamp: new Date().toISOString(),
					data: activities,
				};
			} catch (err: any) {
				console.error('Error in dashboard activities:', err);
				if (err instanceof HttpError) throw err;
				throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงกิจกรรมล่าสุด');
			}
		},
		{
			params: t.Object({
				siteId: t.String(),
			}),
			query: t.Object({
				limit: t.Optional(t.String()),
			}),
		}
	);
