import { HttpError } from '../../core/utils';
import { getOrderStats } from '../order/order.service';
import { getProductStats } from '../product/product.service';
import { getCustomerStats } from '../customer/customer.service';
import { analyticsService } from '../analytics/analytics.service';

export interface DashboardStats {
	totalSales: number;
	totalOrders: number;
	totalVisitors: number;
	totalProducts: number;
	salesChange: string;
	ordersChange: string;
	visitorsChange: string;
	productsChange: string;
}

export interface DashboardChartData {
	sales: {
		labels: string[];
		datasets: Array<{
			label: string;
			data: number[];
			borderColor: string;
			backgroundColor: string;
			tension: number;
		}>;
	};
	visitors: {
		labels: string[];
		datasets: Array<{
			label: string;
			data: number[];
			borderColor: string;
			backgroundColor: string;
			tension: number;
		}>;
	};
	orders: {
		labels: string[];
		datasets: Array<{
			data: number[];
			backgroundColor: string[];
		}>;
	};
	products: {
		labels: string[];
		datasets: Array<{
			data: number[];
			backgroundColor: string[];
		}>;
	};
}

export interface RecentActivity {
	type: 'order' | 'payment' | 'product' | 'customer';
	message: string;
	time: string;
	icon: string;
	color: string;
}

export interface DashboardData {
	stats: DashboardStats;
	chartData: DashboardChartData;
	recentActivities: RecentActivity[];
}

export interface DashboardParams {
	period?: 'today' | 'week' | 'month' | 'year';
	startDate?: Date;
	endDate?: Date;
}

/**
 * Dashboard Service - รวบรวมข้อมูลจาก services ต่างๆ
 */
export class DashboardService {
	/**
	 * ดึงข้อมูล dashboard overview
	 */
	async getDashboardOverview(siteId: string, params: DashboardParams = {}): Promise<DashboardData> {
		try {
			// กำหนดช่วงเวลาเริ่มต้น
			const dateRange = this.getDateRange(params);

			// ดึงข้อมูลจาก services ต่างๆ แบบ parallel
			const [orderStats, productStats, customerStats, salesAnalytics]:any[] = await Promise.all([
				getOrderStats(siteId, dateRange),
				getProductStats(siteId),
				getCustomerStats(siteId, dateRange),
				analyticsService.getSalesAnalytics(siteId, dateRange),
			]);

			// รวบรวมสถิติ
			const stats: DashboardStats = {
				totalSales: salesAnalytics.totalRevenue || 0,
				totalOrders: orderStats.totalOrders || 0,
				totalVisitors: 0, // TODO: เพิ่มจาก analytics service
				totalProducts: productStats.total || 0,
				salesChange: orderStats.monthChange || '+0%',
				ordersChange: orderStats.todayChange || '+0%',
				visitorsChange: '+0%', // TODO: เพิ่มจาก analytics service
				productsChange: '+0%', // TODO: คำนวณจากข้อมูลสินค้า
			};

			// สร้างข้อมูลกราฟ
			const chartData: DashboardChartData = {
				sales: {
					labels: salesAnalytics.chartLabels || ['ม.ค.', 'ก.พ.', 'มี.ค.', 'เม.ย.', 'พ.ค.', 'มิ.ย.'],
					datasets: [
						{
							label: 'ยอดขาย (บาท)',
							data: salesAnalytics.chartData || [0, 0, 0, 0, 0, 0],
							borderColor: 'rgb(59, 130, 246)',
							backgroundColor: 'rgba(59, 130, 246, 0.1)',
							tension: 0.4,
						},
					],
				},
				visitors: {
					labels: ['ม.ค.', 'ก.พ.', 'มี.ค.', 'เม.ย.', 'พ.ค.', 'มิ.ย.'],
					datasets: [
						{
							label: 'ผู้เข้าชม',
							data: [0, 0, 0, 0, 0, 0], // TODO: เพิ่มจาก analytics service
							borderColor: 'rgb(168, 85, 247)',
							backgroundColor: 'rgba(168, 85, 247, 0.1)',
							tension: 0.4,
						},
					],
				},
				orders: {
					labels: ['รอดำเนินการ', 'กำลังจัดส่ง', 'จัดส่งแล้ว', 'ยกเลิก'],
					datasets: [
						{
							data: [
								orderStats.pendingOrders || 0,
								0, // TODO: เพิ่ม shipping orders
								orderStats.paidOrders || 0,
								0, // TODO: เพิ่ม cancelled orders
							],
							backgroundColor: ['rgb(59, 130, 246)', 'rgb(245, 158, 11)', 'rgb(34, 197, 94)', 'rgb(239, 68, 68)'],
						},
					],
				},
				products: {
					labels: ['ขายดี', 'ปกติ', 'ขายช้า', 'หมด'],
					datasets: [
						{
							data: [
								0, // TODO: เพิ่มจาก product analytics
								productStats.active || 0,
								0, // TODO: เพิ่มจาก product analytics
								productStats.outOfStock || 0,
							],
							backgroundColor: ['rgb(34, 197, 94)', 'rgb(59, 130, 246)', 'rgb(245, 158, 11)', 'rgb(239, 68, 68)'],
						},
					],
				},
			};

			// สร้างกิจกรรมล่าสุด
			const recentActivities: RecentActivity[] = [
				{
					type: 'order',
					message: `มีคำสั่งซื้อใหม่ ${orderStats.today || 0} รายการวันนี้`,
					time: 'เมื่อสักครู่',
					icon: 'mdi:shopping-cart',
					color: 'text-blue-500',
				},
				{
					type: 'payment',
					message: `ได้รับชำระเงิน ฿${(salesAnalytics.totalRevenue || 0).toLocaleString()}`,
					time: '5 นาทีที่แล้ว',
					icon: 'mdi:currency-usd',
					color: 'text-green-500',
				},
				{
					type: 'product',
					message: `มีสินค้าใหม่ ${productStats.active || 0} รายการ`,
					time: '1 ชั่วโมงที่แล้ว',
					icon: 'mdi:package',
					color: 'text-purple-500',
				},
				{
					type: 'customer',
					message: `ลูกค้าใหม่ ${customerStats.new || 0} คน`,
					time: '2 ชั่วโมงที่แล้ว',
					icon: 'mdi:account-plus',
					color: 'text-orange-500',
				},
			];

			return {
				stats,
				chartData,
				recentActivities,
			};
		} catch (err: any) {
			console.error('Error in getDashboardOverview:', err);
			throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงข้อมูล dashboard');
		}
	}

	/**
	 * ดึงเฉพาะสถิติ dashboard
	 */
	async getDashboardStats(siteId: string, params: DashboardParams = {}): Promise<DashboardStats> {
		try {
			const dateRange = this.getDateRange(params);

			const [orderStats, productStats, salesAnalytics]:any[] = await Promise.all([
				getOrderStats(siteId, dateRange),
				getProductStats(siteId),
				analyticsService.getSalesAnalytics(siteId, dateRange),
			]);

			return {
				totalSales: salesAnalytics.totalRevenue || 0,
				totalOrders: orderStats.totalOrders || 0,
				totalVisitors: 0,
				totalProducts: productStats.total || 0,
				salesChange: orderStats.monthChange || '+0%',
				ordersChange: orderStats.todayChange || '+0%',
				visitorsChange: '+0%',
				productsChange: '+0%',
			};
		} catch (err: any) {
			console.error('Error in getDashboardStats:', err);
			throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงสถิติ dashboard');
		}
	}

	/**
	 * ดึงกิจกรรมล่าสุด
	 */
	async getRecentActivities(siteId: string, _limit: number = 10): Promise<RecentActivity[]> {
		try {
			// TODO: ดึงข้อมูลกิจกรรมจริงจาก database
			const activities: RecentActivity[] = [
				{
					type: 'order',
					message: 'มีคำสั่งซื้อใหม่ #ORD-2024-001',
					time: 'เมื่อสักครู่',
					icon: 'mdi:shopping-cart',
					color: 'text-blue-500',
				},
				{
					type: 'payment',
					message: 'ได้รับชำระเงิน ฿2,500',
					time: '5 นาทีที่แล้ว',
					icon: 'mdi:currency-usd',
					color: 'text-green-500',
				},
			];
			
			return activities;
		} catch (err: any) {
			console.error('Error in getRecentActivities:', err);
			throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงกิจกรรมล่าสุด');
		}
	}

	/**
	 * กำหนดช่วงเวลาสำหรับการดึงข้อมูล
	 */
	private getDateRange(params: DashboardParams): { start: Date; end: Date } {
		const now = new Date();

		if (params.startDate && params.endDate) {
			return {
				start: params.startDate,
				end: params.endDate,
			};
		}

		switch (params.period) {
			case 'today':
				return {
					start: new Date(now.getFullYear(), now.getMonth(), now.getDate()),
					end: now,
				};
			case 'week':
				return {
					start: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000),
					end: now,
				};
			case 'year':
				return {
					start: new Date(now.getFullYear(), 0, 1),
					end: now,
				};
			case 'month':
			default:
				return {
					start: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000),
					end: now,
				};
		}
	}
}

export const dashboardService = new DashboardService();
