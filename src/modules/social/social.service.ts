import { SocialPost, SocialComment, SocialShare, SocialInfluencer, SocialCampaign } from './social.model';
import { HttpError } from '@/core/utils/error';

// Social Commerce Service
export async function createSocialPost(siteId: string, postData: any) {
	try {
		const post = await SocialPost.create({
			siteId,
			...postData,
		});

		return post;
	} catch (err: any) {
		console.error('Error in createSocialPost:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะสร้างโพสต์');
	}
}

export async function getSocialPosts(
	siteId: string,
	userId?: string,
	type?: string,
	page: number = 1,
	limit: number = 20
) {
	try {
		const query: any = { siteId, status: 'published' };

		if (userId) {
			query.userId = userId;
		}

		if (type) {
			query.type = type;
		}

		const skip = (page - 1) * limit;
		const posts = await SocialPost.find(query).sort({ createdAt: -1 }).skip(skip).limit(limit);

		const total = await SocialPost.countDocuments(query);

		return {
			posts,
			pagination: {
				page,
				limit,
				total,
				pages: Math.ceil(total / limit),
			},
		};
	} catch (err: any) {
		console.error('Error in getSocialPosts:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงโพสต์');
	}
}

export async function getSocialPost(siteId: string, postId: string) {
	try {
		const post = await SocialPost.findOne({ siteId, _id: postId, status: 'published' });

		if (!post) {
			throw new HttpError(404, 'ไม่พบโพสต์');
		}

		return post;
	} catch (err: any) {
		console.error('Error in getSocialPost:', err);
		if (err instanceof HttpError) throw err;
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงโพสต์');
	}
}

export async function updateSocialPost(siteId: string, postId: string, userId: string, updates: any) {
	try {
		const post = await SocialPost.findOneAndUpdate({ siteId, _id: postId, userId }, { $set: updates }, { new: true });

		if (!post) {
			throw new HttpError(404, 'ไม่พบโพสต์');
		}

		return post;
	} catch (err: any) {
		console.error('Error in updateSocialPost:', err);
		if (err instanceof HttpError) throw err;
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะอัปเดตโพสต์');
	}
}

export async function deleteSocialPost(siteId: string, postId: string, userId: string) {
	try {
		const post = await SocialPost.findOneAndDelete({
			siteId,
			_id: postId,
			userId,
		});

		if (!post) {
			throw new HttpError(404, 'ไม่พบโพสต์');
		}

		return { message: 'ลบโพสต์สำเร็จ' };
	} catch (err: any) {
		console.error('Error in deleteSocialPost:', err);
		if (err instanceof HttpError) throw err;
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะลบโพสต์');
	}
}

export async function likeSocialPost(siteId: string, postId: string ) {
	try {
		const post = await SocialPost.findOneAndUpdate(
			{ siteId, _id: postId },
			{ $inc: { 'engagement.likes': 1 } },
			{ new: true }
		);

		if (!post) {
			throw new HttpError(404, 'ไม่พบโพสต์');
		}

		return post;
	} catch (err: any) {
		console.error('Error in likeSocialPost:', err);
		if (err instanceof HttpError) throw err;
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะกดไลค์โพสต์');
	}
}

export async function createSocialComment(siteId: string, commentData: any) {
	try {
		const comment = await SocialComment.create({
			siteId,
			...commentData,
		});

		// อัปเดตจำนวน comments ในโพสต์
		await SocialPost.findOneAndUpdate({ siteId, _id: commentData.postId }, { $inc: { 'engagement.comments': 1 } });

		return comment;
	} catch (err: any) {
		console.error('Error in createSocialComment:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะสร้างคอมเมนต์');
	}
}

export async function getSocialComments(siteId: string, postId: string) {
	try {
		const comments = await (SocialComment as any).findByPost(siteId, postId);
		return comments;
	} catch (err: any) {
		console.error('Error in getSocialComments:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงคอมเมนต์');
	}
}

export async function likeSocialComment(siteId: string, commentId: string) {
	try {
		const comment = await SocialComment.findOneAndUpdate(
			{ siteId, _id: commentId },
			{ $inc: { likes: 1 } },
			{ new: true }
		);

		if (!comment) {
			throw new HttpError(404, 'ไม่พบคอมเมนต์');
		}

		return comment;
	} catch (err: any) {
		console.error('Error in likeSocialComment:', err);
		if (err instanceof HttpError) throw err;
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะกดไลค์คอมเมนต์');
	}
}

export async function shareSocialPost(siteId: string, shareData: any) {
	try {
		const share = await SocialShare.create({
			siteId,
			...shareData,
		});

		// อัปเดตจำนวน shares ในโพสต์
		await SocialPost.findOneAndUpdate({ siteId, _id: shareData.postId }, { $inc: { 'engagement.shares': 1 } });

		return share;
	} catch (err: any) {
		console.error('Error in shareSocialPost:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะแชร์โพสต์');
	}
}

export async function getTrendingPosts(siteId: string, limit: number = 10) {
	try {
		const posts = await (SocialPost as any).findTrending(siteId, limit);
		return posts;
	} catch (err: any) {
		console.error('Error in getTrendingPosts:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงโพสต์ยอดนิยม');
	}
}

export async function getPostsByHashtag(siteId: string, hashtag: string, _page: number = 1, _limit: number = 20) {
	try {
		const posts = await (SocialPost as any).findByHashtag(siteId, hashtag);
		return posts;
	} catch (err: any) {
		console.error('Error in getPostsByHashtag:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงโพสต์ตาม hashtag');
	}	
}

export async function createSocialInfluencer(siteId: string, influencerData: any) {
	try {
		const influencer = await SocialInfluencer.create({
			siteId,
			...influencerData,
		});

		return influencer;
	} catch (err: any) {
		console.error('Error in createSocialInfluencer:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะสร้าง influencer');
	}
}

export async function getSocialInfluencers(siteId: string) {
	try {
		const influencers = await (SocialInfluencer as any).findActive(siteId);
		return influencers;
	} catch (err: any) {
		console.error('Error in getSocialInfluencers:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง influencer');
	}
}

export async function updateSocialInfluencer(siteId: string, influencerId: string, updates: any) {
	try {
		const influencer = await SocialInfluencer.findOneAndUpdate(
			{ siteId, _id: influencerId },
			{ $set: updates },
			{ new: true }
		);

		if (!influencer) {
			throw new HttpError(404, 'ไม่พบ influencer');
		}

		return influencer;
	} catch (err: any) {
		console.error('Error in updateSocialInfluencer:', err);
		if (err instanceof HttpError) throw err;
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะอัปเดต influencer');
	}
}

export async function deleteSocialInfluencer(siteId: string, influencerId: string) {
	try {
		const influencer = await SocialInfluencer.findOneAndDelete({ siteId, _id: influencerId });

		if (!influencer) {
			throw new HttpError(404, 'ไม่พบ influencer');
		}

		return { message: 'ลบ influencer สำเร็จ' };
	} catch (err: any) {
		console.error('Error in deleteSocialInfluencer:', err);
		if (err instanceof HttpError) throw err;
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะลบ influencer');
	}
}

export async function createSocialCampaign(siteId: string, campaignData: any) {
	try {
		const campaign = await SocialCampaign.create({
			siteId,
			...campaignData,
		});

		return campaign;
	} catch (err: any) {
		console.error('Error in createSocialCampaign:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะสร้างแคมเปญ');
	}
}

export async function getSocialCampaigns(siteId: string) {
	try {
		const campaigns = await SocialCampaign.find({ siteId }).sort({ createdAt: -1 });
		return campaigns;
	} catch (err: any) {
		console.error('Error in getSocialCampaigns:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงแคมเปญ');
	}
}

export async function getActiveSocialCampaigns(siteId: string) {
	try {
		const campaigns = await (SocialCampaign as any).findActive(siteId);
		return campaigns;
	} catch (err: any) {
		console.error('Error in getActiveSocialCampaigns:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงแคมเปญที่กำลังดำเนินการ');
	}
}

export async function updateSocialCampaign(siteId: string, campaignId: string, updates: any) {
	try {
		const campaign = await SocialCampaign.findOneAndUpdate(
			{ siteId, _id: campaignId },
			{ $set: updates },
			{ new: true }
		);

		if (!campaign) {
			throw new HttpError(404, 'ไม่พบแคมเปญ');
		}

		return campaign;
	} catch (err: any) {
		console.error('Error in updateSocialCampaign:', err);
		if (err instanceof HttpError) throw err;
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะอัปเดตแคมเปญ');
	}
}

export async function deleteSocialCampaign(siteId: string, campaignId: string) {
	try {
		const campaign = await SocialCampaign.findOneAndDelete({ siteId, _id: campaignId });

		if (!campaign) {
			throw new HttpError(404, 'ไม่พบแคมเปญ');
		}

		return { message: 'ลบแคมเปญสำเร็จ' };
	} catch (err: any) {
		console.error('Error in deleteSocialCampaign:', err);
		if (err instanceof HttpError) throw err;
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะลบแคมเปญ');
	}
}

export async function getSocialStats(siteId: string, timeRange: { start: Date; end: Date }) {
	try {
		const stats = await SocialPost.aggregate([
			{
				$match: {
					siteId,
					createdAt: { $gte: timeRange.start, $lte: timeRange.end },
				},
			},
			{
				$group: {
					_id: '$type',
					count: { $sum: 1 },
					totalLikes: { $sum: '$engagement.likes' },
					totalComments: { $sum: '$engagement.comments' },
					totalShares: { $sum: '$engagement.shares' },
				},
			},
		]);

		return stats;
	} catch (err: any) {
		console.error('Error in getSocialStats:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงสถิติ social');
	}
}

export async function getInfluencerStats(siteId: string, influencerId: string) {
	try {
		const posts = await SocialPost.find({ siteId, userId: influencerId });

		const stats = {
			totalPosts: posts.length,
			totalLikes: posts.reduce((sum, post) => sum + post.engagement.likes, 0),
			totalComments: posts.reduce((sum, post) => sum + post.engagement.comments, 0),
			totalShares: posts.reduce((sum, post) => sum + post.engagement.shares, 0),
			averageEngagement: 0,
		};

		if (posts.length > 0) {
			const totalEngagement = stats.totalLikes + stats.totalComments + stats.totalShares;
			stats.averageEngagement = totalEngagement / posts.length;
		}

		return stats;
	} catch (err: any) {
		console.error('Error in getInfluencerStats:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงสถิติ influencer');
	}
}
