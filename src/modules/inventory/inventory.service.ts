import { Inventory, InventoryTransaction } from './inventory.model';
import { Product } from '../product/product.model';
import { HttpError } from '@/core/utils/error';

// Inventory Service
export async function createInventoryItem(inventoryData: {
	siteId: string;
	productId: string;
	variantId?: string;
	sku: string;
	quantity: number;
	costPrice: number;
	lowStockThreshold?: number;
	reorderPoint?: number;
	reorderQuantity?: number;
	supplierId?: string;
	supplierName?: string;
	location?: string;
	expiryDate?: Date;
	batchNumber?: string;
}) {
	try {
		const {
			siteId,
			productId,
			variantId,
			sku,
			quantity,
			costPrice,
			lowStockThreshold,
			reorderPoint,
			reorderQuantity,
			supplierId,
			supplierName,
			location,
			expiryDate,
			batchNumber,
		} = inventoryData;

		// ตรวจสอบว่าสินค้ามีอยู่จริง
		const product = await Product.findById(productId);
		if (!product) {
			throw new HttpError(404, 'ไม่พบสินค้า');
		}

		// ตรวจสอบว่ามี inventory item นี้แล้วหรือไม่
		const existingInventory = await Inventory.findOne({ siteId, productId, variantId });
		if (existingInventory) {
			throw new HttpError(400, 'มี inventory item นี้แล้ว');
		}

		// ตรวจสอบ SKU ซ้ำ
		const existingSku = await Inventory.findOne({ siteId, sku });
		if (existingSku) {
			throw new HttpError(400, 'SKU นี้ถูกใช้แล้ว');
		}

		const inventory = await Inventory.create({
			siteId,
			productId,
			variantId,
			sku,
			quantity,
			costPrice,
			lowStockThreshold: lowStockThreshold || 10,
			reorderPoint: reorderPoint || 5,
			reorderQuantity: reorderQuantity || 50,
			supplierId,
			supplierName,
			location,
			expiryDate,
			batchNumber,
		});

		return {
			success: true,
			message: 'สร้าง inventory item สำเร็จ',
			data: inventory,
		};
	} catch (err: any) {
		console.error('Error in createInventoryItem:', err);
		if (err instanceof HttpError) throw err;
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะสร้าง inventory item');
	}
}

export async function getInventoryByProduct(productId: string, siteId: string) {
	try {
		const inventory = await Inventory.find({ productId, siteId });
		return inventory;
	} catch (err: any) {
		console.error('Error in getInventoryByProduct:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง inventory');
	}
}

export async function getInventoryById(inventoryId: string) {
	try {
		const inventory = await Inventory.findById(inventoryId);
		if (!inventory) {
			throw new HttpError(404, 'ไม่พบ inventory item');
		}
		return inventory;
	} catch (err: any) {
		console.error('Error in getInventoryById:', err);
		if (err instanceof HttpError) throw err;
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงข้อมูล inventory');
	}
}

export async function updateInventoryStock(
	inventoryId: string,
	updateData: {
		quantity?: number;
		costPrice?: number;
		lowStockThreshold?: number;
		reorderPoint?: number;
		reorderQuantity?: number;
		supplierId?: string;
		supplierName?: string;
		location?: string;
		expiryDate?: Date;
		batchNumber?: string;
		status?: InventoryStatus;
	}
) {
	try {
		const inventory = await Inventory.findById(inventoryId);
		if (!inventory) {
			throw new HttpError(404, 'ไม่พบ inventory item');
		}

		// อัปเดตข้อมูล
		Object.assign(inventory, updateData);
		await inventory.save();

		return {
			success: true,
			message: 'อัปเดต inventory สำเร็จ',
			data: inventory,
		};
	} catch (err: any) {
		console.error('Error in updateInventoryStock:', err);
		if (err instanceof HttpError) throw err;
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะอัปเดต inventory');
	}
}

export async function adjustStock(
	inventoryId: string,
	action: InventoryAction,
	quantity: number,
	performedBy: string,
	referenceId?: string,
	referenceType?: string,
	notes?: string
) {
	try {
		const inventory = await Inventory.findById(inventoryId);
		if (!inventory) {
			throw new HttpError(404, 'ไม่พบ inventory item');
		}

		const previousQuantity = inventory.quantity;

		// อัปเดตสต็อก
		await (inventory as any).updateStock(quantity, action, performedBy, referenceId, referenceType, notes);

		// สร้าง transaction record
		const transaction = await InventoryTransaction.create({
			siteId: inventory.siteId,
			productId: inventory.productId,
			variantId: inventory.variantId,
			action,
			quantity,
			previousQuantity,
			newQuantity: inventory.quantity,
			unitCost: inventory.costPrice,
			totalCost: inventory.costPrice * quantity,
			referenceId,
			referenceType,
			notes,
			performedBy,
			performedAt: new Date(),
		});

		return {
			success: true,
			message: 'ปรับสต็อกสำเร็จ',
			data: {
				inventory,
				transaction,
			},
		};
	} catch (err: any) {
		console.error('Error in adjustStock:', err);
		if (err instanceof HttpError) throw err;
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะปรับสต็อก');
	}
}

export async function reserveStock(inventoryId: string, quantity: number) {
	try {
		const inventory = await Inventory.findById(inventoryId);
		if (!inventory) {
			throw new HttpError(404, 'ไม่พบ inventory item');
		}

		await (inventory as any).reserveStock(quantity);

		return {
			success: true,
			message: 'จองสต็อกสำเร็จ',
			data: inventory,
		};
	} catch (err: any) {
		console.error('Error in reserveStock:', err);
		if (err instanceof HttpError) throw err;
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะจองสต็อก');
	}
}

export async function releaseStock(inventoryId: string, quantity: number) {
	try {
		const inventory = await Inventory.findById(inventoryId);
		if (!inventory) {
			throw new HttpError(404, 'ไม่พบ inventory item');
		}

		await (inventory as any).releaseStock(quantity);

		return {
			success: true,
			message: 'ปล่อยสต็อกสำเร็จ',
			data: inventory,
		};
	} catch (err: any) {
		console.error('Error in releaseStock:', err);
		if (err instanceof HttpError) throw err;
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะปล่อยสต็อก');
	}
}

export async function getInventoryTransactions(siteId: string, filter: any = {}) {
	try {
		const query = { siteId, ...filter };
		const transactions = await InventoryTransaction.find(query).sort({ performedAt: -1 });
		return transactions;
	} catch (err: any) {
		console.error('Error in getInventoryTransactions:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงประวัติการเคลื่อนไหวสต็อก');
	}
}

export async function getLowStockItems(siteId: string) {
	try {
		const lowStockItems = await Inventory.find({
			siteId,
			status: 'active',
			availableQuantity: { $lte: '$lowStockThreshold' },
		});

		return lowStockItems;
	} catch (err: any) {
		console.error('Error in getLowStockItems:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงรายการสต็อกต่ำ');
	}
}

export async function getReorderItems(siteId: string) {
	try {
		const reorderItems = await Inventory.find({
			siteId,
			status: 'active',
			availableQuantity: { $lte: '$reorderPoint' },
		});

		return reorderItems;
	} catch (err: any) {
		console.error('Error in getReorderItems:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงรายการที่ต้องสั่งซื้อใหม่');
	}
}

export async function getExpiringItems(siteId: string, daysThreshold: number = 30) {
	try {
		const thresholdDate = new Date();
		thresholdDate.setDate(thresholdDate.getDate() + daysThreshold);

		const expiringItems = await Inventory.find({
			siteId,
			status: 'active',
			expiryDate: { $lte: thresholdDate, $gte: new Date() },
		});

		return expiringItems;
	} catch (err: any) {
		console.error('Error in getExpiringItems:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงรายการใกล้หมดอายุ');
	}
}

// Analytics functions
export async function getInventoryStats(siteId: string) {
	try {
		const [totalItems, lowStockItems, reorderItems, totalValue] = await Promise.all([
			Inventory.countDocuments({ siteId, status: 'active' }),
			Inventory.countDocuments({ siteId, status: 'active', availableQuantity: { $lte: '$lowStockThreshold' } }),
			Inventory.countDocuments({ siteId, status: 'active', availableQuantity: { $lte: '$reorderPoint' } }),
			Inventory.aggregate([
				{ $match: { siteId, status: 'active' } },
				{ $group: { _id: null, total: { $sum: { $multiply: ['$quantity', '$costPrice'] } } } },
			]),
		]);

		return {
			totalItems,
			lowStockItems,
			reorderItems,
			totalValue: totalValue[0]?.total || 0,
		};
	} catch (err: any) {
		console.error('Error in getInventoryStats:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงสถิติ inventory');
	}
}

export async function getStockMovement(siteId: string, startDate: Date, endDate: Date) {
	try {
		const movements = await InventoryTransaction.aggregate([
			{ $match: { siteId, performedAt: { $gte: startDate, $lte: endDate } } },
			{
				$group: {
					_id: {
						action: '$action',
						date: { $dateToString: { format: '%Y-%m-%d', date: '$performedAt' } },
					},
					totalQuantity: { $sum: '$quantity' },
					totalCost: { $sum: '$totalCost' },
					count: { $sum: 1 },
				},
			},
			{ $sort: { '_id.date': 1 } },
		]);

		return movements;
	} catch (err: any) {
		console.error('Error in getStockMovement:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงการเคลื่อนไหวสต็อก');
	}
}
