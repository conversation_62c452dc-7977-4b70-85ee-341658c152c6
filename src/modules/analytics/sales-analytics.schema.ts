import { t } from 'elysia';

// Analytics Period Schema
export const analyticsPeriodSchema = t.Union(
	[
		t.Literal('daily', { error: 'ต้องเป็น daily เท่านั้น' }),
		t.Literal('weekly', { error: 'ต้องเป็น weekly เท่านั้น' }),
		t.Literal('monthly', { error: 'ต้องเป็น monthly เท่านั้น' }),
		t.Literal('yearly', { error: 'ต้องเป็น yearly เท่านั้น' }),
	],
	{ error: 'period ไม่ถูกต้อง' }
);

// Date Range Schema
export const dateRangeSchema = t.Object({
	startDate: t.String({ format: 'date-time', error: 'startDate ต้องเป็นวันที่' }),
	endDate: t.String({ format: 'date-time', error: 'endDate ต้องเป็นวันที่' }),
});

// Sales Analytics Filter Schema
export const salesAnalyticsFilterSchema = t.Object({
	period: analyticsPeriodSchema,
	startDate: t.String({ format: 'date-time', error: 'startDate ต้องเป็นวันที่' }),
	endDate: t.String({ format: 'date-time', error: 'endDate ต้องเป็นวันที่' }),
	limit: t.Optional(t.Number({ minimum: 1, maximum: 100, error: 'limit ต้องเป็นตัวเลข 1-100' })),
});

// Sales Analytics Response Schema
export const salesAnalyticsResponseSchema = t.Object({
	success: t.Boolean({ error: 'success ต้องเป็น true หรือ false เท่านั้น' }),
	message: t.String({ error: 'message ต้องเป็นข้อความ' }),
	statusMessage: t.String({ error: 'statusMessage ต้องเป็นข้อความ' }),
	timestamp: t.String({ error: 'timestamp ต้องเป็นข้อความ' }),
	data: t.Object({
		_id: t.String({ error: '_id ต้องเป็นข้อความ' }),
		siteId: t.String({ error: 'siteId ต้องเป็นข้อความ' }),
		period: analyticsPeriodSchema,
		startDate: t.String({ format: 'date-time', error: 'startDate ต้องเป็นวันที่' }),
		endDate: t.String({ format: 'date-time', error: 'endDate ต้องเป็นวันที่' }),
		totalRevenue: t.Number({ error: 'totalRevenue ต้องเป็นตัวเลข' }),
		totalOrders: t.Number({ error: 'totalOrders ต้องเป็นตัวเลข' }),
		totalCustomers: t.Number({ error: 'totalCustomers ต้องเป็นตัวเลข' }),
		averageOrderValue: t.Number({ error: 'averageOrderValue ต้องเป็นตัวเลข' }),
		conversionRate: t.Number({ error: 'conversionRate ต้องเป็นตัวเลข' }),
		totalRefunds: t.Number({ error: 'totalRefunds ต้องเป็นตัวเลข' }),
		netRevenue: t.Number({ error: 'netRevenue ต้องเป็นตัวเลข' }),
		revenueGrowth: t.Number({ error: 'revenueGrowth ต้องเป็นตัวเลข' }),
		orderGrowth: t.Number({ error: 'orderGrowth ต้องเป็นตัวเลข' }),
		customerGrowth: t.Number({ error: 'customerGrowth ต้องเป็นตัวเลข' }),
		topProducts: t.Array(t.Any({ error: 'topProducts ต้องเป็น array' }), { error: 'topProducts ต้องเป็น array' }),
		topCategories: t.Array(t.Any({ error: 'topCategories ต้องเป็น array' }), { error: 'topCategories ต้องเป็น array' }),
		topCustomers: t.Array(t.Any({ error: 'topCustomers ต้องเป็น array' }), { error: 'topCustomers ต้องเป็น array' }),
	}),
});

// Sales Trends Response Schema
export const salesTrendsResponseSchema = t.Object({
	success: t.Boolean({ error: 'success ต้องเป็น true หรือ false เท่านั้น' }),
	message: t.String({ error: 'message ต้องเป็นข้อความ' }),
	statusMessage: t.String({ error: 'statusMessage ต้องเป็นข้อความ' }),
	timestamp: t.String({ error: 'timestamp ต้องเป็นข้อความ' }),
	data: t.Array(
		t.Object({
			date: t.String({ format: 'date-time', error: 'date ต้องเป็นวันที่' }),
			revenue: t.Number({ error: 'revenue ต้องเป็นตัวเลข' }),
			orders: t.Number({ error: 'orders ต้องเป็นตัวเลข' }),
			customers: t.Number({ error: 'customers ต้องเป็นตัวเลข' }),
			averageOrderValue: t.Number({ error: 'averageOrderValue ต้องเป็นตัวเลข' }),
		}),
		{ error: 'data ต้องเป็น array ของ object' }
	),
});

// Top Products Response Schema
export const topProductsResponseSchema = t.Object({
	success: t.Boolean({ error: 'success ต้องเป็น true หรือ false เท่านั้น' }),
	message: t.String({ error: 'message ต้องเป็นข้อความ' }),
	statusMessage: t.String({ error: 'statusMessage ต้องเป็นข้อความ' }),
	timestamp: t.String({ error: 'timestamp ต้องเป็นข้อความ' }),
	data: t.Array(
		t.Object({
			productId: t.String({ error: 'productId ต้องเป็นข้อความ' }),
			productName: t.String({ error: 'productName ต้องเป็นข้อความ' }),
			categoryId: t.String({ error: 'categoryId ต้องเป็นข้อความ' }),
			categoryName: t.String({ error: 'categoryName ต้องเป็นข้อความ' }),
			brandId: t.Optional(t.String({ error: 'brandId ต้องเป็นข้อความ' })),
			brandName: t.Optional(t.String({ error: 'brandName ต้องเป็นข้อความ' })),
			quantitySold: t.Number({ error: 'quantitySold ต้องเป็นตัวเลข' }),
			revenue: t.Number({ error: 'revenue ต้องเป็นตัวเลข' }),
			averageRating: t.Number({ error: 'averageRating ต้องเป็นตัวเลข' }),
			reviewCount: t.Number({ error: 'reviewCount ต้องเป็นตัวเลข' }),
		}),
		{ error: 'data ต้องเป็น array ของ object' }
	),
});

// Top Customers Response Schema
export const topCustomersResponseSchema = t.Object({
	success: t.Boolean({ error: 'success ต้องเป็น true หรือ false เท่านั้น' }),
	message: t.String({ error: 'message ต้องเป็นข้อความ' }),
	statusMessage: t.String({ error: 'statusMessage ต้องเป็นข้อความ' }),
	timestamp: t.String({ error: 'timestamp ต้องเป็นข้อความ' }),
	data: t.Array(
		t.Object({
			customerId: t.String({ error: 'customerId ต้องเป็นข้อความ' }),
			customerName: t.String({ error: 'customerName ต้องเป็นข้อความ' }),
			email: t.String({ error: 'email ต้องเป็นข้อความ' }),
			totalOrders: t.Number({ error: 'totalOrders ต้องเป็นตัวเลข' }),
			totalSpent: t.Number({ error: 'totalSpent ต้องเป็นตัวเลข' }),
			averageOrderValue: t.Number({ error: 'averageOrderValue ต้องเป็นตัวเลข' }),
			lastOrderDate: t.String({ format: 'date-time', error: 'lastOrderDate ต้องเป็นวันที่' }),
			firstOrderDate: t.String({ format: 'date-time', error: 'firstOrderDate ต้องเป็นวันที่' }),
			customerLifetimeValue: t.Number({ error: 'customerLifetimeValue ต้องเป็นตัวเลข' }),
		}),
		{ error: 'data ต้องเป็น array ของ object' }
	),
});
