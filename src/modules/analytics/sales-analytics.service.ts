import { SalesAnalytics } from './sales-analytics.model';
import { Order } from './order.model';
import { Product } from './product.model';
import { HttpError } from '@/core/utils/error';

// Sales Analytics Service
export async function generateSalesAnalytics(siteId: string, period: AnalyticsPeriod, startDate: Date, endDate: Date) {
	try {
		// ตรวจสอบว่ามี analytics นี้แล้วหรือไม่
		const existingAnalytics = await (SalesAnalytics as any).getAnalyticsByPeriod(siteId, period, startDate, endDate);
		if (existingAnalytics) {
			return existingAnalytics;
		}

		// ดึงข้อมูลออเดอร์ในช่วงเวลาที่กำหนด
		const orders = await Order.find({
			siteId,
			createdAt: { $gte: startDate, $lte: endDate },
			status: { $in: ['completed', 'delivered', 'shipped'] },
		}).populate('userId', 'name email');

		// คำนวณ metrics พื้นฐาน
		const totalRevenue = orders.reduce((sum, order) => sum + order.totalAmount, 0);
		const totalOrders = orders.length;
		const uniqueCustomers = new Set(orders.map(order => order.userId.toString())).size;
		const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

		// คำนวณ refunds
		const refundedOrders = await Order.find({
			siteId,
			createdAt: { $gte: startDate, $lte: endDate },
			status: 'refunded',
		});
		const totalRefunds = refundedOrders.reduce((sum, order) => sum + order.totalAmount, 0);
		const netRevenue = totalRevenue - totalRefunds;

		// คำนวณ growth metrics (เทียบกับช่วงก่อนหน้า)
		const previousStartDate = new Date(startDate);
		const previousEndDate = new Date(endDate);
		const periodInMs = endDate.getTime() - startDate.getTime();

		previousStartDate.setTime(previousStartDate.getTime() - periodInMs);
		previousEndDate.setTime(previousEndDate.getTime() - periodInMs);

		const previousOrders = await Order.find({
			siteId,
			createdAt: { $gte: previousStartDate, $lte: previousEndDate },
			status: { $in: ['completed', 'delivered', 'shipped'] },
		});

		const previousRevenue = previousOrders.reduce((sum, order) => sum + order.totalAmount, 0);
		const previousOrdersCount = previousOrders.length;
		const previousCustomers = new Set(previousOrders.map(order => order.userId.toString())).size;

		const revenueGrowth = previousRevenue > 0 ? ((totalRevenue - previousRevenue) / previousRevenue) * 100 : 0;
		const orderGrowth = previousOrdersCount > 0 ? ((totalOrders - previousOrdersCount) / previousOrdersCount) * 100 : 0;
		const customerGrowth =
			previousCustomers > 0 ? ((uniqueCustomers - previousCustomers) / previousCustomers) * 100 : 0;

		// คำนวณ top products
		const productSales = new Map();
		for (const order of orders) {
			for (const item of order.items) {
				const productId = item.productId.toString();
				if (!productSales.has(productId)) {
					productSales.set(productId, {
						productId,
						quantitySold: 0,
						revenue: 0,
					});
				}
				const productData = productSales.get(productId);
				productData.quantitySold += item.quantity;
				productData.revenue += item.price * item.quantity;
			}
		}

		// ดึงข้อมูลสินค้าและจัดอันดับ
		const topProducts = [];
		for (const [productId, data] of productSales) {
			const product = await Product.findById(productId);
			if (product) {
				topProducts.push({
					productId,
					productName: product.name,
					categoryId: product.categoryId,
					categoryName: product.categoryName,
					brandId: product.brandId,
					brandName: product.brandName,
					quantitySold: data.quantitySold,
					revenue: data.revenue,
					averageRating: product.averageRating || 0,
					reviewCount: product.reviewCount || 0,
				});
			}
		}

		// จัดอันดับ top products
		topProducts.sort((a, b) => b.revenue - a.revenue);
		const topProductsFinal = topProducts.slice(0, 10);

		// คำนวณ top categories
		const categorySales = new Map();
		for (const product of topProducts) {
			const categoryId = product.categoryId;
			if (!categorySales.has(categoryId)) {
				categorySales.set(categoryId, {
					categoryId,
					categoryName: product.categoryName,
					revenue: 0,
					orders: 0,
				});
			}
			const categoryData = categorySales.get(categoryId);
			categoryData.revenue += product.revenue;
			categoryData.orders += 1;
		}

		const topCategories = Array.from(categorySales.values())
			.sort((a, b) => b.revenue - a.revenue)
			.slice(0, 10);

		// คำนวณ top customers
		const customerSales = new Map();
		for (const order of orders) {
			const customerId = order.userId.toString();
			if (!customerSales.has(customerId)) {
				customerSales.set(customerId, {
					customerId,
					customerName: (order.userId as any).name,
					email: (order.userId as any).email,
					totalOrders: 0,
					totalSpent: 0,
					orders: [],
				});
			}
			const customerData = customerSales.get(customerId);
			customerData.totalOrders += 1;
			customerData.totalSpent += order.totalAmount;
			customerData.orders.push(order);
		}

		const topCustomers = [];
		for (const [customerId, data] of customerSales) {
			const averageOrderValue = data.totalOrders > 0 ? data.totalSpent / data.totalOrders : 0;
			const lastOrderDate = Math.max(...data.orders.map((order: any) => order.createdAt.getTime()));
			const firstOrderDate = Math.min(...data.orders.map((order: any) => order.createdAt.getTime()));

			topCustomers.push({
				customerId,
				customerName: data.customerName,
				email: data.email,
				totalOrders: data.totalOrders,
				totalSpent: data.totalSpent,
				averageOrderValue,
				lastOrderDate: new Date(lastOrderDate),
				firstOrderDate: new Date(firstOrderDate),
				customerLifetimeValue: data.totalSpent,
			});
		}

		topCustomers.sort((a, b) => b.totalSpent - a.totalSpent);
		const topCustomersFinal = topCustomers.slice(0, 10);

		// สร้าง analytics record
		const analytics = await SalesAnalytics.create({
			siteId,
			period,
			startDate,
			endDate,
			totalRevenue,
			totalOrders,
			totalCustomers: uniqueCustomers,
			averageOrderValue,
			conversionRate: 0, // จะคำนวณแยก
			totalRefunds,
			netRevenue,
			revenueGrowth,
			orderGrowth,
			customerGrowth,
			topProducts: topProductsFinal,
			topCategories,
			topCustomers: topCustomersFinal,
			dailySales: [],
			weeklySales: [],
			monthlySales: [],
		});

		return analytics;
	} catch (err: any) {
		console.error('Error in generateSalesAnalytics:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะสร้าง sales analytics');
	}
}

export async function getSalesAnalytics(siteId: string, period: AnalyticsPeriod, startDate: Date, endDate: Date) {
	try {
		let analytics = await (SalesAnalytics as any).getAnalyticsByPeriod(siteId, period, startDate, endDate);

		if (!analytics) {
			analytics = await generateSalesAnalytics(siteId, period, startDate, endDate);
		}

		return analytics;
	} catch (err: any) {
		console.error('Error in getSalesAnalytics:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง sales analytics');
	}
}

export async function getLatestSalesAnalytics(siteId: string, period: AnalyticsPeriod) {
	try {
		const analytics = await (SalesAnalytics as any).getLatestAnalytics(siteId, period);
		if (!analytics) {
			throw new HttpError(404, 'ไม่พบ sales analytics');
		}
		return analytics;
	} catch (err: any) {
		console.error('Error in getLatestSalesAnalytics:', err);
		if (err instanceof HttpError) throw err;
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง latest sales analytics');
	}
}

export async function getSalesTrends(siteId: string, startDate: Date, endDate: Date) {
	try {
		const orders = await Order.find({
			siteId,
			createdAt: { $gte: startDate, $lte: endDate },
			status: { $in: ['completed', 'delivered', 'shipped'] },
		}).sort({ createdAt: 1 });

		// จัดกลุ่มตามวัน
		const dailyData = new Map();
		for (const order of orders) {
			const dateKey = order.createdAt.toISOString().split('T')[0];
			if (!dailyData.has(dateKey)) {
				dailyData.set(dateKey, {
					date: new Date(dateKey),
					revenue: 0,
					orders: 0,
					customers: new Set(),
				});
			}
			const dayData = dailyData.get(dateKey);
			dayData.revenue += order.totalAmount;
			dayData.orders += 1;
			dayData.customers.add(order.userId.toString());
		}

		// แปลงเป็น array และคำนวณ metrics เพิ่มเติม
		const trends = Array.from(dailyData.entries()).map(([_date, data]) => ({
			date: data.date,
			revenue: data.revenue,
			orders: data.orders,
			customers: data.customers.size,
			averageOrderValue: data.orders > 0 ? data.revenue / data.orders : 0,
		}));

		return trends;
	} catch (err: any) {
		console.error('Error in getSalesTrends:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง sales trends');
	}
}

export async function getTopProducts(siteId: string, startDate: Date, endDate: Date, limit: number = 10) {
	try {
		const orders = await Order.find({
			siteId,
			createdAt: { $gte: startDate, $lte: endDate },
			status: { $in: ['completed', 'delivered', 'shipped'] },
		});

		const productSales = new Map();
		for (const order of orders) {
			for (const item of order.items) {
				const productId = item.productId.toString();
				if (!productSales.has(productId)) {
					productSales.set(productId, {
						productId,
						quantitySold: 0,
						revenue: 0,
					});
				}
				const productData = productSales.get(productId);
				productData.quantitySold += item.quantity;
				productData.revenue += item.price * item.quantity;
			}
		}

		const topProducts = [];
		for (const [productId, data] of productSales) {
			const product = await Product.findById(productId);
			if (product) {
				topProducts.push({
					productId,
					productName: product.name,
					categoryId: product.categoryId,
					categoryName: product.categoryName,
					brandId: product.brandId,
					brandName: product.brandName,
					quantitySold: data.quantitySold,
					revenue: data.revenue,
					averageRating: product.averageRating || 0,
					reviewCount: product.reviewCount || 0,
				});
			}
		}

		return topProducts.sort((a, b) => b.revenue - a.revenue).slice(0, limit);
	} catch (err: any) {
		console.error('Error in getTopProducts:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง top products');
	}
}

export async function getTopCustomers(siteId: string, startDate: Date, endDate: Date, limit: number = 10) {
	try {
		const orders = await Order.find({
			siteId,
			createdAt: { $gte: startDate, $lte: endDate },
			status: { $in: ['completed', 'delivered', 'shipped'] },
		}).populate('userId', 'name email');

		const customerSales = new Map();
		for (const order of orders) {
			const customerId = order.userId.toString();
			if (!customerSales.has(customerId)) {
				customerSales.set(customerId, {
					customerId,
					customerName: (order.userId as any).name,
					email: (order.userId as any).email,
					totalOrders: 0,
					totalSpent: 0,
					orders: [],
				});
			}
			const customerData = customerSales.get(customerId);
			customerData.totalOrders += 1;
			customerData.totalSpent += order.totalAmount;
			customerData.orders.push(order);
		}

		const topCustomers = [];
		for (const [customerId, data] of customerSales) {
			const averageOrderValue = data.totalOrders > 0 ? data.totalSpent / data.totalOrders : 0;
			const lastOrderDate = Math.max(...data.orders.map((order: any) => order.createdAt.getTime()));
			const firstOrderDate = Math.min(...data.orders.map((order: any) => order.createdAt.getTime()));

			topCustomers.push({
				customerId,
				customerName: data.customerName,
				email: data.email,
				totalOrders: data.totalOrders,
				totalSpent: data.totalSpent,
				averageOrderValue,
				lastOrderDate: new Date(lastOrderDate),
				firstOrderDate: new Date(firstOrderDate),
				customerLifetimeValue: data.totalSpent,
			});
		}

		return topCustomers.sort((a, b) => b.totalSpent - a.totalSpent).slice(0, limit);
	} catch (err: any) {
		console.error('Error in getTopCustomers:', err);
		throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง top customers');
	}
}
