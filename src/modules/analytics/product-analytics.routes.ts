import { Elysia, t } from 'elysia';
import * as productAnalyticsService from './product-analytics.service';
import { userPlugin } from '@/core/middleware/checkUser';

export const productAnalyticsRoutes = new Elysia({ prefix: '/product-analytics' })
	.use(userPlugin)
	.get(
		'/',
		async ({ query, store }: any) => {
			const { _period = 'month', startDate, endDate, _limit = 10 } = query;

			const analytics = await productAnalyticsService.getProductAnalytics(
				store.siteId,
				startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
				endDate ? new Date(endDate) : new Date()
			);

			return {
				success: true,
				data: analytics,
			};
		},
		{
			query: t.Object({
				period: t.Optional(t.Union([t.Literal('day'), t.Literal('week'), t.Literal('month'), t.Literal('year')])),
				startDate: t.Optional(t.String()),
				endDate: t.Optional(t.String()),
				limit: t.Optional(t.String()),
			}),
		}
	)
	.get(
		'/top-products',
		async ({ query, store }: any) => {
			const { _period = 'month', startDate, endDate, _limit = 10 } = query;

			const topProducts = await productAnalyticsService.getTopProducts(
				store.siteId,
				startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
				endDate ? new Date(endDate) : new Date()
			);

			return {
				success: true,
				data: topProducts,
			};
		},
		{
			query: t.Object({
				period: t.Optional(t.Union([t.Literal('day'), t.Literal('week'), t.Literal('month'), t.Literal('year')])),
				startDate: t.Optional(t.String()),
				endDate: t.Optional(t.String()),
				limit: t.Optional(t.String()),
			}),
		}
	)
	.get(
		'/category-performance',
		async ({ query, store }: any) => {
			const { _period = 'month', startDate, endDate } = query;

			const categoryPerformance = await productAnalyticsService.getCategoryPerformance(
				store.siteId,
				startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
				endDate ? new Date(endDate) : new Date()
			);

			return {
				success: true,
				data: categoryPerformance,
			};
		},
		{
			query: t.Object({
				period: t.Optional(t.Union([t.Literal('day'), t.Literal('week'), t.Literal('month'), t.Literal('year')])),
				startDate: t.Optional(t.String()),
				endDate: t.Optional(t.String()),
			}),
		}
	)
	.get(
		'/price-analysis',
		async ({ query, store }: any) => {
			const { _period = 'month', startDate, endDate } = query;

			const priceAnalysis = await productAnalyticsService.getPriceAnalysis(
				store.siteId,
				startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
				endDate ? new Date(endDate) : new Date()
			);

			return {
				success: true,
				data: priceAnalysis,
			};
		},
		{
			query: t.Object({
				period: t.Optional(t.Union([t.Literal('day'), t.Literal('week'), t.Literal('month'), t.Literal('year')])),
				startDate: t.Optional(t.String()),
				endDate: t.Optional(t.String()),
			}),
		}
	)
	.get(
		'/inventory-analysis',
		async ({ _query, store }: any) => {
			const inventoryAnalysis = await productAnalyticsService.getInventoryAnalysis(store.siteId);

			return {
				success: true,
				data: inventoryAnalysis,
			};
		},
		{
			query: t.Object({}),
		}
	)
	.get(
		'/performance-metrics',
		async ({ query, store }: any) => {
			const { _period = 'month', startDate, endDate } = query;

			const performanceMetrics = await productAnalyticsService.getPerformanceMetrics(
				store.siteId,
				startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
				endDate ? new Date(endDate) : new Date()
			);

			return {
				success: true,
				data: performanceMetrics,
			};
		},
		{
			query: t.Object({
				period: t.Optional(t.Union([t.Literal('day'), t.Literal('week'), t.Literal('month'), t.Literal('year')])),
				startDate: t.Optional(t.String()),
				endDate: t.Optional(t.String()),
			}),
		}
	)
	.post(
		'/generate',
		async ({ body, store }: any) => {
			const { _period, startDate, endDate } = body;

			const analytics = await productAnalyticsService.generateProductAnalytics(
				store.siteId,
				new Date(startDate),
				new Date(endDate)
			);

			return {
				success: true,
				data: analytics,
			};
		},
		{
			body: t.Object({
				period: t.Union([t.Literal('day'), t.Literal('week'), t.Literal('month'), t.Literal('year')]),
				startDate: t.String(),
				endDate: t.String(),
			}),
		}
	);
