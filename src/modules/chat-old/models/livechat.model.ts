import mongoose, { Schema, type Document } from 'mongoose';
import { customAlphabet } from 'nanoid';

const generateId = () => {
	const timestamp = Date.now().toString(36);
	const nanoid = customAlphabet('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz', 12)();
	return `${timestamp}${nanoid}`;
};

export interface ILiveChatSession extends Document {
	_id: string;
	siteId: string;
	customerId: string;
	customerName: string;
	customerEmail?: string;
	agentId?: string;
	agentName?: string;
	status: 'waiting' | 'active' | 'resolved' | 'closed';
	priority: 'low' | 'medium' | 'high' | 'urgent';
	category: 'general' | 'sales' | 'support' | 'technical' | 'billing';
	subject?: string;
	description: string;
	tags: string[];
	rating?: number; // 1-5
	feedback?: string;
	metadata: {
		userAgent: string;
		ipAddress: string;
		pageUrl?: string;
		referrer?: string;
		deviceType?: string;
		location?: string;
	};
	stats: {
		waitTime: number; // seconds
		chatDuration: number; // seconds
		messageCount: number;
		responseTime: number; // average response time
	};
	createdAt: Date;
	updatedAt: Date;
	resolvedAt?: Date;
	closedAt?: Date;
}

export interface ILiveChatMessage extends Document {
	_id: string;
	siteId: string;
	sessionId: string;
	senderId: string;
	senderType: 'customer' | 'agent' | 'system' | 'bot';
	senderName: string;
	messageType: 'text' | 'image' | 'file' | 'system' | 'typing';
	content: string;
	attachments?: Array<{
		fileName: string;
		fileUrl: string;
		fileSize: number;
		fileType: string;
	}>;
	isRead: boolean;
	readAt?: Date;
	createdAt: Date;
	updatedAt: Date;
}

export interface IChatBot extends Document {
	_id: string;
	siteId: string;
	name: string;
	description: string;
	isActive: boolean;
	settings: {
		welcomeMessage: string;
		autoResponse: boolean;
		transferToHuman: boolean;
		transferThreshold: number; // confidence score
		operatingHours: {
			enabled: boolean;
			startTime: string; // HH:mm
			endTime: string; // HH:mm
			timezone: string;
			offlineMessage: string;
		};
	};
	responses: Array<{
		trigger: string;
		response: string;
		confidence: number;
		category: string;
		followUpQuestions?: string[];
	}>;
	createdAt: Date;
	updatedAt: Date;
}

export interface IFAQ extends Document {
	_id: string;
	siteId: string;
	category: string;
	question: string;
	answer: string;
	tags: string[];
	isActive: boolean;
	helpful: {
		yes: number;
		no: number;
	};
	createdAt: Date;
	updatedAt: Date;
}

const LiveChatSessionSchema = new Schema<ILiveChatSession>(
	{
		_id: { type: String, default: generateId },
		siteId: { type: String, required: true, index: true },
		customerId: { type: String, required: true, index: true },
		customerName: { type: String, required: true },
		customerEmail: { type: String },
		agentId: { type: String, index: true },
		agentName: { type: String },
		status: {
			type: String,
			required: true,
			enum: ['waiting', 'active', 'resolved', 'closed'],
			default: 'waiting',
		},
		priority: {
			type: String,
			required: true,
			enum: ['low', 'medium', 'high', 'urgent'],
			default: 'medium',
		},
		category: {
			type: String,
			required: true,
			enum: ['general', 'sales', 'support', 'technical', 'billing'],
			default: 'general',
		},
		subject: { type: String },
		description: { type: String, required: true },
		tags: [{ type: String }],
		rating: { type: Number, min: 1, max: 5 },
		feedback: { type: String },
		metadata: {
			userAgent: { type: String },
			ipAddress: { type: String },
			pageUrl: { type: String },
			referrer: { type: String },
			deviceType: { type: String },
			location: { type: String },
		},
		stats: {
			waitTime: { type: Number, default: 0 },
			chatDuration: { type: Number, default: 0 },
			messageCount: { type: Number, default: 0 },
			responseTime: { type: Number, default: 0 },
		},
		resolvedAt: { type: Date },
		closedAt: { type: Date },
	},
	{
		timestamps: true,
		versionKey: false,
	}
);

const LiveChatMessageSchema = new Schema<ILiveChatMessage>(
	{
		_id: { type: String, default: generateId },
		siteId: { type: String, required: true, index: true },
		sessionId: { type: String, required: true, index: true },
		senderId: { type: String, required: true, index: true },
		senderType: {
			type: String,
			required: true,
			enum: ['customer', 'agent', 'system', 'bot'],
		},
		senderName: { type: String, required: true },
		messageType: {
			type: String,
			required: true,
			enum: ['text', 'image', 'file', 'system', 'typing'],
			default: 'text',
		},
		content: { type: String, required: true },
		attachments: [
			{
				fileName: { type: String, required: true },
				fileUrl: { type: String, required: true },
				fileSize: { type: Number, required: true },
				fileType: { type: String, required: true },
			},
		],
		isRead: { type: Boolean, default: false },
		readAt: { type: Date },
	},
	{
		timestamps: true,
		versionKey: false,
	}
);

const ChatBotSchema = new Schema<IChatBot>(
	{
		_id: { type: String, default: generateId },
		siteId: { type: String, required: true, index: true },
		name: { type: String, required: true },
		description: { type: String },
		isActive: { type: Boolean, default: true },
		settings: {
			welcomeMessage: { type: String, default: 'สวัสดีครับ! มีอะไรให้ช่วยเหลือไหม?' },
			autoResponse: { type: Boolean, default: true },
			transferToHuman: { type: Boolean, default: true },
			transferThreshold: { type: Number, default: 0.7 },
			operatingHours: {
				enabled: { type: Boolean, default: false },
				startTime: { type: String, default: '09:00' },
				endTime: { type: String, default: '18:00' },
				timezone: { type: String, default: 'Asia/Bangkok' },
				offlineMessage: { type: String, default: 'ขออภัย ตอนนี้อยู่นอกเวลาทำการ' },
			},
		},
		responses: [
			{
				trigger: { type: String, required: true },
				response: { type: String, required: true },
				confidence: { type: Number, default: 1.0 },
				category: { type: String },
				followUpQuestions: [{ type: String }],
			},
		],
	},
	{
		timestamps: true,
		versionKey: false,
	}
);

const FAQSchema = new Schema<IFAQ>(
	{
		_id: { type: String, default: generateId },
		siteId: { type: String, required: true, index: true },
		category: { type: String, required: true },
		question: { type: String, required: true },
		answer: { type: String, required: true },
		tags: [{ type: String }],
		isActive: { type: Boolean, default: true },
		helpful: {
			yes: { type: Number, default: 0 },
			no: { type: Number, default: 0 },
		},
	},
	{
		timestamps: true,
		versionKey: false,
	}
);

// Indexes
LiveChatSessionSchema.index({ siteId: 1, status: 1 });
LiveChatSessionSchema.index({ siteId: 1, agentId: 1 });
LiveChatSessionSchema.index({ siteId: 1, priority: 1 });
LiveChatSessionSchema.index({ createdAt: -1 });

LiveChatMessageSchema.index({ siteId: 1, sessionId: 1 });
LiveChatMessageSchema.index({ siteId: 1, senderId: 1 });
LiveChatMessageSchema.index({ createdAt: -1 });

ChatBotSchema.index({ siteId: 1, isActive: 1 });

FAQSchema.index({ siteId: 1, category: 1 });
FAQSchema.index({ siteId: 1, isActive: 1 });
FAQSchema.index({ tags: 1 });

// Static methods
LiveChatSessionSchema.statics.findWaiting = async function (siteId: string) {
	return this.find({ siteId, status: 'waiting' }).sort({ createdAt: 1 });
};

LiveChatSessionSchema.statics.findByAgent = async function (siteId: string, agentId: string) {
	return this.find({ siteId, agentId }).sort({ updatedAt: -1 });
};

LiveChatSessionSchema.statics.findByCustomer = async function (siteId: string, customerId: string) {
	return this.find({ siteId, customerId }).sort({ createdAt: -1 });
};

LiveChatMessageSchema.statics.findBySession = async function (siteId: string, sessionId: string) {
	return this.find({ siteId, sessionId }).sort({ createdAt: 1 });
};

ChatBotSchema.statics.findActive = async function (siteId: string) {
	return this.findOne({ siteId, isActive: true });
};

FAQSchema.statics.findByCategory = async function (siteId: string, category: string) {
	return this.find({ siteId, category, isActive: true });
};

FAQSchema.statics.search = async function (siteId: string, query: string) {
	return this.find({
		siteId,
		isActive: true,
		$or: [
			{ question: { $regex: query, $options: 'i' } },
			{ answer: { $regex: query, $options: 'i' } },
			{ tags: { $in: [new RegExp(query, 'i')] } },
		],
	});
};

export const LiveChatSession = mongoose.model<ILiveChatSession>('LiveChatSession', LiveChatSessionSchema);
export const LiveChatMessage = mongoose.model<ILiveChatMessage>('LiveChatMessage', LiveChatMessageSchema);
export const ChatBot = mongoose.model<IChatBot>('ChatBot', ChatBotSchema);
export const FAQ = mongoose.model<IFAQ>('FAQ', FAQSchema);
