import { LiveChatSession, LiveChatMessage, ChatBot, FAQ } from '../models/livechat.model';
import { logger } from '@/core/utils/logger';

export class LiveChatService {
	// สร้าง session สำหรับ customer support
	async createSession(
		siteId: string,
		customerId: string,
		customerName: string,
		description: string,
		options: {
			customerEmail?: string;
			priority?: 'low' | 'medium' | 'high' | 'urgent';
			category?: 'general' | 'sales' | 'support' | 'technical' | 'billing';
			subject?: string;
			metadata?: {
				userAgent?: string;
				ipAddress?: string;
				pageUrl?: string;
				referrer?: string;
				deviceType?: string;
				location?: string;
			};
		} = {}
	) {
		try {
			const session = new LiveChatSession({
				siteId,
				customerId,
				customerName,
				customerEmail: options.customerEmail,
				description,
				priority: options.priority || 'medium',
				category: options.category || 'general',
				subject: options.subject,
				metadata: options.metadata || {},
				stats: {
					waitTime: 0,
					chatDuration: 0,
					messageCount: 0,
					responseTime: 0,
				},
			});

			await session.save();

			logger.info(`สร้าง LiveChat session สำหรับลูกค้า ${customerName} (${customerId})`);

			return session;
		} catch (error) {
			logger.error('เกิดข้อผิดพลาดในการสร้าง LiveChat session:', error);
			throw error;
		}
	}

	// ดึง sessions
	async getSessions(
		siteId: string,
		filters: {
			customerId?: string;
			agentId?: string;
			status?: 'waiting' | 'active' | 'resolved' | 'closed';
			priority?: 'low' | 'medium' | 'high' | 'urgent';
			category?: 'general' | 'sales' | 'support' | 'technical' | 'billing';
			page?: number;
			limit?: number;
		} = {}
	) {
		try {
			const page = filters.page || 1;
			const limit = filters.limit || 20;
			const skip = (page - 1) * limit;

			const query: any = { siteId };

			if (filters.customerId) query.customerId = filters.customerId;
			if (filters.agentId) query.agentId = filters.agentId;
			if (filters.status) query.status = filters.status;
			if (filters.priority) query.priority = filters.priority;
			if (filters.category) query.category = filters.category;

			const [sessions, total] = await Promise.all([
				LiveChatSession.find(query).sort({ updatedAt: -1 }).skip(skip).limit(limit),
				LiveChatSession.countDocuments(query),
			]);

			return {
				sessions,
				pagination: {
					page,
					limit,
					total,
					totalPages: Math.ceil(total / limit),
				},
			};
		} catch (error) {
			logger.error('เกิดข้อผิดพลาดในการดึง LiveChat sessions:', error);
			throw error;
		}
	}

	// ดึง session ตาม ID
	async getSession(siteId: string, sessionId: string) {
		try {
			const session = await LiveChatSession.findOne({ siteId, _id: sessionId });
			if (!session) {
				throw new Error('ไม่พบ session');
			}
			return session;
		} catch (error) {
			logger.error('เกิดข้อผิดพลาดในการดึง LiveChat session:', error);
			throw error;
		}
	}

	// มอบหมาย agent
	async assignAgent(siteId: string, sessionId: string, agentId: string, agentName: string) {
		try {
			const session = await LiveChatSession.findOneAndUpdate(
				{ siteId, _id: sessionId },
				{
					$set: {
						agentId,
						agentName,
						status: 'active',
					},
				},
				{ new: true }
			);

			if (!session) {
				throw new Error('ไม่พบ session');
			}

			logger.info(`มอบหมาย agent ${agentName} ให้ session ${sessionId}`);

			return session;
		} catch (error) {
			logger.error('เกิดข้อผิดพลาดในการมอบหมาย agent:', error);
			throw error;
		}
	}

	// ส่งข้อความ
	async sendMessage(
		siteId: string,
		sessionId: string,
		senderId: string,
		senderType: 'customer' | 'agent' | 'system' | 'bot',
		senderName: string,
		content: string,
		options: {
			messageType?: 'text' | 'image' | 'file' | 'system';
			attachments?: Array<{
				fileName: string;
				fileUrl: string;
				fileSize: number;
				fileType: string;
			}>;
		} = {}
	) {
		try {
			// ตรวจสอบ session
			const session = await this.getSession(siteId, sessionId);
			if (session.status === 'closed') {
				throw new Error('Session ถูกปิดแล้ว');
			}

			const message = new LiveChatMessage({
				siteId,
				sessionId,
				senderId,
				senderType,
				senderName,
				messageType: options.messageType || 'text',
				content,
				attachments: options.attachments,
			});

			await message.save();

			// อัพเดท session stats
			await LiveChatSession.findByIdAndUpdate(sessionId, {
				$inc: { 'stats.messageCount': 1 },
				$set: { updatedAt: new Date() },
			});

			logger.info(`ส่งข้อความใน LiveChat session ${sessionId} โดย ${senderName}`);

			return message;
		} catch (error) {
			logger.error('เกิดข้อผิดพลาดในการส่งข้อความ LiveChat:', error);
			throw error;
		}
	}

	// ดึงข้อความ
	async getMessages(siteId: string, sessionId: string) {
		try {
			return await LiveChatMessage.find({ siteId, sessionId }).sort({ createdAt: 1 });
		} catch (error) {
			logger.error('เกิดข้อผิดพลาดในการดึงข้อความ LiveChat:', error);
			throw error;
		}
	}

	// ปิด session
	async closeSession(siteId: string, sessionId: string, agentId?: string) {
		try {
			const updateData: any = {
				status: 'closed',
				closedAt: new Date(),
			};

			if (agentId) {
				updateData.agentId = agentId;
			}

			const session = await LiveChatSession.findOneAndUpdate(
				{ siteId, _id: sessionId },
				{ $set: updateData },
				{ new: true }
			);

			if (!session) {
				throw new Error('ไม่พบ session');
			}

			logger.info(`ปิด LiveChat session ${sessionId}`);

			return session;
		} catch (error) {
			logger.error('เกิดข้อผิดพลาดในการปิด LiveChat session:', error);
			throw error;
		}
	}

	// ให้คะแนน session
	async rateSession(siteId: string, sessionId: string, rating: number, feedback?: string) {
		try {
			if (rating < 1 || rating > 5) {
				throw new Error('คะแนนต้องอยู่ระหว่าง 1-5');
			}

			const session = await LiveChatSession.findOneAndUpdate(
				{ siteId, _id: sessionId },
				{
					$set: {
						rating,
						feedback: feedback || '',
					},
				},
				{ new: true }
			);

			if (!session) {
				throw new Error('ไม่พบ session');
			}

			logger.info(`ให้คะแนน ${rating} ดาว สำหรับ session ${sessionId}`);

			return session;
		} catch (error) {
			logger.error('เกิดข้อผิดพลาดในการให้คะแนน session:', error);
			throw error;
		}
	}

	// จัดการ ChatBot
	async getChatBot(siteId: string) {
		try {
			return await ChatBot.findOne({ siteId, isActive: true });
		} catch (error) {
			logger.error('เกิดข้อผิดพลาดในการดึง ChatBot:', error);
			throw error;
		}
	}

	async createChatBot(siteId: string, botData: any) {
		try {
			const bot = new ChatBot({
				siteId,
				...botData,
			});

			await bot.save();

			logger.info(`สร้าง ChatBot สำหรับเว็บไซต์ ${siteId}`);

			return bot;
		} catch (error) {
			logger.error('เกิดข้อผิดพลาดในการสร้าง ChatBot:', error);
			throw error;
		}
	}

	async updateChatBot(siteId: string, botId: string, updates: any) {
		try {
			const bot = await ChatBot.findOneAndUpdate({ siteId, _id: botId }, { $set: updates }, { new: true });

			if (!bot) {
				throw new Error('ไม่พบ ChatBot');
			}

			logger.info(`อัพเดท ChatBot ${botId}`);

			return bot;
		} catch (error) {
			logger.error('เกิดข้อผิดพลาดในการอัพเดท ChatBot:', error);
			throw error;
		}
	}

	// จัดการ FAQ
	async getFAQs(siteId: string, category?: string) {
		try {
			const query: any = { siteId, isActive: true };
			if (category) query.category = category;

			return await FAQ.find(query).sort({ createdAt: -1 });
		} catch (error) {
			logger.error('เกิดข้อผิดพลาดในการดึง FAQ:', error);
			throw error;
		}
	}

	async searchFAQs(siteId: string, searchQuery: string) {
		try {
			return await FAQ.find({
				siteId,
				isActive: true,
				$or: [
					{ question: { $regex: searchQuery, $options: 'i' } },
					{ answer: { $regex: searchQuery, $options: 'i' } },
					{ tags: { $in: [new RegExp(searchQuery, 'i')] } },
				],
			});
		} catch (error) {
			logger.error('เกิดข้อผิดพลาดในการค้นหา FAQ:', error);
			throw error;
		}
	}

	async createFAQ(siteId: string, faqData: any) {
		try {
			const faq = new FAQ({
				siteId,
				...faqData,
			});

			await faq.save();

			logger.info(`สร้าง FAQ ใหม่สำหรับเว็บไซต์ ${siteId}`);

			return faq;
		} catch (error) {
			logger.error('เกิดข้อผิดพลาดในการสร้าง FAQ:', error);
			throw error;
		}
	}

	async updateFAQ(siteId: string, faqId: string, updates: any) {
		try {
			const faq = await FAQ.findOneAndUpdate({ siteId, _id: faqId }, { $set: updates }, { new: true });

			if (!faq) {
				throw new Error('ไม่พบ FAQ');
			}

			logger.info(`อัพเดท FAQ ${faqId}`);

			return faq;
		} catch (error) {
			logger.error('เกิดข้อผิดพลาดในการอัพเดท FAQ:', error);
			throw error;
		}
	}

	async deleteFAQ(siteId: string, faqId: string) {
		try {
			const faq = await FAQ.findOneAndDelete({ siteId, _id: faqId });

			if (!faq) {
				throw new Error('ไม่พบ FAQ');
			}

			logger.info(`ลบ FAQ ${faqId}`);

			return true;
		} catch (error) {
			logger.error('เกิดข้อผิดพลาดในการลบ FAQ:', error);
			throw error;
		}
	}

	// สถิติ
	async getStats(siteId: string, agentId?: string) {
		try {
			const matchQuery: any = { siteId };
			if (agentId) matchQuery.agentId = agentId;

			const stats = await LiveChatSession.aggregate([
				{ $match: matchQuery },
				{
					$group: {
						_id: '$status',
						count: { $sum: 1 },
						avgWaitTime: { $avg: '$stats.waitTime' },
						avgChatDuration: { $avg: '$stats.chatDuration' },
						avgResponseTime: { $avg: '$stats.responseTime' },
						avgRating: { $avg: '$rating' },
					},
				},
			]);

			return stats;
		} catch (error) {
			logger.error('เกิดข้อผิดพลาดในการดึงสถิติ LiveChat:', error);
			throw error;
		}
	}

	// ดึง sessions ที่รอ
	async getWaitingSessions(siteId: string) {
		try {
			return await LiveChatSession.find({ siteId, status: 'waiting' }).sort({ createdAt: 1 });
		} catch (error) {
			logger.error('เกิดข้อผิดพลาดในการดึง sessions ที่รอ:', error);
			throw error;
		}
	}
}
