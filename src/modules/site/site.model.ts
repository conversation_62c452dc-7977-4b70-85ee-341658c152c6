import { generateFileId } from '@/core/utils/idGenerator';
import mongoose, { Schema, type Document } from 'mongoose';

export interface ISite extends Document {
	_id: string;
	typeDomain: 'subdomain' | 'custom'; // ประเภทโดเมน
	fullDomain: string; // domain เต็ม เช่น myshop.mystore.com หรือ mycustom.com
	expiredAt: Date;
	isActive: boolean;
	loadingSettings?: {
		message?: string;
		size?: 'sm' | 'md' | 'lg';
		color?: 'blue' | 'green' | 'red' | 'yellow' | 'purple';
		showLogo?: boolean;
		logoUrl?: string;
		logoSize?: string;
		type?: 'spinner' | 'dots' | 'bars' | 'pulse';
	};
	themeSettings?: {
		primaryColor?: string;
		secondaryColor?: string;
		backgroundColor?: string;
		textColor?: string;
		fontFamily?: string;
		borderRadius?: string;
		spacing?: string;
		layout?: 'grid' | 'list' | 'masonry';
		headerStyle?: 'centered' | 'left' | 'minimal';
		footerStyle?: 'simple' | 'detailed' | 'minimal';
		customCSS?: string;
	};
	seoSettings?: {
		title?: string;
		logo?: string;
		cover?: string;
		description?: string;
		keywords?: string;
		ogImage?: string;
		ogTitle?: string;
		ogDescription?: string;
		twitterCard?: string;
		canonicalUrl?: string;
		robots?: string;
	};
	analyticsSettings?: {
		googleAnalyticsId?: string;
		googleTagManagerId?: string;
		facebookPixelId?: string;
		hotjarId?: string;
		customTrackingCode?: string;
	};
	securitySettings?: {
		sslEnabled?: boolean;
		cspEnabled?: boolean;
		cspPolicy?: string;
		hstsEnabled?: boolean;
		xssProtection?: boolean;
		contentTypeOptions?: boolean;
		referrerPolicy?: string;
	};
	navigationSettings?: {
		showHeader?: boolean;
		showFooter?: boolean;
		showSearch?: boolean;
		showLanguageSelector?: boolean;
		showThemeToggle?: boolean;
		mobileMenuStyle?: 'slide' | 'overlay' | 'dropdown';
		desktopMenuStyle?: 'horizontal' | 'vertical' | 'mega';
	};
	pageSettings?: {
		defaultPageId?: string;
		errorPageId?: string;
		maintenancePageId?: string;
	};
	createdAt: Date;
	updatedAt: Date;
}

const siteSchema = new Schema<ISite>(
	{
		_id: { type: String, default: () => generateFileId(5) },
		expiredAt: { type: Date, required: true },
		isActive: { type: Boolean, default: true },
		typeDomain: {
			type: String,
			enum: ['subdomain', 'custom'],
			default: 'subdomain',
		},
		fullDomain: { type: String, unique: true, required: true },
		loadingSettings: {
			message: { type: String, default: 'กำลังโหลด...' },
			size: { type: String, enum: ['sm', 'md', 'lg'], default: 'md' },
			color: {
				type: String,
				enum: ['blue', 'green', 'red', 'yellow', 'purple'],
				default: 'blue',
			},
			showLogo: { type: Boolean, default: false },
			logoUrl: { type: String, default: '' },
			logoSize: { type: String, default: 'w-8 h-8' },
			type: {
				type: String,
				enum: ['spinner', 'dots', 'bars', 'pulse'],
				default: 'spinner',
			},
		},
		themeSettings: {
			primaryColor: { type: String, default: '#3b82f6' },
			secondaryColor: { type: String, default: '#6b7280' },
			backgroundColor: { type: String, default: '#ffffff' },
			textColor: { type: String, default: '#1f2937' },
			fontFamily: { type: String, default: 'Inter' },
			borderRadius: { type: String, default: '0.375rem' },
			spacing: { type: String, default: '1rem' },
			layout: {
				type: String,
				enum: ['grid', 'list', 'masonry'],
				default: 'grid',
			},
			headerStyle: {
				type: String,
				enum: ['centered', 'left', 'minimal'],
				default: 'centered',
			},
			footerStyle: {
				type: String,
				enum: ['simple', 'detailed', 'minimal'],
				default: 'simple',
			},
			customCSS: { type: String, default: '' },
		},
		seoSettings: {
			title: { type: String, default: '' },
			logo: { type: String, default: 'shoplogo' },
			cover: { type: String, default: 'shopcover' },
			description: { type: String, default: '' },
			keywords: { type: String, default: '' },
			ogImage: { type: String, default: 'websitebanner' },
			ogTitle: { type: String, default: '' },
			ogDescription: { type: String, default: '' },
			twitterCard: { type: String, default: 'summary_large_image' },
			canonicalUrl: { type: String, default: '' },
			robots: { type: String, default: 'index, follow' },
		},
		analyticsSettings: {
			googleAnalyticsId: { type: String, default: '' },
			googleTagManagerId: { type: String, default: '' },
			facebookPixelId: { type: String, default: '' },
			hotjarId: { type: String, default: '' },
			customTrackingCode: { type: String, default: '' },
		},
		securitySettings: {
			sslEnabled: { type: Boolean, default: true },
			cspEnabled: { type: Boolean, default: false },
			cspPolicy: { type: String, default: '' },
			hstsEnabled: { type: Boolean, default: true },
			xssProtection: { type: Boolean, default: true },
			contentTypeOptions: { type: Boolean, default: true },
			referrerPolicy: {
				type: String,
				default: 'strict-origin-when-cross-origin',
			},
		},
		navigationSettings: {
			showHeader: { type: Boolean, default: true },
			showFooter: { type: Boolean, default: true },
			showSearch: { type: Boolean, default: true },
			showLanguageSelector: { type: Boolean, default: true },
			showThemeToggle: { type: Boolean, default: true },
			mobileMenuStyle: {
				type: String,
				enum: ['slide', 'overlay', 'dropdown'],
				default: 'slide',
			},
			desktopMenuStyle: {
				type: String,
				enum: ['horizontal', 'vertical', 'mega'],
				default: 'horizontal',
			},
		},
		pageSettings: {
			defaultPageId: { type: String, ref: 'Page' },
			errorPageId: { type: String, ref: 'Page' },
			maintenancePageId: { type: String, ref: 'Page' },
		},
	},
	{
		timestamps: true,
		id: false,
		versionKey: false,
		toJSON: {
			virtuals: true,
		},
		toObject: {
			virtuals: true,
		},
	}
);

// Virtual fields for populate
siteSchema.virtual('menuItems', {
	ref: 'MenuItem',
	localField: '_id',
	foreignField: 'siteId',
	options: { sort: { order: 1 } },
});

siteSchema.virtual('pages', {
	ref: 'Page',
	localField: '_id',
	foreignField: 'siteId',
	options: { sort: { order: 1 } },
});

siteSchema.virtual('defaultPage', {
	ref: 'Page',
	localField: 'pageSettings.defaultPageId',
	foreignField: '_id',
	justOne: true,
});

siteSchema.virtual('errorPage', {
	ref: 'Page',
	localField: 'pageSettings.errorPageId',
	foreignField: '_id',
	justOne: true,
});

siteSchema.virtual('maintenancePage', {
	ref: 'Page',
	localField: 'pageSettings.maintenancePageId',
	foreignField: '_id',
	justOne: true,
});

// Indexes
// siteSchema.index({ fullDomain: 1 }, { unique: true });
// siteSchema.index({ isActive: 1 });
// siteSchema.index({ typeDomain: 1 });
// siteSchema.index({ expiredAt: 1 });

export const Site = mongoose.model<ISite>('Site', siteSchema);
