import { describe, it, expect, beforeEach, afterEach } from 'bun:test';
import { createSite, renewSite, getPackageInfo } from './site.service';
import { Site } from './site.model';
// Mock dependencies
const mockUser = {
	_id: 'user123',
	moneyPoint: 1000,
	save: async () => mockUser,
};

const mockSite = {
	_id: 'site123',
	userId: 'user123',
	fullDomain: 'test.com',
	expiredAt: new Date(),
	save: async () => mockSite,
};

// Mock models
const mockUserModel = {
	findById: async (id: string) => (id === 'user123' ? mockUser : null),
};

const mockSiteModel = {
	create: async (data: any) => ({ ...data, _id: 'site123' }),
	findOne: async (query: any) => {
		if (query.fullDomain === 'existing.com') return mockSite;
		return null;
	},
};

const mockSitePackageHistoryModel = {
	create: async (data: any) => ({ ...data, _id: 'history123' }),
};

const mockAddCustomer = async (siteId: string, userId: string, role: string) => {
	return { success: true };
};

const mockRecordSitePackageHistory = async (data: any) => {
	return { success: true };
};

describe('Site Service - Package System', () => {
	beforeEach(() => {
		// Reset mock data
		mockUser.moneyPoint = 1000;
	});

	afterEach(() => {
		// Clean up
	});

	describe('getPackageInfo', () => {
		it('should return all package information', () => {
			const result = getPackageInfo();

			expect(result.success).toBe(true);
			expect(result.data.packages).toHaveLength(5);
			expect(result.data.packages[0].id).toBe('daily');
			expect(result.data.packages[0].moneyPoint).toBe(19);
			expect(result.data.packages[4].id).toBe('permanent');
			expect(result.data.packages[4].moneyPoint).toBe(9999);
		});
	});

	describe('createSite', () => {
		it('should create site with daily package', async () => {
			const siteData = {
				packageType: 'daily' as const,
				userId: 'user123',
				name: 'Test Site',
				domain: 'test.com',
				typeDomain: 'subdomain' as const,
				subDomain: 'test',
				mainDomain: 'example.com',
			};

			const result = await createSite(siteData);

			expect(result.success).toBe(true);
			expect(result.moneyPointUsed).toBe(19);
			expect(result.remainingMoneyPoint).toBe(981); // 1000 - 19
		});

		it('should throw error when user has insufficient moneyPoint', async () => {
			mockUser.moneyPoint = 10; // Less than required 19

			const siteData = {
				packageType: 'daily' as const,
				userId: 'user123',
				name: 'Test Site',
				domain: 'test.com',
			};

			await expect(createSite(siteData)).rejects.toThrow('moneyPoint ไม่เพียงพอ');
		});

		it('should throw error when user not found', async () => {
			const siteData = {
				packageType: 'daily' as const,
				userId: 'nonexistent',
				name: 'Test Site',
				domain: 'test.com',
			};

			await expect(createSite(siteData)).rejects.toThrow('ไม่พบผู้ใช้');
		});

		it('should create permanent site with 100 years expiry', async () => {
			const siteData = {
				packageType: 'permanent' as const,
				userId: 'user123',
				name: 'Test Site',
				domain: 'test.com',
			};

			const result = await createSite(siteData);

			expect(result.success).toBe(true);
			expect(result.moneyPointUsed).toBe(9999);
			expect(result.remainingMoneyPoint).toBe(1); // 1000 - 9999
		});
	});

	describe('renewSite', () => {
		it('should renew existing site', async () => {
			const renewData = {
				packageType: 'yearly' as const,
				userId: 'user123',
				domain: 'existing.com',
			};

			const result = await renewSite(renewData);

			expect(result.success).toBe(true);
			expect(result.message).toBe('ต่ออายุเว็บไซต์สำเร็จ');
			expect(result.moneyPointUsed).toBe(1999);
			expect(result.remainingMoneyPoint).toBe(-999); // 1000 - 1999
		});

		it('should throw error when site not found', async () => {
			const renewData = {
				packageType: 'monthly' as const,
				userId: 'user123',
				domain: 'nonexistent.com',
			};

			await expect(renewSite(renewData)).rejects.toThrow('ไม่พบเว็บไซต์');
		});

		it('should throw error when user is not owner', async () => {
			// Mock site with different userId
			const mockSiteWithDifferentOwner = {
				...mockSite,
				userId: 'otheruser',
			};

			const renewData = {
				packageType: 'monthly' as const,
				userId: 'user123',
				domain: 'existing.com',
			};

			await expect(renewSite(renewData)).rejects.toThrow('ไม่มีสิทธิ์ต่ออายุเว็บไซต์นี้');
		});

		it('should throw error when user has insufficient moneyPoint', async () => {
			mockUser.moneyPoint = 100; // Less than required 1999 for yearly

			const renewData = {
				packageType: 'yearly' as const,
				userId: 'user123',
				domain: 'existing.com',
			};

			await expect(renewSite(renewData)).rejects.toThrow('moneyPoint ไม่เพียงพอ');
		});
	});
});
