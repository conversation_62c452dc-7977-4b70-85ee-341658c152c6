import { Elysia, t } from 'elysia';
import { bearer } from '@elysiajs/bearer';
import * as jose from 'jose';
import { config } from '@/core/config/environment';
import { HttpError } from '@/core/utils/error';
import { createChatRoom, sendMessage, getChatMessages, getUserChatRooms, markMessageAsRead, markAllMessagesAsRead, deleteMessage, leaveChatRoom, searchUsersForChat } from './chat.service';
import { addConnection, removeConnection, joinChatRoom, leaveChatRoom as leaveRoom, broadcastToRoom, getConnection, getOnlineUsersInSite } from './websocket.service';
// Middleware สำหรับตรวจสอบ JWT
const authMiddleware = async (bearer: string | undefined) => {
	if (!bearer) throw new HttpError(401, 'ไม่พบ token');

	try {
		const { payload } = await jose.jwtVerify(bearer, new TextEncoder().encode(config.jwtSecret));
		return payload;
	} catch (_error) {
		throw new HttpError(401, 'Token ไม่ถูกต้องหรือหมดอายุ');
	}
};

export const chatRoutes = new Elysia({ prefix: '/chat' })
	.use(bearer())

	// WebSocket endpoint สำหรับ real-time chat
	.ws('/ws', {
		body: t.Object({
			type: t.String(),
			data: t.Optional(t.Any()),
		}),

		async open(_ws) {
			console.log('WebSocket connection opened');
		},

		async message(ws, message) {
			try {
				const { type, data } = message;

				switch (type) {
					case 'auth':
						await handleAuth(ws, data);
						break;

					case 'join_room':
						await handleJoinRoom(ws, data);
						break;

					case 'leave_room':
						await handleLeaveRoom(ws, data);
						break;

					case 'send_message':
						await handleSendMessage(ws, data);
						break;

					case 'typing_start':
						await handleTypingStart(ws, data);
						break;

					case 'typing_stop':
						await handleTypingStop(ws, data);
						break;

					case 'mark_read':
						await handleMarkRead(ws, data);
						break;

					default:
						ws.send(
							JSON.stringify({
								type: 'error',
								message: 'ประเภทข้อความไม่ถูกต้อง',
							})
						);
				}
			} catch (error) {
				console.error('WebSocket message error:', error);
				ws.send(
					JSON.stringify({
						type: 'error',
						message: error instanceof HttpError ? error.message : 'เกิดข้อผิดพลาด',
					})
				);
			}
		},

		async close(ws) {
			const connectionId = (ws as any).connectionId;
			if (connectionId) {
				removeConnection(connectionId);
			}
			console.log('WebSocket connection closed');
		},
	})

	// REST API endpoints

	// สร้างห้องแชทใหม่
	.post(
		'/rooms',
		async ({ bearer, body }) => {
			const auth = await authMiddleware(bearer);
			const { targetUserId, targetUserType, roomType } = body;

			const siteId = (auth as any).siteId || 'default';
			const currentUserId = (auth as any).userId || (auth as any).customerId;
			const currentUserType = (auth as any).userId ? 'user' : 'customer';

			const participants: any = [
				{ userId: currentUserId, userType: currentUserType },
				{ userId: targetUserId, userType: targetUserType },
			];

			const chatRoom = await createChatRoom(siteId, roomType, participants);

			return {
				success: true,
				message: 'สร้างห้องแชทสำเร็จ',
				data: { chatRoom },
			};
		},
		{
			body: t.Object({
				targetUserId: t.String(),
				targetUserType: t.Union([t.Literal('user'), t.Literal('customer')]),
				roomType: t.Union([t.Literal('user_customer'), t.Literal('customer_customer')]),
			}),
		}
	)

	// ดึงรายการห้องแชท
	.get('/rooms', async ({ bearer, _query }) => {
		const auth = await authMiddleware(bearer);
		const currentUserId = (auth as any).userId || (auth as any).customerId;
		const currentUserType = (auth as any).userId ? 'user' : 'customer';
		const siteId = (auth as any).siteId;

		const chatRooms = await getUserChatRooms(currentUserId, currentUserType, siteId);

		return {
			success: true,
			message: 'ดึงรายการห้องแชทสำเร็จ',
			data: { chatRooms },
		};
	})

	// ดึงข้อความในห้องแชท
	.get(
		'/rooms/:roomId/messages',
		async ({ bearer, params, query }) => {
			const auth = await authMiddleware(bearer);
			const { roomId } = params;
			const { page = 1, limit = 50 } = query;

			const currentUserId = (auth as any).userId || (auth as any).customerId;
			const currentUserType = (auth as any).userId ? 'user' : 'customer';

			const result = await getChatMessages(
				roomId,
				currentUserId,
				currentUserType,
				parseInt(page.toString()),
				parseInt(limit.toString())
			);

			return {
				success: true,
				message: 'ดึงข้อความสำเร็จ',
				data: result,
			};
		},
		{
			params: t.Object({
				roomId: t.String(),
			}),
			query: t.Object({
				page: t.Optional(t.Numeric()),
				limit: t.Optional(t.Numeric()),
			}),
		}
	)

	// อ่านข้อความทั้งหมดในห้อง
	.post(
		'/rooms/:roomId/read',
		async ({ bearer, params }) => {
			const auth = await authMiddleware(bearer);
			const { roomId } = params;

			const currentUserId = (auth as any).userId || (auth as any).customerId;
			const currentUserType = (auth as any).userId ? 'user' : 'customer';

			await markAllMessagesAsRead(roomId, currentUserId, currentUserType);

			// แจ้งผู้ใช้อื่นในห้องว่ามีการอ่านข้อความ
			broadcastToRoom(roomId, {
				type: 'messages_read',
				data: {
					userId: currentUserId,
					userType: currentUserType,
					chatRoomId: roomId,
				},
			});

			return {
				success: true,
				message: 'อ่านข้อความทั้งหมดสำเร็จ',
			};
		},
		{
			params: t.Object({
				roomId: t.String(),
			}),
		}
	)

	// ลบข้อความ
	.delete(
		'/messages/:messageId',
		async ({ bearer, params }) => {
			const auth = await authMiddleware(bearer);
			const { messageId } = params;

			const currentUserId = (auth as any).userId || (auth as any).customerId;
			const currentUserType = (auth as any).userId ? 'user' : 'customer';

			const message = await deleteMessage(messageId, currentUserId, currentUserType);

			// แจ้งผู้ใช้อื่นในห้องว่ามีการลบข้อความ
			broadcastToRoom(message.chatRoomId, {
				type: 'message_deleted',
				data: {
					messageId,
					deletedBy: currentUserId,
					deletedByType: currentUserType,
				},
			});

			return {
				success: true,
				message: 'ลบข้อความสำเร็จ',
			};
		},
		{
			params: t.Object({
				messageId: t.String(),
			}),
		}
	)

	// ออกจากห้องแชท
	.post(
		'/rooms/:roomId/leave',
		async ({ bearer, params }) => {
			const auth = await authMiddleware(bearer);
			const { roomId } = params;

			const currentUserId = (auth as any).userId || (auth as any).customerId;
			const currentUserType = (auth as any).userId ? 'user' : 'customer';

			await leaveChatRoom(roomId, currentUserId, currentUserType);

			// แจ้งผู้ใช้อื่นในห้องว่ามีคนออกจากห้อง
			broadcastToRoom(roomId, {
				type: 'user_left',
				data: {
					userId: currentUserId,
					userType: currentUserType,
					chatRoomId: roomId,
				},
			});

			return {
				success: true,
				message: 'ออกจากห้องแชทสำเร็จ',
			};
		},
		{
			params: t.Object({
				roomId: t.String(),
			}),
		}
	)

	// ค้นหาผู้ใช้สำหรับเริ่มแชท
	.get(
		'/search-users',
		async ({ bearer, query }) => {
			const auth = await authMiddleware(bearer);
			const { q: searchTerm } = query;

			if (!searchTerm || searchTerm.length < 2) {
				throw new HttpError(400, 'กรุณาใส่คำค้นหาอย่างน้อย 2 ตัวอักษร');
			}

			const siteId = (auth as any).siteId || 'default';
			const currentUserType = (auth as any).userId ? 'user' : 'customer';

			const users = await searchUsersForChat(siteId, searchTerm, currentUserType);

			return {
				success: true,
				message: 'ค้นหาผู้ใช้สำเร็จ',
				data: { users },
			};
		},
		{
			query: t.Object({
				q: t.String(),
			}),
		}
	)

	// ดึงรายการผู้ใช้ออนไลน์
	.get('/online-users', async ({ bearer }) => {
		const auth = await authMiddleware(bearer);
		const siteId = (auth as any).siteId || 'default';

		const onlineUsers = getOnlineUsersInSite(siteId);

		return {
			success: true,
			message: 'ดึงรายการผู้ใช้ออนไลน์สำเร็จ',
			data: { onlineUsers },
		};
	});

// WebSocket message handlers

async function handleAuth(ws: any, data: any) {
	try {
		const { token } = data;
		const { payload } = await jose.jwtVerify(token, new TextEncoder().encode(config.jwtSecret));

		const userId = (payload as any).userId || (payload as any).customerId;
		const userType = (payload as any).userId ? 'user' : 'customer';
		const siteId = (payload as any).siteId || 'default';

		const connectionId = addConnection(ws, userId, userType, siteId);
		ws.connectionId = connectionId;

		ws.send(
			JSON.stringify({
				type: 'auth_success',
				data: {
					connectionId,
					userId,
					userType,
					siteId,
				},
			})
		);
	} catch (_error) {
		ws.send(
			JSON.stringify({
				type: 'auth_error',
				message: 'การยืนยันตัวตนล้มเหลว',
			})
		);
	}
}

async function handleJoinRoom(ws: any, data: any) {
	const { chatRoomId } = data;
	const connectionId = ws.connectionId;

	if (!connectionId) {
		ws.send(
			JSON.stringify({
				type: 'error',
				message: 'กรุณายืนยันตัวตนก่อน',
			})
		);
		return;
	}

	joinChatRoom(connectionId, chatRoomId);

	ws.send(
		JSON.stringify({
			type: 'room_joined',
			data: { chatRoomId },
		})
	);

	// แจ้งผู้ใช้อื่นในห้องว่ามีคนเข้าร่วม
	const connection = getConnection(connectionId);
	if (connection) {
		broadcastToRoom(
			chatRoomId,
			{
				type: 'user_joined',
				data: {
					userId: connection.userId,
					userType: connection.userType,
					chatRoomId,
				},
			},
			connectionId
		);
	}
}

async function handleLeaveRoom(ws: any, _data: any) {
	const connectionId = ws.connectionId;

	if (!connectionId) return;

	const connection = getConnection(connectionId);
	if (connection && connection.chatRoomId) {
		const chatRoomId = connection.chatRoomId;

		leaveRoom(connectionId);

		ws.send(
			JSON.stringify({
				type: 'room_left',
				data: { chatRoomId },
			})
		);

		// แจ้งผู้ใช้อื่นในห้องว่ามีคนออกจากห้อง
		broadcastToRoom(chatRoomId, {
			type: 'user_left',
			data: {
				userId: connection.userId,
				userType: connection.userType,
				chatRoomId,
			},
		});
	}
}

async function handleSendMessage(ws: any, data: any) {
	const connectionId = ws.connectionId;

	if (!connectionId) {
		ws.send(
			JSON.stringify({
				type: 'error',
				message: 'กรุณายืนยันตัวตนก่อน',
			})
		);
		return;
	}

	const connection = getConnection(connectionId);
	if (!connection || !connection.chatRoomId) {
		ws.send(
			JSON.stringify({
				type: 'error',
				message: 'กรุณาเข้าร่วมห้องแชทก่อน',
			})
		);
		return;
	}

	const { message, messageType = 'text', fileData } = data;

	try {
		const newMessage = await sendMessage(
			connection.chatRoomId,
			connection.userId,
			connection.userType,
			message,
			messageType,
			fileData
		);

		// ส่งข้อความไปยังทุกคนในห้อง
		broadcastToRoom(connection.chatRoomId, {
			type: 'new_message',
			data: newMessage,
		});
	} catch (error) {
		ws.send(
			JSON.stringify({
				type: 'error',
				message: error instanceof HttpError ? error.message : 'ไม่สามารถส่งข้อความได้',
			})
		);
	}
}

async function handleTypingStart(ws: any, _data: any) {
	const connectionId = ws.connectionId;
	const connection = getConnection(connectionId);

	if (connection && connection.chatRoomId) {
		broadcastToRoom(
			connection.chatRoomId,
			{
				type: 'typing_start',
				data: {
					userId: connection.userId,
					userType: connection.userType,
					chatRoomId: connection.chatRoomId,
				},
			},
			connectionId
		);
	}
}

async function handleTypingStop(ws: any, _data: any) {
	const connectionId = ws.connectionId;
	const connection = getConnection(connectionId);

	if (connection && connection.chatRoomId) {
		broadcastToRoom(
			connection.chatRoomId,
			{
				type: 'typing_stop',
				data: {
					userId: connection.userId,
					userType: connection.userType,
					chatRoomId: connection.chatRoomId,
				},
			},
			connectionId
		);
	}
}

async function handleMarkRead(ws: any, data: any) {
	const connectionId = ws.connectionId;
	const connection = getConnection(connectionId);
	const { messageId } = data;

	if (!connection) return;

	try {
		await markMessageAsRead(messageId, connection.userId, connection.userType);

		// แจ้งผู้ใช้อื่นในห้องว่ามีการอ่านข้อความ
		if (connection.chatRoomId) {
			broadcastToRoom(
				connection.chatRoomId,
				{
					type: 'message_read',
					data: {
						messageId,
						userId: connection.userId,
						userType: connection.userType,
					},
				},
				connectionId
			);
		}
	} catch (error) {
		ws.send(
			JSON.stringify({
				type: 'error',
				message: error instanceof HttpError ? error.message : 'ไม่สามารถอ่านข้อความได้',
			})
		);
	}
}
