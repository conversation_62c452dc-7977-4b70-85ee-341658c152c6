import { ChatRoom, Message } from './chat.model';
import { User } from '@/modules/user/user.model';
import { Customer } from '@/modules/customer/customer.model';
import { HttpError } from '@/core/utils/error';
import { generateFileId } from '@/core/utils/idGenerator';

const defaultDeps = {
	ChatRoom,
	Message,
	User,
	Customer,
};

// สร้างห้องแชทใหม่
export async function createChatRoom(
	siteId: string,
	roomType: 'user_customer' | 'customer_customer',
	participants: Array<{ userId: string; userType: 'user' | 'customer' }>,
	deps = defaultDeps
) {
	// ตรวจสอบว่ามีห้องแชทอยู่แล้วหรือไม่
	const participantIds = participants.map(p => p.userId).sort();
	const existingRoom = await deps.ChatRoom.findOne({
		siteId,
		roomType,
		'participants.userId': { $all: participantIds },
		isActive: true,
	});

	if (existingRoom) {
		return existingRoom;
	}

	// สร้างห้องแชทใหม่
	const chatRoom = await deps.ChatRoom.create({
		_id: generateFileId(8),
		siteId,
		roomType,
		participants: participants.map(p => ({
			userId: p.userId,
			userType: p.userType,
			joinedAt: new Date(),
			isActive: true,
		})),
		isActive: true,
	});

	return chatRoom;
}

// ส่งข้อความ
export async function sendMessage(
	chatRoomId: string,
	senderId: string,
	senderType: 'user' | 'customer',
	message: string,
	messageType: 'text' | 'image' | 'file' = 'text',
	fileData?: { fileUrl: string; fileName: string; fileSize: number },
	deps = defaultDeps
) {
	// ตรวจสอบว่าห้องแชทมีอยู่และผู้ส่งเป็นสมาชิกของห้อง
	const chatRoom = await deps.ChatRoom.findOne({
		_id: chatRoomId,
		'participants.userId': senderId,
		'participants.userType': senderType,
		isActive: true,
	});

	if (!chatRoom) {
		throw new HttpError(404, 'ไม่พบห้องแชทหรือคุณไม่มีสิทธิ์เข้าถึง');
	}

	// สร้างข้อความใหม่
	const newMessage = await deps.Message.create({
		_id: generateFileId(10),
		chatRoomId,
		senderId,
		senderType,
		message,
		messageType,
		...(fileData && {
			fileUrl: fileData.fileUrl,
			fileName: fileData.fileName,
			fileSize: fileData.fileSize,
		}),
		isRead: false,
		readBy: [],
	});

	// อัปเดตข้อความล่าสุดในห้องแชท
	await deps.ChatRoom.findByIdAndUpdate(chatRoomId, {
		lastMessage: {
			message,
			senderId,
			senderType,
			sentAt: new Date(),
		},
	});

	return newMessage;
}

// ดึงข้อความในห้องแชท
export async function getChatMessages(
	chatRoomId: string,
	userId: string,
	userType: 'user' | 'customer',
	page: number = 1,
	limit: number = 50,
	deps = defaultDeps
) {
	// ตรวจสอบสิทธิ์เข้าถึงห้องแชท
	const chatRoom = await deps.ChatRoom.findOne({
		_id: chatRoomId,
		'participants.userId': userId,
		'participants.userType': userType,
		isActive: true,
	});

	if (!chatRoom) {
		throw new HttpError(404, 'ไม่พบห้องแชทหรือคุณไม่มีสิทธิ์เข้าถึง');
	}

	const skip = (page - 1) * limit;

	const messages = await deps.Message.find({
		chatRoomId,
		isDeleted: false,
	})
		.sort({ createdAt: -1 })
		.skip(skip)
		.limit(limit)
		.lean();

	const total = await deps.Message.countDocuments({
		chatRoomId,
		isDeleted: false,
	});

	return {
		messages: messages.reverse(), // กลับลำดับให้เป็นเก่า -> ใหม่
		pagination: {
			page,
			limit,
			total,
			totalPages: Math.ceil(total / limit),
		},
	};
}

// ดึงรายการห้องแชทของผู้ใช้
export async function getUserChatRooms(
	userId: string,
	userType: 'user' | 'customer',
	siteId?: string,
	deps = defaultDeps
) {
	const query: any = {
		'participants.userId': userId,
		'participants.userType': userType,
		isActive: true,
	};

	if (siteId) {
		query.siteId = siteId;
	}

	const chatRooms = await deps.ChatRoom.find(query).sort({ updatedAt: -1 }).lean();

	// เพิ่มข้อมูลผู้เข้าร่วมแต่ละห้อง
	const roomsWithParticipants = await Promise.all(
		chatRooms.map(async room => {
			const participantsData = await Promise.all(
				room.participants.map(async participant => {
					if (participant.userType === 'user') {
						const user = await deps.User.findById(participant.userId).select('firstName lastName email avatar').lean();
						return {
							...participant,
							userData: user,
						};
					} else {
						const customer = await deps.Customer.findById(participant.userId)
							.select('firstName lastName email avatar')
							.lean();
						return {
							...participant,
							customerData: customer,
						};
					}
				})
			);

			return {
				...room,
				participants: participantsData,
			};
		})
	);

	return roomsWithParticipants;
}

// อ่านข้อความ
export async function markMessageAsRead(
	messageId: string,
	userId: string,
	userType: 'user' | 'customer',
	deps = defaultDeps
) {
	const message = await deps.Message.findById(messageId);
	if (!message) {
		throw new HttpError(404, 'ไม่พบข้อความ');
	}

	// ตรวจสอบว่าผู้ใช้อยู่ในห้องแชทหรือไม่
	const chatRoom = await deps.ChatRoom.findOne({
		_id: message.chatRoomId,
		'participants.userId': userId,
		'participants.userType': userType,
		isActive: true,
	});

	if (!chatRoom) {
		throw new HttpError(403, 'คุณไม่มีสิทธิ์อ่านข้อความนี้');
	}

	// ตรวจสอบว่าอ่านแล้วหรือยัง
	const alreadyRead = message.readBy.some(read => read.userId === userId && read.userType === userType);

	if (!alreadyRead) {
		message.readBy.push({
			userId,
			userType,
			readAt: new Date(),
		});
		await message.save();
	}

	return message;
}

// อ่านข้อความทั้งหมดในห้องแชท
export async function markAllMessagesAsRead(
	chatRoomId: string,
	userId: string,
	userType: 'user' | 'customer',
	deps = defaultDeps
) {
	// ตรวจสอบสิทธิ์เข้าถึงห้องแชท
	const chatRoom = await deps.ChatRoom.findOne({
		_id: chatRoomId,
		'participants.userId': userId,
		'participants.userType': userType,
		isActive: true,
	});

	if (!chatRoom) {
		throw new HttpError(404, 'ไม่พบห้องแชทหรือคุณไม่มีสิทธิ์เข้าถึง');
	}

	// อัปเดตข้อความที่ยังไม่ได้อ่าน
	await deps.Message.updateMany(
		{
			chatRoomId,
			'readBy.userId': { $ne: userId },
			'readBy.userType': { $ne: userType },
			isDeleted: false,
		},
		{
			$push: {
				readBy: {
					userId,
					userType,
					readAt: new Date(),
				},
			},
		}
	);

	return { success: true };
}

// ลบข้อความ
export async function deleteMessage(
	messageId: string,
	userId: string,
	userType: 'user' | 'customer',
	deps = defaultDeps
) {
	const message = await deps.Message.findOne({
		_id: messageId,
		senderId: userId,
		senderType: userType,
		isDeleted: false,
	});

	if (!message) {
		throw new HttpError(404, 'ไม่พบข้อความหรือคุณไม่มีสิทธิ์ลบ');
	}

	message.isDeleted = true;
	message.deletedAt = new Date();
	await message.save();

	return message;
}

// ออกจากห้องแชท
export async function leaveChatRoom(
	chatRoomId: string,
	userId: string,
	userType: 'user' | 'customer',
	deps = defaultDeps
) {
	const chatRoom = await deps.ChatRoom.findById(chatRoomId);
	if (!chatRoom) {
		throw new HttpError(404, 'ไม่พบห้องแชท');
	}

	// อัปเดตสถานะผู้เข้าร่วม
	const participantIndex = chatRoom.participants.findIndex(p => p.userId === userId && p.userType === userType);

	if (participantIndex === -1) {
		throw new HttpError(404, 'คุณไม่ได้อยู่ในห้องแชทนี้');
	}

	chatRoom.participants[participantIndex].isActive = false;

	// ถ้าไม่มีผู้เข้าร่วมที่ active แล้ว ให้ปิดห้องแชท
	const activeParticipants = chatRoom.participants.filter(p => p.isActive);
	if (activeParticipants.length === 0) {
		chatRoom.isActive = false;
	}

	await chatRoom.save();
	return chatRoom;
}

// ค้นหาผู้ใช้สำหรับเริ่มแชท
export async function searchUsersForChat(
	siteId: string,
	searchTerm: string,
	searcherType: 'user' | 'customer',
	deps = defaultDeps
) {
	let results = [];

	if (searcherType === 'user') {
		// User ค้นหา Customer
		results = await deps.Customer.find({
			siteId,
			$or: [
				{ firstName: { $regex: searchTerm, $options: 'i' } },
				{ lastName: { $regex: searchTerm, $options: 'i' } },
				{ email: { $regex: searchTerm, $options: 'i' } },
			],
			isEmailVerified: true,
		})
			.select('firstName lastName email avatar')
			.limit(10)
			.lean();
	} else {
		// Customer ค้นหา Customer อื่น
		results = await deps.Customer.find({
			siteId,
			$or: [
				{ firstName: { $regex: searchTerm, $options: 'i' } },
				{ lastName: { $regex: searchTerm, $options: 'i' } },
				{ email: { $regex: searchTerm, $options: 'i' } },
			],
			isEmailVerified: true,
		})
			.select('firstName lastName email avatar')
			.limit(10)
			.lean();
	}

	return results;
}
