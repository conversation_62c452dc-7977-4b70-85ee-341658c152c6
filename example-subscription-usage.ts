// ตัวอย่างการใช้งาน Site Package Subscription System

import {
	createSiteSubscription,
	getUserSubscriptions,
	toggleAutoRenew,
	renewSubscription,
	getNotifications,
} from '@/modules/subscription/subscription.service';

// 1. Site Owner เช่าแพ็คเกจรายเดือนพร้อม auto-renewal
async function subscribeMonthlyPackage() {
	try {
		const subscription = await createSiteSubscription(
			'site123', // siteId
			'user456', // userId (site owner)
			'monthly', // packageType
			{
				autoRenew: true,
				paymentMethod: 'moneyPoint',
				discountCode: 'SAVE10', // รหัสส่วนลด (optional)
			}
		);

		console.log('✅ สมัครแพ็คเกจสำเร็จ:', {
			subscriptionId: subscription._id,
			packageType: subscription.packageType,
			amount: subscription.pricing.amount,
			autoRenew: subscription.autoRenew,
			nextRenewal: subscription.nextRenewalDate,
		});

		return subscription;
	} catch (error) {
		console.error('❌ สมัครแพ็คเกจล้มเหลว:', error.message);
		throw error;
	}
}

// 2. ดึงรายการ subscription ของ user
async function getMySubscriptions() {
	try {
		const result = await getUserSubscriptions('user456', {
			page: 1,
			limit: 10,
			status: 'active',
		});

		console.log('📋 รายการ Subscription:', {
			total: result.pagination.total,
			subscriptions: result.subscriptions.map(sub => ({
				id: sub._id,
				site: sub.siteId.name,
				package: sub.packageType,
				status: sub.status,
				autoRenew: sub.autoRenew,
				nextRenewal: sub.nextRenewalDate,
				totalSpent: sub.stats.totalSpent,
			})),
		});

		return result;
	} catch (error) {
		console.error('❌ ดึงรายการล้มเหลว:', error.message);
		throw error;
	}
}

// 3. เปิด/ปิด Auto Renewal
async function manageAutoRenewal() {
	try {
		// เปิด auto renewal
		const subscription = await toggleAutoRenew(
			'site123', // siteId
			'sub789', // subscriptionId
			'user456', // userId
			true // autoRenew = true
		);

		console.log('🔄 เปิด Auto Renewal สำเร็จ:', {
			subscriptionId: subscription._id,
			autoRenew: subscription.autoRenew,
			nextRenewal: subscription.nextRenewalDate,
		});

		// ปิด auto renewal
		await toggleAutoRenew('site123', 'sub789', 'user456', false);
		console.log('⏹️ ปิด Auto Renewal สำเร็จ');
	} catch (error) {
		console.error('❌ จัดการ Auto Renewal ล้มเหลว:', error.message);
		throw error;
	}
}

// 4. ต่ออายุแบบ Manual
async function manualRenewal() {
	try {
		const subscription = await renewSubscription('sub789');

		console.log('🔄 ต่ออายุสำเร็จ:', {
			subscriptionId: subscription._id,
			newExpiry: subscription.endDate,
			totalRenewals: subscription.stats.totalRenewals,
			totalSpent: subscription.stats.totalSpent,
		});

		return subscription;
	} catch (error) {
		console.error('❌ ต่ออายุล้มเหลว:', error.message);
		throw error;
	}
}

// 5. ดึงการแจ้งเตือน
async function getMyNotifications() {
	try {
		const result = await getNotifications('site123', 'user456', {
			page: 1,
			limit: 20,
			unreadOnly: true,
		});

		console.log('🔔 การแจ้งเตือน:', {
			unread: result.notifications.filter(n => !n.isRead).length,
			notifications: result.notifications.map(notif => ({
				type: notif.type,
				title: notif.title,
				message: notif.message,
				isRead: notif.isRead,
				sentAt: notif.sentAt,
			})),
		});

		return result;
	} catch (error) {
		console.error('❌ ดึงการแจ้งเตือนล้มเหลว:', error.message);
		throw error;
	}
}

// 6. ตัวอย่างการใช้งานใน Route Handler
export async function siteSubscriptionHandler() {
	console.log('🚀 เริ่มต้นการใช้งาน Site Subscription System\n');

	try {
		// 1. สมัครแพ็คเกจ
		console.log('1️⃣ สมัครแพ็คเกจรายเดือน...');
		const subscription = await subscribeMonthlyPackage();

		// 2. ดึงรายการ subscription
		console.log('\n2️⃣ ดึงรายการ subscription...');
		await getMySubscriptions();

		// 3. จัดการ auto renewal
		console.log('\n3️⃣ จัดการ auto renewal...');
		await manageAutoRenewal();

		// 4. ต่ออายุแบบ manual
		console.log('\n4️⃣ ต่ออายุแบบ manual...');
		await manualRenewal();

		// 5. ดึงการแจ้งเตือน
		console.log('\n5️⃣ ดึงการแจ้งเตือน...');
		await getMyNotifications();

		console.log('\n✅ ทดสอบระบบสำเร็จทั้งหมด!');
	} catch (error) {
		console.error('\n❌ เกิดข้อผิดพลาด:', error.message);
	}
}

// 7. ตัวอย่าง API Usage
export const apiExamples = {
	// สร้าง subscription
	createSubscription: {
		method: 'POST',
		url: '/subscription/sites/site123/subscribe',
		headers: {
			Authorization: 'Bearer <user-token>',
			'Content-Type': 'application/json',
		},
		body: {
			packageType: 'monthly',
			autoRenew: true,
			paymentMethod: 'moneyPoint',
			discountCode: 'SAVE10',
		},
	},

	// ดึงรายการ subscription
	getSubscriptions: {
		method: 'GET',
		url: '/subscription/my-subscriptions?page=1&limit=10&status=active',
		headers: {
			Authorization: 'Bearer <user-token>',
		},
	},

	// เปิด/ปิด auto renew
	toggleAutoRenew: {
		method: 'PUT',
		url: '/subscription/sites/site123/subscriptions/sub789/auto-renew',
		headers: {
			Authorization: 'Bearer <user-token>',
			'Content-Type': 'application/json',
		},
		body: {
			autoRenew: true,
		},
	},

	// ยกเลิก subscription
	cancelSubscription: {
		method: 'PUT',
		url: '/subscription/sites/site123/subscriptions/sub789/cancel',
		headers: {
			Authorization: 'Bearer <user-token>',
		},
	},

	// ดึงการแจ้งเตือน
	getNotifications: {
		method: 'GET',
		url: '/subscription/sites/site123/notifications?unreadOnly=true',
		headers: {
			Authorization: 'Bearer <user-token>',
		},
	},
};

// 8. ตัวอย่าง Cron Job Usage
import {
	processAutoRenewals,
	sendExpiryWarnings,
	cleanupExpiredSubscriptions,
	runSubscriptionCronJobs,
} from '@/modules/subscription/subscription.cron';

export async function setupCronJobs() {
	// รัน cron jobs ทั้งหมดทุกวันเวลา 00:00
	setInterval(
		async () => {
			console.log('🕐 เริ่มรัน Subscription Cron Jobs...');

			try {
				const results = await runSubscriptionCronJobs();
				console.log('✅ Cron Jobs สำเร็จ:', results);
			} catch (error) {
				console.error('❌ Cron Jobs ล้มเหลว:', error.message);
			}
		},
		24 * 60 * 60 * 1000
	); // ทุก 24 ชั่วโมง

	// หรือรันแยกกัน
	// Auto renewals ทุกวันเวลา 00:00
	setInterval(processAutoRenewals, 24 * 60 * 60 * 1000);

	// Expiry warnings ทุกวันเวลา 09:00
	setInterval(() => sendExpiryWarnings(7), 24 * 60 * 60 * 1000);

	// Cleanup ทุกวันเวลา 02:00
	setInterval(cleanupExpiredSubscriptions, 24 * 60 * 60 * 1000);
}

// 9. ตัวอย่างการใช้งานใน Frontend
export const frontendExamples = {
	// React Component สำหรับแสดงรายการ subscription
	subscriptionList: `
    import { useState, useEffect } from 'react';
    
    function SubscriptionList() {
      const [subscriptions, setSubscriptions] = useState([]);
      const [loading, setLoading] = useState(true);
      
      useEffect(() => {
        fetchSubscriptions();
      }, []);
      
      const fetchSubscriptions = async () => {
        try {
          const response = await fetch('/api/subscription/my-subscriptions', {
            headers: { 'Authorization': 'Bearer ' + token }
          });
          const data = await response.json();
          setSubscriptions(data.data.subscriptions);
        } catch (error) {
          console.error('Error:', error);
        } finally {
          setLoading(false);
        }
      };
      
      const toggleAutoRenew = async (siteId, subscriptionId, autoRenew) => {
        try {
          await fetch(\`/api/subscription/sites/\${siteId}/subscriptions/\${subscriptionId}/auto-renew\`, {
            method: 'PUT',
            headers: {
              'Authorization': 'Bearer ' + token,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ autoRenew })
          });
          fetchSubscriptions(); // Refresh list
        } catch (error) {
          console.error('Error:', error);
        }
      };
      
      if (loading) return <div>Loading...</div>;
      
      return (
        <div>
          <h2>My Subscriptions</h2>
          {subscriptions.map(sub => (
            <div key={sub._id} className="subscription-card">
              <h3>{sub.siteId.name}</h3>
              <p>Package: {sub.packageType}</p>
              <p>Status: {sub.status}</p>
              <p>Next Renewal: {new Date(sub.nextRenewalDate).toLocaleDateString()}</p>
              <label>
                <input
                  type="checkbox"
                  checked={sub.autoRenew}
                  onChange={(e) => toggleAutoRenew(sub.siteId._id, sub._id, e.target.checked)}
                />
                Auto Renewal
              </label>
            </div>
          ))}
        </div>
      );
    }
  `,

	// Vue Component สำหรับสมัครแพ็คเกจ
	subscriptionForm: `
    <template>
      <div class="subscription-form">
        <h2>Subscribe to Package</h2>
        <form @submit.prevent="subscribe">
          <select v-model="selectedPackage" required>
            <option value="">Select Package</option>
            <option value="monthly">Monthly (99 points)</option>
            <option value="yearly">Yearly (999 points)</option>
            <option value="permanent">Permanent (9999 points)</option>
          </select>
          
          <input 
            v-model="discountCode" 
            placeholder="Discount Code (optional)"
          />
          
          <label>
            <input v-model="autoRenew" type="checkbox" />
            Enable Auto Renewal
          </label>
          
          <button type="submit" :disabled="loading">
            {{ loading ? 'Processing...' : 'Subscribe' }}
          </button>
        </form>
      </div>
    </template>
    
    <script>
    export default {
      data() {
        return {
          selectedPackage: '',
          discountCode: '',
          autoRenew: false,
          loading: false
        };
      },
      methods: {
        async subscribe() {
          this.loading = true;
          try {
            const response = await fetch(\`/api/subscription/sites/\${this.siteId}/subscribe\`, {
              method: 'POST',
              headers: {
                'Authorization': 'Bearer ' + this.token,
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                packageType: this.selectedPackage,
                autoRenew: this.autoRenew,
                discountCode: this.discountCode || undefined
              })
            });
            
            const data = await response.json();
            if (data.success) {
              alert('Subscription created successfully!');
              this.$emit('subscribed', data.data);
            } else {
              alert('Error: ' + data.message);
            }
          } catch (error) {
            alert('Error: ' + error.message);
          } finally {
            this.loading = false;
          }
        }
      }
    };
    </script>
  `,
};

console.log('📚 ตัวอย่างการใช้งาน Site Package Subscription System พร้อมแล้ว!');
