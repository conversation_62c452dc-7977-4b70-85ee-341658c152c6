#!/usr/bin/env node

/**
 * Script สำหรับลบ unused variables, functions, และ imports
 * ใช้ ESLint API เพื่อหา unused code และลบออกอัตโนมัติ
 */

import { ESLint } from 'eslint';
import fs from 'fs';
import path from 'path';

async function removeUnusedCode() {
  console.log('🔍 กำลังค้นหา unused code...');
  
  // สร้าง ESLint instance
  const eslint = new ESLint({
    fix: true,
    overrideConfigFile: 'eslint.config.js',
  });

  try {
    // ค้นหาไฟล์ TypeScript ทั้งหมด
    const results = await eslint.lintFiles(['src/**/*.ts']);
    
    let totalFixed = 0;
    let totalUnused = 0;

    for (const result of results) {
      if (result.output) {
        // เขียนไฟล์ที่แก้ไขแล้วกลับ
        fs.writeFileSync(result.filePath, result.output);
        console.log(`✅ แก้ไข: ${path.relative(process.cwd(), result.filePath)}`);
        totalFixed++;
      }

      // นับ unused issues
      const unusedIssues = result.messages.filter(msg => 
        msg.ruleId === '@typescript-eslint/no-unused-vars' ||
        msg.ruleId === '@typescript-eslint/no-unused-imports'
      );
      
      if (unusedIssues.length > 0) {
        totalUnused += unusedIssues.length;
        console.log(`⚠️  ${path.relative(process.cwd(), result.filePath)}: ${unusedIssues.length} unused items`);
      }
    }

    console.log('\n📊 สรุปผลลัพธ์:');
    console.log(`✅ แก้ไขไฟล์: ${totalFixed} ไฟล์`);
    console.log(`⚠️  Unused items ที่เหลือ: ${totalUnused} รายการ`);
    
    if (totalUnused > 0) {
      console.log('\n💡 หมายเหตุ: บาง unused items อาจต้องแก้ไขด้วยมือ');
    }

  } catch (error) {
    console.error('❌ เกิดข้อผิดพลาด:', error.message);
    process.exit(1);
  }
}

// เพิ่ม function สำหรับลบ unused imports เฉพาะ
async function removeUnusedImports() {
  console.log('🔍 กำลังลบ unused imports...');
  
  const eslint = new ESLint({
    fix: true,
    overrideConfigFile: 'eslint.config.js',
  });

  const results = await eslint.lintFiles(['src/**/*.ts']);
  
  for (const result of results) {
    if (result.output) {
      fs.writeFileSync(result.filePath, result.output);
      console.log(`✅ ลบ unused imports: ${path.relative(process.cwd(), result.filePath)}`);
    }
  }
}

// เพิ่ม function สำหรับสร้างรายงาน unused code
async function generateUnusedReport() {
  console.log('📋 กำลังสร้างรายงาน unused code...');
  
  const eslint = new ESLint({
    overrideConfigFile: 'eslint.config.js',
  });
  const results = await eslint.lintFiles(['src/**/*.ts']);
  
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      totalFiles: results.length,
      filesWithUnused: 0,
      totalUnusedItems: 0
    },
    details: []
  };

  for (const result of results) {
    const unusedIssues = result.messages.filter(msg => 
      msg.ruleId === '@typescript-eslint/no-unused-vars'
    );
    
    if (unusedIssues.length > 0) {
      report.summary.filesWithUnused++;
      report.summary.totalUnusedItems += unusedIssues.length;
      
      report.details.push({
        file: path.relative(process.cwd(), result.filePath),
        unusedCount: unusedIssues.length,
        items: unusedIssues.map(issue => ({
          line: issue.line,
          column: issue.column,
          message: issue.message,
          variable: issue.message.match(/'([^']+)'/)?.[1] || 'unknown'
        }))
      });
    }
  }

  // เขียนรายงานลงไฟล์
  fs.writeFileSync('unused-code-report.json', JSON.stringify(report, null, 2));
  console.log('📄 สร้างรายงานเสร็จแล้ว: unused-code-report.json');
  
  return report;
}

// Main function
async function main() {
  const command = process.argv[2];
  
  switch (command) {
    case 'fix':
      await removeUnusedCode();
      break;
    case 'imports':
      await removeUnusedImports();
      break;
    case 'report':
      await generateUnusedReport();
      break;
    default:
      console.log(`
🛠️  Remove Unused Code Script

การใช้งาน:
  node scripts/remove-unused.js fix      - แก้ไข unused code ทั้งหมด
  node scripts/remove-unused.js imports  - ลบ unused imports เท่านั้น  
  node scripts/remove-unused.js report   - สร้างรายงาน unused code

ตัวอย่าง:
  node scripts/remove-unused.js fix
  bun run scripts/remove-unused.js imports
      `);
  }
}

// Check if this is the main module
if (process.argv[1] === new URL(import.meta.url).pathname) {
  main().catch(console.error);
}

export {
  removeUnusedCode,
  removeUnusedImports,
  generateUnusedReport
};
