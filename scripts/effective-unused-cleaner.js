#!/usr/bin/env node

/**
 * Effective Unused Code Cleaner
 * ใช้ ESLint output เพื่อลบ unused code ได้จริง
 */

import { ESLint } from 'eslint';
import fs from 'fs';
import path from 'path';

class EffectiveUnusedCleaner {
  constructor() {
    this.removedCount = 0;
    this.processedFiles = 0;
    this.modifiedFiles = 0;
  }

  /**
   * รัน ESLint และได้ข้อมูล unused code
   */
  async getUnusedCodeInfo() {
    console.log('🔍 กำลังวิเคราะห์ unused code ด้วย ESLint...');
    
    const eslint = new ESLint({
      overrideConfigFile: 'eslint.config.js',
    });

    const results = await eslint.lintFiles(['src/**/*.ts']);
    const unusedItems = [];

    for (const result of results) {
      for (const message of result.messages) {
        if (message.message.includes('never used') || 
            message.message.includes('is defined but never used') ||
            message.message.includes('is assigned a value but never used')) {
          
          unusedItems.push({
            file: result.filePath,
            line: message.line,
            column: message.column,
            message: message.message,
            ruleId: message.ruleId,
            severity: message.severity
          });
        }
      }
    }

    return unusedItems;
  }

  /**
   * ลบ unused imports จากไฟล์
   */
  removeUnusedImportsFromFile(filePath, unusedItems) {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    let modified = false;
    let removedInThisFile = 0;

    // หา unused imports ในไฟล์นี้
    const fileUnusedItems = unusedItems.filter(item => 
      item.file === filePath && 
      item.message.includes('imported but never used')
    );

    if (fileUnusedItems.length === 0) {
      return { modified: false, removedCount: 0 };
    }

    // สร้าง set ของ unused import names
    const unusedImportNames = new Set();
    fileUnusedItems.forEach(item => {
      const match = item.message.match(/'([^']+)' is imported but never used/);
      if (match) {
        unusedImportNames.add(match[1]);
      }
    });

    // ประมวลผลแต่ละบรรทัด
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      
      // ตรวจสอบ import statements
      if (line.trim().startsWith('import ') && line.includes('from ')) {
        let newLine = line;
        let lineModified = false;

        // ลบ unused imports จาก named imports
        if (line.includes('{') && line.includes('}')) {
          const importMatch = line.match(/import\s*{\s*([^}]+)\s*}\s*from\s*(['"][^'"]+['"])/);
          if (importMatch) {
            const imports = importMatch[1].split(',').map(imp => imp.trim());
            const usedImports = imports.filter(imp => {
              const cleanImp = imp.replace(/\s+as\s+\w+/, '').trim();
              return !unusedImportNames.has(cleanImp);
            });

            if (usedImports.length === 0) {
              // ลบทั้งบรรทัด
              lines[i] = '';
              modified = true;
              lineModified = true;
              removedInThisFile++;
              console.log(`  ❌ ลบ import ทั้งหมด: ${importMatch[2]} ใน ${path.basename(filePath)}`);
            } else if (usedImports.length < imports.length) {
              // ลบบางตัว
              newLine = `import { ${usedImports.join(', ')} } from ${importMatch[2]};`;
              lines[i] = newLine;
              modified = true;
              lineModified = true;
              removedInThisFile++;
              console.log(`  🔧 ลบ unused imports บางตัวจาก ${importMatch[2]} ใน ${path.basename(filePath)}`);
            }
          }
        }

        // ลบ default imports ที่ไม่ใช้
        if (!lineModified) {
          const defaultImportMatch = line.match(/import\s+(\w+)\s+from\s*(['"][^'"]+['"])/);
          if (defaultImportMatch && unusedImportNames.has(defaultImportMatch[1])) {
            lines[i] = '';
            modified = true;
            removedInThisFile++;
            console.log(`  ❌ ลบ default import: ${defaultImportMatch[1]} จาก ${defaultImportMatch[2]} ใน ${path.basename(filePath)}`);
          }
        }
      }
    }

    // เขียนไฟล์กลับถ้ามีการเปลี่ยนแปลง
    if (modified) {
      // ลบบรรทัดว่างที่เกินไป
      const cleanedLines = [];
      let consecutiveEmptyLines = 0;
      
      for (const line of lines) {
        if (line.trim() === '') {
          consecutiveEmptyLines++;
          if (consecutiveEmptyLines <= 1) {
            cleanedLines.push(line);
          }
        } else {
          consecutiveEmptyLines = 0;
          cleanedLines.push(line);
        }
      }

      fs.writeFileSync(filePath, cleanedLines.join('\n'));
      this.modifiedFiles++;
    }

    return { modified, removedCount: removedInThisFile };
  }

  /**
   * ลบ unused variables จากไฟล์
   */
  removeUnusedVariablesFromFile(filePath, unusedItems) {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    let modified = false;
    let removedInThisFile = 0;

    // หา unused variables ในไฟล์นี้
    const fileUnusedItems = unusedItems.filter(item => 
      item.file === filePath && 
      (item.message.includes('is defined but never used') || 
       item.message.includes('is assigned a value but never used'))
    );

    if (fileUnusedItems.length === 0) {
      return { modified: false, removedCount: 0 };
    }

    // ลบ unused variables ที่ชัดเจน
    fileUnusedItems.forEach(item => {
      const lineIndex = item.line - 1;
      if (lineIndex >= 0 && lineIndex < lines.length) {
        const line = lines[lineIndex];
        
        // ลบ variables ที่ขึ้นต้นด้วย _ (convention สำหรับ unused)
        if (line.includes('const _') || line.includes('let _') || line.includes('var _')) {
          console.log(`  🗑️  ลบ unused variable: ${line.trim()} ใน ${path.basename(filePath)}`);
          lines[lineIndex] = '';
          modified = true;
          removedInThisFile++;
        }
        
        // ลบ unused parameters ใน function
        else if (line.includes('function') || line.includes('=>')) {
          // ไม่ลบ function parameters เพราะอาจทำให้ code เสีย
          // แต่สามารถเพิ่ม _ prefix ได้
          const match = item.message.match(/'([^']+)' is defined but never used/);
          if (match) {
            const unusedParam = match[1];
            if (!unusedParam.startsWith('_')) {
              const newLine = line.replace(new RegExp(`\\b${unusedParam}\\b`), `_${unusedParam}`);
              if (newLine !== line) {
                console.log(`  🔧 เพิ่ม _ prefix: ${unusedParam} -> _${unusedParam} ใน ${path.basename(filePath)}`);
                lines[lineIndex] = newLine;
                modified = true;
                removedInThisFile++;
              }
            }
          }
        }
      }
    });

    // เขียนไฟล์กลับถ้ามีการเปลี่ยนแปลง
    if (modified) {
      fs.writeFileSync(filePath, lines.join('\n'));
      this.modifiedFiles++;
    }

    return { modified, removedCount: removedInThisFile };
  }

  /**
   * ประมวลผลไฟล์ทั้งหมด
   */
  async processAllFiles() {
    console.log('🧹 Effective Unused Code Cleaner');
    console.log('================================\n');

    // ขั้นตอนที่ 1: รับข้อมูล unused code จาก ESLint
    const unusedItems = await this.getUnusedCodeInfo();
    
    if (unusedItems.length === 0) {
      console.log('🎉 ไม่พบ unused code ที่สามารถลบได้!');
      return;
    }

    console.log(`📋 พบ unused items: ${unusedItems.length} รายการ\n`);

    // จัดกลุ่มตามไฟล์
    const fileGroups = {};
    unusedItems.forEach(item => {
      if (!fileGroups[item.file]) {
        fileGroups[item.file] = [];
      }
      fileGroups[item.file].push(item);
    });

    console.log('🔧 กำลังลบ unused code...\n');

    // ขั้นตอนที่ 2: ประมวลผลแต่ละไฟล์
    for (const [filePath, items] of Object.entries(fileGroups)) {
      console.log(`📁 ประมวลผล: ${path.relative(process.cwd(), filePath)}`);
      
      // ลบ unused imports
      const importResult = this.removeUnusedImportsFromFile(filePath, items);
      this.removedCount += importResult.removedCount;

      // ลบ unused variables
      const variableResult = this.removeUnusedVariablesFromFile(filePath, items);
      this.removedCount += variableResult.removedCount;

      if (importResult.modified || variableResult.modified) {
        console.log(`✅ แก้ไข: ${path.relative(process.cwd(), filePath)}`);
      }

      this.processedFiles++;
      console.log('');
    }

    // สรุปผลลัพธ์
    console.log('📊 สรุปผลลัพธ์:');
    console.log(`📁 ประมวลผล: ${this.processedFiles} ไฟล์`);
    console.log(`✅ แก้ไข: ${this.modifiedFiles} ไฟล์`);
    console.log(`🗑️  ลบ unused items: ${this.removedCount} รายการ`);

    if (this.removedCount > 0) {
      console.log('\n💡 แนะนำ: ทดสอบระบบเพื่อให้แน่ใจว่าทุกอย่างยังทำงานได้ปกติ');
    }
  }

  /**
   * ลบเฉพาะ unused imports
   */
  async cleanImportsOnly() {
    console.log('🧹 ลบ Unused Imports เท่านั้น');
    console.log('=============================\n');

    const unusedItems = await this.getUnusedCodeInfo();
    const importItems = unusedItems.filter(item => 
      item.message.includes('imported but never used')
    );

    if (importItems.length === 0) {
      console.log('🎉 ไม่พบ unused imports!');
      return;
    }

    console.log(`📋 พบ unused imports: ${importItems.length} รายการ\n`);

    // จัดกลุ่มตามไฟล์
    const fileGroups = {};
    importItems.forEach(item => {
      if (!fileGroups[item.file]) {
        fileGroups[item.file] = [];
      }
      fileGroups[item.file].push(item);
    });

    console.log('🔧 กำลังลบ unused imports...\n');

    for (const [filePath, items] of Object.entries(fileGroups)) {
      console.log(`📁 ประมวลผล: ${path.relative(process.cwd(), filePath)}`);
      
      const result = this.removeUnusedImportsFromFile(filePath, items);
      this.removedCount += result.removedCount;

      if (result.modified) {
        console.log(`✅ แก้ไข: ${path.relative(process.cwd(), filePath)}`);
      }

      this.processedFiles++;
      console.log('');
    }

    console.log('📊 สรุปผลลัพธ์:');
    console.log(`📁 ประมวลผล: ${this.processedFiles} ไฟล์`);
    console.log(`✅ แก้ไข: ${this.modifiedFiles} ไฟล์`);
    console.log(`🗑️  ลบ unused imports: ${this.removedCount} รายการ`);
  }
}

// Main function
async function main() {
  const command = process.argv[2];
  const cleaner = new EffectiveUnusedCleaner();

  switch (command) {
    case 'all':
      await cleaner.processAllFiles();
      break;
    case 'imports':
      await cleaner.cleanImportsOnly();
      break;
    default:
      console.log(`
🧹 Effective Unused Code Cleaner

การใช้งาน:
  node scripts/effective-unused-cleaner.js all      - ลบ unused code ทั้งหมด
  node scripts/effective-unused-cleaner.js imports  - ลบ unused imports เท่านั้น

ตัวอย่าง:
  bun run clean:effective
  bun run clean:effective-imports
      `);
  }
}

// Check if this is the main module
if (process.argv[1] === new URL(import.meta.url).pathname) {
  main().catch(console.error);
}

export default EffectiveUnusedCleaner;
