#!/usr/bin/env node

/**
 * Cleanup Status Reporter
 * แสดงสถานะการทำความสะอาด unused code
 */

import { ESLint } from 'eslint';
import fs from 'fs';

class CleanupStatusReporter {
  constructor() {
    this.initialStats = {
      totalIssues: 1523,
      errors: 79,
      warnings: 1443
    };
  }

  /**
   * รับสถานะปัจจุบันจาก ESLint
   */
  async getCurrentStatus() {
    console.log('🔍 กำลังตรวจสอบสถานะปัจจุบัน...\n');

    const eslint = new ESLint({
      overrideConfigFile: 'eslint.config.js',
    });

    const results = await eslint.lintFiles(['src/**/*.ts']);
    
    let totalErrors = 0;
    let totalWarnings = 0;
    let unusedItems = 0;
    let filesWithIssues = 0;

    const issuesByType = {
      'unused-vars': 0,
      'unused-imports': 0,
      'unreachable-code': 0,
      'lexical-declaration': 0,
      'other': 0
    };

    for (const result of results) {
      if (result.messages.length > 0) {
        filesWithIssues++;
      }

      for (const message of result.messages) {
        if (message.severity === 2) {
          totalErrors++;
        } else {
          totalWarnings++;
        }

        // นับ unused items
        if (message.message.includes('never used') || 
            message.message.includes('is defined but never used') ||
            message.message.includes('is assigned a value but never used')) {
          unusedItems++;
          
          if (message.message.includes('imported but never used')) {
            issuesByType['unused-imports']++;
          } else {
            issuesByType['unused-vars']++;
          }
        }
        // นับ errors อื่นๆ
        else if (message.message.includes('Unreachable code')) {
          issuesByType['unreachable-code']++;
        }
        else if (message.message.includes('Unexpected lexical declaration')) {
          issuesByType['lexical-declaration']++;
        }
        else {
          issuesByType['other']++;
        }
      }
    }

    return {
      totalIssues: totalErrors + totalWarnings,
      errors: totalErrors,
      warnings: totalWarnings,
      unusedItems,
      filesWithIssues,
      totalFiles: results.length,
      issuesByType
    };
  }

  /**
   * คำนวณความคืบหน้า
   */
  calculateProgress(current) {
    const totalImprovement = this.initialStats.totalIssues - current.totalIssues;
    const errorImprovement = this.initialStats.errors - current.errors;
    const warningImprovement = this.initialStats.warnings - current.warnings;

    const totalProgress = (totalImprovement / this.initialStats.totalIssues * 100).toFixed(1);
    const errorProgress = (errorImprovement / this.initialStats.errors * 100).toFixed(1);
    const warningProgress = (warningImprovement / this.initialStats.warnings * 100).toFixed(1);

    return {
      totalImprovement,
      errorImprovement,
      warningImprovement,
      totalProgress,
      errorProgress,
      warningProgress
    };
  }

  /**
   * แสดงรายงานสถานะ
   */
  async showStatus() {
    console.log('📊 สถานะการทำความสะอาด Unused Code');
    console.log('=====================================\n');

    const current = await this.getCurrentStatus();
    const progress = this.calculateProgress(current);

    // สถานะปัจจุบัน
    console.log('🎯 สถานะปัจจุบัน:');
    console.log(`   📁 ไฟล์ทั้งหมด: ${current.totalFiles}`);
    console.log(`   ⚠️  ไฟล์ที่มี issues: ${current.filesWithIssues}`);
    console.log(`   🔴 Errors: ${current.errors}`);
    console.log(`   🟡 Warnings: ${current.warnings}`);
    console.log(`   📊 Issues รวม: ${current.totalIssues}`);
    console.log(`   🗑️  Unused items: ${current.unusedItems}\n`);

    // ความคืบหน้า
    console.log('📈 ความคืบหน้า (เทียบกับตอนเริ่มต้น):');
    console.log(`   🎉 Issues รวม: ลดลง ${progress.totalImprovement} รายการ (${progress.totalProgress}%)`);
    console.log(`   🔴 Errors: ลดลง ${progress.errorImprovement} รายการ (${progress.errorProgress}%)`);
    console.log(`   🟡 Warnings: ลดลง ${progress.warningImprovement} รายการ (${progress.warningProgress}%)\n`);

    // แยกตามประเภท
    console.log('📋 Issues แยกตามประเภท:');
    console.log(`   🗑️  Unused variables: ${current.issuesByType['unused-vars']}`);
    console.log(`   📦 Unused imports: ${current.issuesByType['unused-imports']}`);
    console.log(`   ⚠️  Unreachable code: ${current.issuesByType['unreachable-code']}`);
    console.log(`   🔧 Lexical declaration: ${current.issuesByType['lexical-declaration']}`);
    console.log(`   📝 อื่นๆ: ${current.issuesByType['other']}\n`);

    // คำแนะนำ
    this.showRecommendations(current);

    // บันทึกสถานะ
    this.saveStatusToFile(current, progress);
  }

  /**
   * แสดงคำแนะนำ
   */
  showRecommendations(current) {
    console.log('💡 คำแนะนำ:');

    if (current.unusedItems > 0) {
      console.log(`   🧹 ยังมี unused code ${current.unusedItems} รายการ ใช้:`);
      console.log('      bun run clean:effective');
    }

    if (current.issuesByType['unreachable-code'] > 0) {
      console.log(`   ⚠️  มี unreachable code ${current.issuesByType['unreachable-code']} รายการ ต้องแก้ไขด้วยมือ`);
    }

    if (current.issuesByType['lexical-declaration'] > 0) {
      console.log(`   🔧 มี lexical declaration issues ${current.issuesByType['lexical-declaration']} รายการ ต้องเพิ่ม {} ใน case blocks`);
    }

    if (current.totalIssues < 50) {
      console.log('   🎉 เกือบเสร็จแล้ว! Issues เหลือน้อยมาก');
    } else if (current.totalIssues < 100) {
      console.log('   👍 ทำได้ดีมาก! Issues เหลือไม่เยอะ');
    }

    console.log('');
  }

  /**
   * บันทึกสถานะลงไฟล์
   */
  saveStatusToFile(current, progress) {
    const statusData = {
      timestamp: new Date().toISOString(),
      initial: this.initialStats,
      current,
      progress,
      summary: {
        totalProgress: `${progress.totalProgress}%`,
        remainingIssues: current.totalIssues,
        canStillImprove: current.unusedItems > 0
      }
    };

    fs.writeFileSync('cleanup-status.json', JSON.stringify(statusData, null, 2));
    console.log('💾 บันทึกสถานะแล้ว: cleanup-status.json');
  }

  /**
   * แสดงสถานะแบบสั้น
   */
  async showQuickStatus() {
    const current = await this.getCurrentStatus();
    const progress = this.calculateProgress(current);

    console.log(`📊 สถานะ: ${current.totalIssues} issues (${current.errors} errors, ${current.warnings} warnings)`);
    console.log(`🗑️  Unused: ${current.unusedItems} รายการ`);
    console.log(`📈 ความคืบหน้า: ${progress.totalProgress}% (ลดลง ${progress.totalImprovement} issues)`);
  }
}

// Main function
async function main() {
  const command = process.argv[2];
  const reporter = new CleanupStatusReporter();

  switch (command) {
    case 'full':
      await reporter.showStatus();
      break;
    case 'quick':
    default:
      await reporter.showQuickStatus();
      break;
  }
}

// Check if this is the main module
if (process.argv[1] === new URL(import.meta.url).pathname) {
  main().catch(console.error);
}

export default CleanupStatusReporter;
