import js from '@eslint/js';
import ts from '@typescript-eslint/eslint-plugin';
import tsParser from '@typescript-eslint/parser';
import prettier from 'eslint-config-prettier';

/** @type {import('eslint').Linter.FlatConfig[]} */
export default [
	js.configs.recommended,
	{
		files: ['**/*.{js,ts}'],
		plugins: {
			'@typescript-eslint': ts
		},
		languageOptions: {
			parser: tsParser,
			parserOptions: {
				ecmaVersion: 2022,
				sourceType: 'module'
			},
			globals: {
				console: 'readonly',
				process: 'readonly',
				Buffer: 'readonly',
				__dirname: 'readonly',
				__filename: 'readonly',
				global: 'readonly',
				setTimeout: 'readonly',
				setInterval: 'readonly',
				clearTimeout: 'readonly',
				clearInterval: 'readonly'
			}
		},
		rules: {
			// TypeScript rules
			'@typescript-eslint/no-unused-vars': 'off',
			'@typescript-eslint/no-explicit-any': 'off',
			'@typescript-eslint/no-non-null-assertion': 'warn',

			// JavaScript rules
			'prefer-const': 'error',
			'no-unused-vars': 'off', // ใช้ TypeScript version แทน
			'no-undef': 'off', // TypeScript จัดการให้แล้ว
			'no-const-assign': 'error'
		}
	},
	// Core modules - strict rules
	{
		files: ['src/core/**/*.ts'],
		rules: {
			'@typescript-eslint/no-unused-vars': 'off',
			'@typescript-eslint/no-explicit-any': 'off',
			'prefer-const': 'error'
		}
	},
	// Modules - relaxed rules
	{
		files: ['src/modules/**/*.ts'],
		rules: {
			'@typescript-eslint/no-unused-vars': 'off',
			'@typescript-eslint/no-explicit-any': 'off'
		}
	},
	// Config files - very relaxed
	{
		files: ['src/core/config/**/*.ts'],
		rules: {
			'@typescript-eslint/no-explicit-any': 'off',
			'@typescript-eslint/no-non-null-assertion': 'off'
		}
	},
	// Test files - relaxed
	{
		files: ['src/**/*.test.ts', 'src/**/*.spec.ts'],
		rules: {
			'@typescript-eslint/no-explicit-any': 'off',
			'@typescript-eslint/no-unused-vars': 'off'
		}
	},
	prettier,
	{
		ignores: [
			'node_modules/',
			'build/',
			'dist/',
			'coverage/',
			'logs/',
			'public/uploads/',
			'dashboard-sveltekit/',
			'*.config.js',
			'bun.lockb'
		]
	}
];
