{"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "eslint.validate": ["javascript", "typescript"], "[typescript]": {"editor.defaultFormatter": "rvest.vs-code-prettier-eslint"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[svelte]": {"editor.defaultFormatter": "rvest.vs-code-prettier-eslint"}}